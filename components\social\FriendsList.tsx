import { useQuery } from '@apollo/client';
import { router } from 'expo-router';
import React from 'react';
import { ActivityIndicator, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { GET_USER_FRIENDS } from '../../lib/graphql-operations';
import { formatRelativeTime } from '../../utils/date';
import { Avatar } from '../ui/Avatar';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface FriendsListProps {
  userId: string;
  limit?: number;
  showHeader?: boolean;
  onViewAllPress?: () => void;
}

export function FriendsList({ userId, limit = 10, showHeader = true, onViewAllPress }: FriendsListProps) {
  const theme = useTheme();

  // Query user's friends
  const { data, loading, error, refetch } = useQuery(GET_USER_FRIENDS, {
    variables: {
      userId,
      limit,
      skip: 0,
    },
    skip: !userId,
  });

  // Handle friend item press
  const handleFriendPress = (friendId: string) => {
    router.push(`/profile/${friendId}`);
  };

  // Handle view all press
  const handleViewAllPress = () => {
    if (onViewAllPress) {
      onViewAllPress();
    } else {
      router.push(`/profile/${userId}/friends`);
    }
  };

  // Render friend item
  const renderFriendItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.friendItem}
      onPress={() => handleFriendPress(item.id)}
    >
      <Avatar
        source={item.userProfile?.avatar}
        name={item.userProfile ? 
          `${item.userProfile.firstName || ''} ${item.userProfile.lastName || ''}`.trim() :
          item.emails?.[0]?.address?.split('@')[0]
        }
        online={item.isOnline}
        size="medium"
      />
      <ThemedView style={styles.friendInfo}>
        <ThemedText variant="subtitle">{item.userProfile ? 
          `${item.userProfile.firstName || ''} ${item.userProfile.lastName || ''}`.trim() :
          item.emails?.[0]?.address?.split('@')[0]
        }</ThemedText>
        {item.lastOnline && (
          <ThemedText variant="caption">
            {item.isOnline ? 'Online now' : `Last seen ${formatRelativeTime(item.lastOnline)}`}
          </ThemedText>
        )}
      </ThemedView>
    </TouchableOpacity>
  );

  // Render empty state
  const renderEmptyState = () => (
    <ThemedView style={styles.emptyState}>
      <ThemedText>No friends found</ThemedText>
    </ThemedView>
  );

  // Get friends from data
  const friends = data?.user?.friends || [];

  return (
    <ThemedView style={styles.container}>
      {showHeader && (
        <ThemedView style={styles.header}>
          <ThemedText variant="title">Friends</ThemedText>
          {friends.length > 0 && (
            <TouchableOpacity onPress={handleViewAllPress}>
              <ThemedText style={{ color: theme.colors.primary }}>View All</ThemedText>
            </TouchableOpacity>
          )}
        </ThemedView>
      )}

      {loading ? (
        <ActivityIndicator size="large" color={theme.colors.primary} style={styles.loader} />
      ) : error ? (
        <ThemedText style={styles.errorText}>Error loading friends: {error.message}</ThemedText>
      ) : (
        <FlatList
          data={friends}
          keyExtractor={(item) => item.id}
          renderItem={renderFriendItem}
          ListEmptyComponent={renderEmptyState}
                    keyboardShouldPersistTaps="handled"
          scrollEnabled={true}

          contentContainerStyle={styles.listContent}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: 16,
  },
  friendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  friendInfo: {
    marginLeft: 12,
    flex: 1,
  },
  loader: {
    marginTop: 20,
  },
  errorText: {
    padding: 16,
    color: 'red',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
});
