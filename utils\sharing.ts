import { Share, Platform } from 'react-native';
import * as Linking from 'expo-linking';

/**
 * Share content using the native sharing capabilities
 * @param title The title of the content to share
 * @param message The message to share
 * @param url The URL to share (optional)
 * @returns A promise that resolves when the share is complete
 */
export async function shareContent(
  title: string,
  message: string,
  url?: string
): Promise<void> {
  try {
    const shareOptions = {
      title,
      message: message + (url ? `\n\n${url}` : ''),
      url: url, // Only used on iOS
    };

    await Share.share(shareOptions);
  } catch (error) {
    console.error('Error sharing content:', error);
  }
}

/**
 * Generate a shareable URL for content
 * @param type The type of content (post, place, happening, offer)
 * @param id The ID of the content
 * @returns A shareable URL
 */
export function getShareableUrl(
  type: 'post' | 'place' | 'happening' | 'offer',
  id: string
): string {
  // Get the base URL for the app
  const baseUrl = Linking.createURL('');
  
  // Create a deep link URL
  return `${baseUrl}/${type}/${id}`;
}

/**
 * Open directions to a location in the native maps app
 * @param name The name of the location
 * @param latitude The latitude of the location
 * @param longitude The longitude of the location
 */
export function openDirections(
  name: string,
  latitude: number,
  longitude: number
): void {
  try {
    const label = encodeURIComponent(name);
    const url = Platform.select({
      ios: `maps:0,0?q=${label}@${latitude},${longitude}`,
      android: `geo:0,0?q=${latitude},${longitude}(${label})`,
      web: `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`
    });

    if (url) {
      Linking.openURL(url);
    }
  } catch (error) {
    console.error('Error opening directions:', error);
  }
}

/**
 * Open a phone number in the native phone app
 * @param phoneNumber The phone number to call
 */
export function callPhoneNumber(phoneNumber: string): void {
  try {
    // Remove any non-numeric characters
    const cleanedNumber = phoneNumber.replace(/\D/g, '');
    const url = `tel:${cleanedNumber}`;
    Linking.openURL(url);
  } catch (error) {
    console.error('Error calling phone number:', error);
  }
}

/**
 * Open a website in the browser
 * @param url The URL to open
 */
export function openWebsite(url: string): void {
  try {
    // Add https:// if not present
    let fullUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      fullUrl = `https://${url}`;
    }
    Linking.openURL(fullUrl);
  } catch (error) {
    console.error('Error opening website:', error);
  }
}
