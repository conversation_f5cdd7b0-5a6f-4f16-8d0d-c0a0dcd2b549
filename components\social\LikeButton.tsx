import { useMutation } from '@apollo/client';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { LIKE_CONTENT, UNLIKE_CONTENT } from '../../lib/graphql-operations';
import { Button } from '../ui/Button';

interface LikeButtonProps {
  objectId: string;
  objectType: 'post' | 'place' | 'happening' | 'offer';
  isLiked: boolean;
  likeCount: number;
  size?: 'small' | 'medium' | 'large';
  style?: any;
}

export function LikeButton({ 
  objectId, 
  objectType, 
  isLiked, 
  likeCount, 
  size = 'small',
  style
}: LikeButtonProps) {
  const theme = useTheme();
  const [likeContent, { loading: likeLoading }] = useMutation(LIKE_CONTENT);
  const [unlikeContent, { loading: unlikeLoading }] = useMutation(UNLIKE_CONTENT);

  const handleToggleLike = async () => {
    const mutation = isLiked ? unlikeContent : likeContent;
    
    await mutation({
      variables: {
        input: {
          objectId,
          objectType,
        },
      },
      optimisticResponse: {
        __typename: 'Mutation',
        [isLiked ? 'unlikeContent' : 'likeContent']: {
          __typename: 'LikeResponse',
          id: objectId,
          likes: isLiked ? likeCount - 1 : likeCount + 1,
          isLiked: !isLiked,
        },
      },
      update: (cache, { data }) => {
        // Update the cache with the new like status
        const updatedData = data?.[isLiked ? 'unlikeContent' : 'likeContent'];
        if (updatedData) {
          // Update the cache for the specific object
          cache.modify({
            id: cache.identify({ __typename: getTypeName(objectType), id: objectId }),
            fields: {
              likes: () => updatedData.likes,
              isLiked: () => updatedData.isLiked,
            },
          });
        }
      },
    });
  };

  // Get the typename based on objectType
  const getTypeName = (type: string): string => {
    switch (type) {
      case 'post':
        return 'Post';
      case 'place':
        return 'Place';
      case 'happening':
        return 'Happening';
      case 'offer':
        return 'Offer';
      default:
        return 'Post';
    }
  };

  // Render a custom button with heart icon
  return (
    <View style={[styles.container, style]}>
      <Button
        title={`${isLiked ? 'Unlike' : 'Like'} (${likeCount})`}
        variant={isLiked ? 'primary' : 'outline'}
        size={size}
        onPress={handleToggleLike}
        loading={likeLoading || unlikeLoading}
        style={styles.button}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
        pointerEvents: 'auto'

  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
