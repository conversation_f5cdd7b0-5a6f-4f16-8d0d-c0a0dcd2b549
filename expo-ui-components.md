# Town App - UI Components and Screens for Expo

## Overview

This document details the UI components and screens needed for the Expo version of the Town App. It covers the main screens, reusable components, and navigation structure.

## Navigation Structure

The app will use Expo Router for navigation, with a structure similar to the following:

```
app/
├── (auth)/                # Authentication stack
│   ├── login.tsx          # Login screen
│   ├── signup.tsx         # Signup screen
│   ├── reset-password.tsx # Password reset screen
│   └── verify-email.tsx   # Email verification screen
├── (tabs)/                # Main tab navigation
│   ├── index.tsx          # Home tab (feed)
│   ├── map.tsx            # Map tab
│   ├── messages.tsx       # Messages tab
│   ├── profile.tsx        # Profile tab
│   └── _layout.tsx        # Tab layout configuration
├── zone/                  # Zone screens
│   ├── [id]/              # Zone detail screens
│   │   ├── index.tsx      # Zone detail
│   │   ├── members.tsx    # Zone members
│   │   └── settings.tsx   # Zone settings
│   └── new.tsx            # Create new zone
├── post/                  # Post screens
│   ├── [id].tsx           # Post detail
│   └── new.tsx            # Create new post
├── place/                 # Place screens
│   ├── [id]/              # Place detail screens
│   │   ├── index.tsx      # Place detail
│   │   └── checkin.tsx    # Place check-in
│   └── new.tsx            # Create new place
├── happening/             # Happening screens
│   ├── [id].tsx           # Happening detail
│   └── new.tsx            # Create new happening
├── offer/                 # Offer screens
│   ├── [id].tsx           # Offer detail
│   └── new.tsx            # Create new offer
├── campaign/              # Campaign screens
│   ├── [id]/              # Campaign detail screens
│   │   ├── index.tsx      # Campaign detail
│   │   └── challenges.tsx # Campaign challenges
│   └── new.tsx            # Create new campaign
├── challenge/             # Challenge screens
│   ├── [id].tsx           # Challenge detail
│   └── new.tsx            # Create new challenge
├── user/                  # User screens
│   └── [id].tsx           # User profile
├── messages/              # Message screens
│   └── [id].tsx           # Conversation detail
├── _layout.tsx            # Root layout
└── index.tsx              # Entry point (redirects to tabs)
```

## Core UI Components

### Common Components

#### ThemedView

A wrapper around React Native's View with theming support.

```tsx
import { View, ViewProps } from 'react-native';
import { useTheme } from '@/hooks/useTheme';

interface ThemedViewProps extends ViewProps {
  variant?: 'primary' | 'secondary' | 'card';
}

export function ThemedView({ style, variant = 'primary', ...props }: ThemedViewProps) {
  const theme = useTheme();

  const variantStyles = {
    primary: {
      backgroundColor: theme.colors.background,
    },
    secondary: {
      backgroundColor: theme.colors.backgroundSecondary,
    },
    card: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.medium,
      padding: theme.spacing.medium,
      ...theme.shadows.medium,
    },
  };

  return (
    <View
      style={[variantStyles[variant], style]}
      {...props}
    />
  );
}
```

#### ThemedText

A wrapper around React Native's Text with theming support.

```tsx
import { Text, TextProps } from 'react-native';
import { useTheme } from '@/hooks/useTheme';

interface ThemedTextProps extends TextProps {
  variant?: 'title' | 'subtitle' | 'body' | 'caption' | 'button';
}

export function ThemedText({ style, variant = 'body', ...props }: ThemedTextProps) {
  const theme = useTheme();

  const variantStyles = {
    title: {
      fontSize: theme.typography.sizes.xl,
      fontWeight: theme.typography.weights.bold,
      color: theme.colors.text,
    },
    subtitle: {
      fontSize: theme.typography.sizes.large,
      fontWeight: theme.typography.weights.semibold,
      color: theme.colors.text,
    },
    body: {
      fontSize: theme.typography.sizes.medium,
      color: theme.colors.text,
    },
    caption: {
      fontSize: theme.typography.sizes.small,
      color: theme.colors.textSecondary,
    },
    button: {
      fontSize: theme.typography.sizes.medium,
      fontWeight: theme.typography.weights.semibold,
      color: theme.colors.primary,
    },
  };

  return (
    <Text
      style={[variantStyles[variant], style]}
      {...props}
    />
  );
}
```

#### Button

A customizable button component.

```tsx
import { TouchableOpacity, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { useTheme } from '@/hooks/useTheme';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
}

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
}: ButtonProps) {
  const theme = useTheme();

  const variantStyles = {
    primary: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
      color: theme.colors.white,
    },
    secondary: {
      backgroundColor: theme.colors.secondary,
      borderColor: theme.colors.secondary,
      color: theme.colors.white,
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: theme.colors.primary,
      color: theme.colors.primary,
    },
    text: {
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      color: theme.colors.primary,
    },
  };

  const sizeStyles = {
    small: {
      paddingVertical: theme.spacing.small,
      paddingHorizontal: theme.spacing.medium,
      fontSize: theme.typography.sizes.small,
    },
    medium: {
      paddingVertical: theme.spacing.medium,
      paddingHorizontal: theme.spacing.large,
      fontSize: theme.typography.sizes.medium,
    },
    large: {
      paddingVertical: theme.spacing.large,
      paddingHorizontal: theme.spacing.xl,
      fontSize: theme.typography.sizes.large,
    },
  };

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: variantStyles[variant].backgroundColor,
      borderColor: variantStyles[variant].borderColor,
      borderWidth: variant === 'outline' ? 1 : 0,
      paddingVertical: sizeStyles[size].paddingVertical,
      paddingHorizontal: sizeStyles[size].paddingHorizontal,
      opacity: disabled ? 0.6 : 1,
      width: fullWidth ? '100%' : 'auto',
    },
  ];

  const textStyle = [
    styles.text,
    {
      color: variantStyles[variant].color,
      fontSize: sizeStyles[size].fontSize,
    },
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator color={variantStyles[variant].color} />
      ) : (
        <Text style={textStyle}>{title}</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600',
  },
});
```

#### Card

A card component for displaying content.

```tsx
import { StyleSheet, ViewProps } from 'react-native';
import { ThemedView } from './ThemedView';

interface CardProps extends ViewProps {
  elevation?: 'none' | 'small' | 'medium' | 'large';
}

export function Card({ style, elevation = 'medium', ...props }: CardProps) {
  const theme = useTheme();

  const elevationStyles = {
    none: {},
    small: theme.shadows.small,
    medium: theme.shadows.medium,
    large: theme.shadows.large,
  };

  return (
    <ThemedView
      variant="card"
      style={[
        styles.card,
        elevationStyles[elevation],
        style,
      ]}
      {...props}
    />
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 8,
    overflow: 'hidden',
  },
});
```

#### Avatar

A component for displaying user avatars.

```tsx
import { Image, StyleSheet, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { useTheme } from '@/hooks/useTheme';

interface AvatarProps {
  source?: string;
  size?: 'small' | 'medium' | 'large';
  name?: string;
  online?: boolean;
}

export function Avatar({ source, size = 'medium', name, online }: AvatarProps) {
  const theme = useTheme();

  const sizeValues = {
    small: 32,
    medium: 48,
    large: 64,
  };

  const sizeValue = sizeValues[size];
  const initials = name
    ? name
        .split(' ')
        .map(part => part[0])
        .join('')
        .toUpperCase()
        .substring(0, 2)
    : '';

  return (
    <View style={styles.container}>
      {source ? (
        <Image
          source={{ uri: source }}
          style={[
            styles.image,
            { width: sizeValue, height: sizeValue, borderRadius: sizeValue / 2 },
          ]}
        />
      ) : (
        <View
          style={[
            styles.placeholder,
            {
              width: sizeValue,
              height: sizeValue,
              borderRadius: sizeValue / 2,
              backgroundColor: theme.colors.primary,
            },
          ]}
        >
          <ThemedText
            style={[
              styles.initials,
              { fontSize: sizeValue * 0.4 },
            ]}
          >
            {initials}
          </ThemedText>
        </View>
      )}

      {online && (
        <View
          style={[
            styles.onlineIndicator,
            {
              width: sizeValue * 0.25,
              height: sizeValue * 0.25,
              borderRadius: sizeValue * 0.125,
              right: 0,
              bottom: 0,
            },
          ]}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    backgroundColor: '#E1E1E1',
  },
  placeholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  initials: {
    color: 'white',
    fontWeight: 'bold',
  },
  onlineIndicator: {
    position: 'absolute',
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: 'white',
  },
});
```

## Main Screens

### Authentication Screens

#### Login Screen

```tsx
import { useState } from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/Button';
import { TextInput } from '@/components/TextInput';
import { useAuth } from '@/hooks/useAuth';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { login, loading, error } = useAuth();

  const handleLogin = async () => {
    const success = await login(email, password);
    if (success) {
      router.replace('/(tabs)');
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ThemedView style={styles.content}>
        <ThemedText variant="title" style={styles.title}>Welcome Back</ThemedText>

        {error && (
          <ThemedText style={styles.error}>{error}</ThemedText>
        )}

        <TextInput
          label="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          style={styles.input}
        />

        <TextInput
          label="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          style={styles.input}
        />

        <Button
          title="Log In"
          onPress={handleLogin}
          loading={loading}
          fullWidth
          style={styles.button}
        />

        <Button
          title="Forgot Password?"
          onPress={() => router.push('/reset-password')}
          variant="text"
          style={styles.forgotPassword}
        />

        <ThemedView style={styles.signupContainer}>
          <ThemedText>Don't have an account? </ThemedText>
          <Button
            title="Sign Up"
            onPress={() => router.push('/signup')}
            variant="text"
          />
        </ThemedView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    marginBottom: 24,
    textAlign: 'center',
  },
  input: {
    marginBottom: 16,
  },
  button: {
    marginTop: 8,
  },
  forgotPassword: {
    marginTop: 16,
    alignSelf: 'center',
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  error: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center',
  },
});
```

## Implementation Status

The UI components and screens have been fully implemented in the Expo application:

### Completed

1. ✅ **Theme Provider**
   - Implemented ThemeContext with light and dark mode support
   - Created useTheme and useThemeControls hooks
   - Set up automatic theme switching based on system preferences

2. ✅ **Basic UI Components**
   - ThemedView for themed container components
   - ThemedText for themed text components
   - Button with multiple variants and states
   - TextInput with label and error handling
   - Card for content containers
   - Avatar for user profile images

3. ✅ **Domain-Specific Components**
   - PostCard for displaying posts
   - PlaceCard for displaying places
   - HappeningCard for displaying events
   - OfferCard for displaying offers

4. ✅ **Authentication Screens**
   - Login screen
   - Signup screen
   - Password reset screen

5. ✅ **Main Tab Navigation**
   - Home tab with posts feed
   - Map tab with interactive map
   - Messages tab with conversations list
   - Profile tab with user details

6. ✅ **Detail Screens**
   - Post detail screen
   - Place detail screen
   - Zone detail screen
   - Conversation screen
   - Profile edit screen
   - Settings screen

### Next Steps

1. **Component Testing**
   - Write unit tests for components
   - Test across different devices and screen sizes

2. **Performance Optimization**
   - Memoize components where appropriate
   - Optimize rendering performance

3. **Accessibility Improvements**
   - Add more comprehensive accessibility labels
   - Ensure proper focus management
   - Support screen readers fully
