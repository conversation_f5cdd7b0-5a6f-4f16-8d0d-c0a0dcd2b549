import { ApolloError, useSubscription } from '@apollo/client';
import { useNetInfo } from '@react-native-community/netinfo';
import { DocumentNode } from 'graphql';
import { useCallback, useEffect, useRef, useState } from 'react';

const RECONNECT_DELAY = 2000; // 2 seconds
const MAX_RECONNECT_ATTEMPTS = 5;

/**
 * Custom hook for handling GraphQL subscriptions with network status awareness and reconnection logic
 * @param subscription The GraphQL subscription document
 * @param variables The variables for the subscription
 * @param onData Callback function to handle new data
 * @returns An object containing the subscription data, loading state, error, and active status
 */
export function useReliableSubscription<TData = any, TVariables = any>(
  subscription: DocumentNode,
  variables?: TVariables,
  onData?: (data: TData) => void
) {
  const [isActive, setIsActive] = useState(true);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [reconnecting, setReconnecting] = useState(false);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const netInfo = useNetInfo();

  // Clear any existing reconnect timeout when component unmounts
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  // Handle reconnection logic
  const handleReconnect = useCallback(() => {
    if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
      console.warn('Max reconnection attempts reached for subscription');
      setReconnecting(false);
      return;
    }

    setReconnecting(true);

    // Clear any existing timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    // Set a timeout to attempt reconnection
    reconnectTimeoutRef.current = setTimeout(() => {
      setIsActive(true);
      setReconnectAttempts((prev) => prev + 1);
      setReconnecting(false);
    }, RECONNECT_DELAY * Math.pow(1.5, reconnectAttempts)); // Exponential backoff
  }, [reconnectAttempts]);

  // Handle network status changes
  useEffect(() => {
    if (netInfo.isConnected === false) {
      // Network is offline, pause subscription
      setIsActive(false);
    } else if (netInfo.isConnected === true) {
      // Network is back online, resume subscription
      setIsActive(true);
      // Reset reconnect attempts when network is restored
      setReconnectAttempts(0);
    }
  }, [netInfo.isConnected]);

  // Handle subscription errors
  const handleError = useCallback((error: ApolloError) => {
    console.error('Subscription error:', error.message);

    // Check if the error is related to connection issues
    const isConnectionError = error.message.includes('connection') ||
                             error.message.includes('network') ||
                             error.message.includes('socket');

    if (isConnectionError && !reconnecting) {
      setIsActive(false);
      handleReconnect();
    }
  }, [handleReconnect, reconnecting]);

  // Use Apollo's useSubscription hook with our enhanced logic
  const { data, loading, error } = useSubscription(subscription, {
    variables,
    skip: !isActive,
    onData: ({ data }) => {
      if (data.data && onData) {
        // Reset reconnect attempts on successful data
        setReconnectAttempts(0);
        onData(data.data);
      }
    },
    onError: handleError,
  });

  // If we get an error, attempt to reconnect
  useEffect(() => {
    if (error && !reconnecting) {
      handleReconnect();
    }
  }, [error, handleReconnect, reconnecting]);

  return {
    data,
    loading,
    error,
    isActive,
    reconnecting,
    reconnectAttempts,
  };
}

export default useReliableSubscription;
