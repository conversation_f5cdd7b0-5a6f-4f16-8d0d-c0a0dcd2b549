# Town App - NextJS to Expo Migration

## Overview

This document serves as the central reference for the Town App migration from NextJS to Expo. It provides links to all relevant documentation and outlines the migration process.

## Migration Documentation

### Status and Planning

- [Migration Status](./expo-migration-status.md) - Current status of the migration project
- [Migration Summary](./expo-migration-summary.md) - High-level summary of the migration project

### Technical Implementation

- [Authentication and State Management](./expo-auth-state-management.md) - Authentication system and state management approach
- [UI Components](./expo-ui-components.md) - UI components and screens implementation
- [GraphQL Integration](./expo-graphql-integration.md) - GraphQL integration with Apollo Client

### Testing and Deployment

- [Testing Strategy](./expo-testing-strategy.md) - Testing approach for the Expo application
- [Deployment Strategy](./expo-deployment-strategy.md) - Deployment process for iOS, Android, and web

## Migration Process

The migration from NextJS to Expo follows these key steps:

1. **Project Setup**
   - Initialize Expo project with TypeScript
   - Set up Expo Router for navigation
   - Configure environment variables
   - Set up Apollo Client for GraphQL integration

2. **Core Architecture**
   - Implement context providers (Auth, Theme, Zone)
   - Create custom hooks (useLocation, useForm, useImageUpload, useInfiniteScroll)
   - Set up utility functions for dates and strings
   - Configure app.json for Expo settings

3. **UI Components**
   - Create base UI components (ThemedView, ThemedText, Button, TextInput, Card, Avatar)
   - Implement domain-specific components (PostCard, PlaceCard, HappeningCard, OfferCard)
   - Set up theming system with light and dark mode support

4. **Authentication**
   - Implement login screen
   - Implement signup screen
   - Implement password reset screen
   - Set up secure token storage with Expo SecureStore

5. **Main Screens**
   - Implement tab navigation (Home, Map, Messages, Profile)
   - Create home screen with posts list
   - Implement map screen with React Native Maps
   - Create messages screen with conversations list
   - Implement profile screen with user details

6. **Detail Screens**
   - Create post detail screen
   - Implement place detail screen
   - Create zone detail screen
   - Implement conversation screen
   - Create profile edit screen
   - Implement settings screen

7. **Data Integration**
   - Connect to the GraphQL API for real data
   - Implement data fetching and caching strategies
   - Set up error handling and loading states

8. **Testing**
   - Test on different devices and platforms
   - Identify and fix platform-specific issues
   - Write automated tests

9. **Optimization**
   - Improve performance for large lists
   - Optimize image loading and caching
   - Reduce bundle size

10. **Deployment**
    - Configure EAS Build
    - Prepare app store assets
    - Set up CI/CD pipeline

## Current Status

The migration project has made significant progress, with most of the core functionality already implemented. Here's a summary of the current status:

### Completed (✅)

- Project Setup
- Core Architecture
- UI Components
- Authentication
- Main Screens
- Detail Screens
- Map and Location

### In Progress (🔄)

- Data Integration
- Testing

### Pending (⏳)

- Advanced Features
- Performance Optimization
- Deployment

## Next Steps

1. **Complete Data Integration**
   - Finalize GraphQL queries and mutations
   - Implement proper error handling
   - Set up optimistic UI updates

2. **Testing and Debugging**
   - Test on multiple devices and platforms
   - Fix any platform-specific issues
   - Ensure consistent behavior across devices

3. **Optimization**
   - Improve performance for large lists
   - Optimize image loading and caching
   - Reduce bundle size

4. **Deployment Preparation**
   - Configure EAS Build
   - Prepare app store assets
   - Set up CI/CD pipeline

## Development Guidelines

### Code Organization

- **app/** - Expo Router screens and navigation
- **components/** - Reusable UI components
- **contexts/** - Context providers for global state
- **hooks/** - Custom React hooks
- **utils/** - Utility functions
- **styles/** - Global styles and theme
- **types/** - TypeScript type definitions

### Naming Conventions

- **Files**: Use PascalCase for components, camelCase for utilities
- **Components**: Use PascalCase (e.g., `Button.tsx`)
- **Hooks**: Use camelCase with 'use' prefix (e.g., `useAuth.ts`)
- **Contexts**: Use PascalCase with 'Context' suffix (e.g., `AuthContext.tsx`)

### Coding Standards

- Use TypeScript for type safety
- Follow ESLint and Prettier configurations
- Write JSDoc comments for functions and components
- Use functional components with hooks
- Implement proper error handling
- Write tests for components and hooks

## Resources

### Expo Documentation

- [Expo Documentation](https://docs.expo.dev/)
- [Expo Router](https://docs.expo.dev/router/introduction/)
- [Expo EAS Build](https://docs.expo.dev/build/introduction/)

### React Native

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [React Native Maps](https://github.com/react-native-maps/react-native-maps)

### Apollo Client

- [Apollo Client Documentation](https://www.apollographql.com/docs/react/)
- [Apollo Client with React Native](https://www.apollographql.com/docs/react/integrations/react-native/)

### Testing

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Native Testing Library](https://callstack.github.io/react-native-testing-library/)
- [Detox](https://github.com/wix/Detox)
