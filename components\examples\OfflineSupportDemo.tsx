import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { gql } from '@apollo/client';
import { useTheme } from '../../contexts/ThemeContext';
import { 
  useOfflineSupport, 
  useNetworkStatus, 
  useMutationQueue, 
  useOfflineMutation 
} from '../../hooks/useOfflineSupport';
import { EnhancedNetworkStatus, CompactNetworkStatus, FloatingNetworkStatus } from '../common/EnhancedNetworkStatus';

// Example mutation for demonstration
const EXAMPLE_MUTATION = gql`
  mutation ExampleMutation($input: String!) {
    exampleAction(input: $input) {
      id
      result
    }
  }
`;

/**
 * Demo component showing offline support features
 * This demonstrates how to use the new offline capabilities
 */
export function OfflineSupportDemo() {
  const { theme } = useTheme();
  const { networkStatus, isOnline, queueMutation, syncMutations, clearQueue } = useOfflineSupport();
  const { mutationQueue, queueLength } = useMutationQueue();
  const [executeOfflineMutation] = useOfflineMutation(EXAMPLE_MUTATION, {
    onCompleted: (data) => {
      console.log('Mutation completed:', data);
    },
    onError: (error) => {
      console.error('Mutation error:', error);
    },
  });

  const handleQueueMutation = async () => {
    try {
      const result = await executeOfflineMutation({
        input: `Test mutation at ${new Date().toISOString()}`
      });
      console.log('Mutation result:', result);
    } catch (error) {
      console.error('Failed to execute mutation:', error);
    }
  };

  const handleManualSync = async () => {
    try {
      await syncMutations();
      console.log('Manual sync completed');
    } catch (error) {
      console.error('Manual sync failed:', error);
    }
  };

  const handleClearQueue = async () => {
    try {
      await clearQueue();
      console.log('Queue cleared');
    } catch (error) {
      console.error('Failed to clear queue:', error);
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.colors.onBackground }]}>
        Offline Support Demo
      </Text>

      {/* Network Status Display */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
          Network Status
        </Text>
        <EnhancedNetworkStatus />
        
        <View style={styles.statusRow}>
          <Text style={[styles.statusLabel, { color: theme.colors.onBackground }]}>
            Connection:
          </Text>
          <Text style={[
            styles.statusValue, 
            { color: isOnline ? theme.colors.success || theme.colors.primary : theme.colors.error }
          ]}>
            {isOnline ? 'Online' : 'Offline'}
          </Text>
        </View>

        <View style={styles.statusRow}>
          <Text style={[styles.statusLabel, { color: theme.colors.onBackground }]}>
            Type:
          </Text>
          <Text style={[styles.statusValue, { color: theme.colors.onBackground }]}>
            {networkStatus.type}
          </Text>
        </View>

        <View style={styles.statusRow}>
          <Text style={[styles.statusLabel, { color: theme.colors.onBackground }]}>
            Queued Mutations:
          </Text>
          <Text style={[styles.statusValue, { color: theme.colors.onBackground }]}>
            {queueLength}
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
          Actions
        </Text>

        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.colors.primary }]}
          onPress={handleQueueMutation}
        >
          <Text style={[styles.buttonText, { color: theme.colors.onPrimary }]}>
            Execute Mutation (Auto-Queue if Offline)
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button, 
            { 
              backgroundColor: theme.colors.secondary,
              opacity: queueLength === 0 ? 0.5 : 1
            }
          ]}
          onPress={handleManualSync}
          disabled={queueLength === 0}
        >
          <Text style={[styles.buttonText, { color: theme.colors.onSecondary || theme.colors.onPrimary }]}>
            Manual Sync ({queueLength} pending)
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button, 
            { 
              backgroundColor: theme.colors.error,
              opacity: queueLength === 0 ? 0.5 : 1
            }
          ]}
          onPress={handleClearQueue}
          disabled={queueLength === 0}
        >
          <Text style={[styles.buttonText, { color: theme.colors.onError }]}>
            Clear Queue
          </Text>
        </TouchableOpacity>
      </View>

      {/* Mutation Queue Display */}
      {queueLength > 0 && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
            Queued Mutations
          </Text>
          {mutationQueue.map((mutation, index) => (
            <View 
              key={mutation.id} 
              style={[styles.queueItem, { backgroundColor: theme.colors.surface }]}
            >
              <Text style={[styles.queueItemTitle, { color: theme.colors.onSurface }]}>
                Mutation #{index + 1}
              </Text>
              <Text style={[styles.queueItemDetail, { color: theme.colors.onSurface }]}>
                ID: {mutation.id}
              </Text>
              <Text style={[styles.queueItemDetail, { color: theme.colors.onSurface }]}>
                Retries: {mutation.retryCount}
              </Text>
              <Text style={[styles.queueItemDetail, { color: theme.colors.onSurface }]}>
                Queued: {new Date(mutation.timestamp).toLocaleTimeString()}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Network Status Components Demo */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
          Network Status Components
        </Text>
        
        <Text style={[styles.componentLabel, { color: theme.colors.onBackground }]}>
          Compact Status:
        </Text>
        <View style={styles.componentDemo}>
          <CompactNetworkStatus />
        </View>

        <Text style={[styles.componentLabel, { color: theme.colors.onBackground }]}>
          Floating Status (shows when offline/queued):
        </Text>
        <View style={styles.componentDemo}>
          <FloatingNetworkStatus />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  statusLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  queueItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  queueItemTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  queueItemDetail: {
    fontSize: 12,
    opacity: 0.8,
  },
  componentLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
    marginBottom: 4,
  },
  componentDemo: {
    alignItems: 'flex-start',
    marginBottom: 8,
  },
});
