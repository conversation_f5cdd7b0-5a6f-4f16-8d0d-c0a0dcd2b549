import { createPlace } from '@/models/Place';
import { useMutation, useQuery } from '@apollo/client';
import { useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Dimensions, Image, RefreshControl, ScrollView } from 'react-native';
import RenderHtml from 'react-native-render-html';
import { CommentsList } from '../../components/common/CommentsList';
import UniversalMapWrapper from '../../components/map/UniversalMapWrapper';
import { CheckInButton } from '../../components/social/CheckInButton';
import { LikeButton } from '../../components/social/LikeButton';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { ADD_COMMENT, GET_PLACE } from '../../lib/graphql-operations';
import { createUser } from '../../models/User';
import { getShareableUrl, openDirections, shareContent } from '../../utils/sharing';


// Import shared comment utility
import { sharedStyles } from '../../styles/sharedStyles';
import { handleAddComment } from '../../utils/commentUtils';

const { width } = Dimensions.get('window');

export default function PlaceDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [refreshing, setRefreshing] = useState(false);
  const [comment, setComment] = useState('');
  const user = useAuth();

  // Query place details
  const { data, loading, error, refetch } = useQuery(GET_PLACE, {
    variables: { id },
  });

  // Add comment mutation
  const [addComment] = useMutation(ADD_COMMENT);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing place:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Replace local function with shared utility
  const handleAddCommentWrapper = async () => {
    await handleAddComment(addComment, {
      linkedObjectId: id,
      objectType: 'place',
      body: comment,
    }, (cache, newComment) => {
      const existing = cache.readQuery({ query: GET_PLACE, variables: { id } });
      let existingPlace: any = null;
      if (existing && typeof existing === 'object' && 'place' in existing) {
        existingPlace = (existing as any).place;
      } else if (existing) {
        existingPlace = existing;
      }
      if (existingPlace) {
        cache.writeQuery({
          query: GET_PLACE,
          variables: { id },
          data: {
            place: {
              ...existingPlace,
              comments: [...(existingPlace.comments || []), newComment],
            },
          },
        });
      }
    });
  };

  // Handle sharing
  const handleShare = async () => {
    if (!place) return;

    const shareableUrl = getShareableUrl('place', place.id);
    await shareContent(
      place.name,
      `Check out this place: ${place.name}`,
      shareableUrl
    );
  };

  // Handle getting directions
  const handleGetDirections = () => {
    if (!place?.loc) return;

    openDirections(
      place.name,
      place.loc.lat,
      place.loc.lng
    );
  };

  // Handle calling the place
  const handleCall = () => {
    if (!place?.phone) return;

    callPhoneNumber(place.phone);
  };

  // Handle visiting the website
  const handleVisitWebsite = () => {
    if (!place?.website) return;

    openWebsite(place.website);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={sharedStyles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={sharedStyles.loadingText}>Loading place...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={sharedStyles.errorContainer}>
        <ThemedText style={sharedStyles.errorText}>
          Error loading place: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={onRefresh} style={sharedStyles.retryButton} />
      </ThemedView>
    );
  }

  // Add null checks for place fields
  const place = createPlace(data?.place);
  // user from useAuth() is { user: User | null, ... }, so always use user.user
  const userModel = user && user.user ? createUser(user.user) : null;
  // Permission: owner or admin
  const userCanAddStuff = userModel && (userModel.id === place.userId || userModel.isAdmin());

  if (!place || !place.id || !place.name) {
    return (
      <ThemedView style={sharedStyles.errorContainer}>
        <ThemedText style={sharedStyles.errorText}>Place details are incomplete or missing.</ThemedText>
      </ThemedView>
    );
  }

  const callPhoneNumber = (phone: string) => {
    console.log(`Calling phone number: ${phone}`);
  };

  const openWebsite = (url: string) => {
    console.log(`Opening website: ${url}`);
  };

  return (
    <ScrollView
      style={sharedStyles.container}
      contentContainerStyle={sharedStyles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
      scrollEnabled={true} // Ensure scrolling is enabled
    >
      {place.avatar && (
        <Image
          source={{ uri: place.avatarUrl() }}
          style={sharedStyles.avatar}
          resizeMode="cover"
        />
      )}

      <ThemedView style={sharedStyles.header}>
        <ThemedText variant="title">{place.name}</ThemedText>
        {place.categoryDocs && place.categoryDocs.length > 0 && (
          <ThemedText variant="caption" style={sharedStyles.category}>
            {place.categoryDocs.map((category: { title: string }) => category.title).join(', ')}
          </ThemedText>
        )}
      </ThemedView>

      {place.avatar && (
        <Image
          source={{ uri: place.avatarUrl() }}
          style={sharedStyles.avatar}
          resizeMode="cover"
        />
      )}

      {place.desc && (
        <Card style={sharedStyles.section}>
          <ThemedText variant="subtitle">About</ThemedText>
          <RenderHtml
            contentWidth={width}
            source={{ html: place.desc }}
          />
        </Card>
      )}

      {/* Address section using fullLocation */}
      {place.fullLocation && place.getFullLocation() && (
        <Card style={sharedStyles.section}>
          <ThemedText variant="subtitle">Address</ThemedText>
          <ThemedText style={sharedStyles.address}>{place.getFullLocation()}</ThemedText>
        </Card>
      )}

      {place.loc && (
        <Card style={sharedStyles.section}>
          <UniversalMapWrapper
            selectedCategory={null}
            markerPosition={[place.loc.lat, place.loc.lng]}
          />
        </Card>
      )}

      {/* Happenings panel */}
      <Card style={sharedStyles.section}>
        <ThemedText variant="subtitle">Upcoming Events</ThemedText>
        {Array.isArray(place.happenings) && place.happenings.length > 0 ? (
          place.happenings.map((happening: import('../../types').Happening) => (
            <ThemedView key={happening.id} style={sharedStyles.eventItem}>
              <ThemedText>{happening.title}</ThemedText>
              <ThemedText variant="caption">
                {happening.when?.start ? formatDate(happening.when.start) : ''}
                {happening.when?.end && ` - ${formatDate(happening.when.end)}`}
              </ThemedText>
            </ThemedView>
          ))
        ) : (
          <ThemedText variant="caption" style={sharedStyles.emptyText}>No events yet.</ThemedText>
        )}
        {userCanAddStuff && (
          <Button
            title="New Event"
            onPress={() => {/* TODO: Implement navigation to new happening creation */}}
            style={sharedStyles.createButton}
          />
        )}
      </Card>

      {/* Offers panel */}
      <Card style={sharedStyles.section}>
        <ThemedText variant="subtitle">Current Offers</ThemedText>
        {Array.isArray(place.offers) && place.offers.length > 0 ? (
          place.offers.map((offer: import('../../types').Offer) => (
            <ThemedView key={offer.id} style={sharedStyles.offerItem}>
              <ThemedText>{offer.title}</ThemedText>
              <ThemedText variant="caption">
                Valid until: {offer.expires ? formatDate(offer.expires) : 'cancelled'}
              </ThemedText>
            </ThemedView>
          ))
        ) : (
          <ThemedText variant="caption" style={sharedStyles.emptyText}>No offers yet.</ThemedText>
        )}
        {userCanAddStuff && (
          <Button
            title="New Offer"
            onPress={() => {/* TODO: Implement navigation to new offer creation */}}
            style={sharedStyles.createButton}
          />
        )}
      </Card>

      {/* ProximityAds panel, visible only to place owner or admin */}
      {userCanAddStuff && (
        <Card style={sharedStyles.section}>
          <ThemedText variant="subtitle">Proximity Ads</ThemedText>
          {Array.isArray(place.proximityads) && place.proximityads.length > 0 ? (
            place.proximityads.map((ad: any) => (
              <ThemedView key={ad.id} style={sharedStyles.offerItem}>
                <ThemedText>{ad.title}</ThemedText>
                <ThemedText variant="caption">{ad.active ? 'Active' : 'Inactive'}</ThemedText>
              </ThemedView>
            ))
          ) : (
            <ThemedText variant="caption" style={sharedStyles.emptyText}>No proximity ads yet.</ThemedText>
          )}
          <Button
            title="New Proximity Ad"
            onPress={() => {/* TODO: Implement navigation to new proximity ad creation */}}
            style={sharedStyles.createButton}
          />
        </Card>
      )}

      <ThemedView style={sharedStyles.actionsContainer}>
        <CheckInButton
          objectId={place.id}
          objectType="place"
          style={sharedStyles.actionButton}
        />
        <LikeButton
          objectId={place.id}
          objectType="place"
          likeCount={Array.isArray(place.likes) ? place.likes.length : 0}
          isLiked={Array.isArray(place.likes) && userModel ? place.likes.some((like: any) => like.userId === userModel.id) : false}
          style={sharedStyles.actionButton}
        />
        <Button
          title="Directions"
          variant="outline"
          style={sharedStyles.actionButton}
          onPress={handleGetDirections}
        />
        <Button
          title="Share"
          variant="outline"
          style={sharedStyles.actionButton}
          onPress={handleShare}
        />
      </ThemedView>

      <ThemedView style={sharedStyles.actionsContainer}>
        {place.phone && (
          <Button
            title="Call"
            variant="outline"
            style={sharedStyles.actionButton}
            onPress={handleCall}
          />
        )}
        {place.website && (
          <Button
            title="Website"
            variant="outline"
            style={sharedStyles.actionButton}
            onPress={handleVisitWebsite}
          />
        )}
      </ThemedView>

      <Card style={sharedStyles.section}>
        <ThemedText variant="subtitle">Comments ({Array.isArray(place.comments) ? place.comments.length : 0})</ThemedText>
        <CommentsList
          comments={Array.isArray(place.comments) ? place.comments.map((c: any) => ({
            ...c,
            body: c.body || c.text, // Prefer body, fallback to text for legacy
            linkedObjectId: place.id,
            objectType: 'place',
          })) : []}
          onAddComment={handleAddCommentWrapper}
          comment={comment}
          setComment={setComment}
        />
      </Card>
    </ScrollView>
  );
}
