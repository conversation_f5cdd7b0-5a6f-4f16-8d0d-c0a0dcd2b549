import { Page as PageInterface } from '@/types/page';

/**
 * Page class that implements the PageInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Page implements PageInterface {
  id: string;
  title: string;
  body: string;
  fullScreen?: boolean;
  zone: string;
  userId: string;
  createdAt: string;
  updatedAt?: string;
  likes?: { id: string; userId: string }[];
  comments?: { id: string; text: string; userId: string; createdAt: string }[];

  constructor(pageData: PageInterface) {
    Object.assign(this, pageData);
  }

  /**
   * Get the URL for this page
   * @returns The URL for the page
   */
  linkToMe(): string {
    return `/page/${this.id}`;
  }

  /**
   * Get the formatted creation date
   * @returns A formatted string with the creation date
   */
  formattedDate(): string {
    if (!this.createdAt) {
      return '';
    }
    
    const date = new Date(this.createdAt);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Get the excerpt of the page body
   * @param length The maximum length of the excerpt
   * @returns A truncated version of the page body
   */
  excerpt(length: number = 150): string {
    if (!this.body) {
      return '';
    }
    
    // Remove HTML tags
    const textOnly = this.body.replace(/<[^>]*>/g, '');
    
    if (textOnly.length <= length) {
      return textOnly;
    }
    
    // Truncate and add ellipsis
    return textOnly.substring(0, length) + '...';
  }

  /**
   * Check if the user has liked this page
   * @param userId The ID of the user to check
   * @returns True if the user has liked the page, false otherwise
   */
  isLikedBy(userId: string): boolean {
    if (!this.likes || !userId) {
      return false;
    }
    
    return this.likes.some(like => like.userId === userId);
  }

  /**
   * Get the number of likes for this page
   * @returns The number of likes
   */
  likeCount(): number {
    return this.likes?.length || 0;
  }

  /**
   * Get the number of comments for this page
   * @returns The number of comments
   */
  commentCount(): number {
    return this.comments?.length || 0;
  }
}

/**
 * Factory function to create a Page instance from a plain object
 * @param data The page data
 * @returns A new Page instance
 */
export function createPage(data: PageInterface): Page {
  return new Page(data);
}

/**
 * Factory function to create multiple Page instances from an array
 * @param dataArray Array of page data
 * @returns Array of Page instances
 */
export function createPages(dataArray: PageInterface[]): Page[] {
  return dataArray.map(data => createPage(data));
}
