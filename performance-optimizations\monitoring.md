# Performance Monitoring

## Key Metrics
- Time to Interactive (TTI)
- First Contentful Paint (FCP)
- App startup time
- Frame rate during animations
- Memory usage
- API response times
- Bundle size

## Monitoring Tools
- Expo Application Services (EAS) Performance Monitoring
- Firebase Performance Monitoring
- Custom performance tracking with React Native Performance
- Chrome DevTools for web performance
- React DevTools Profiler