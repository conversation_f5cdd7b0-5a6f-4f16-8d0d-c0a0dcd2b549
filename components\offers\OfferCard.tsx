import { Offer, createOffer } from '@/models';
import { Offer as OfferInterface } from '@/types';
import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, View, useWindowDimensions } from 'react-native';
import { formatDate } from '../../utils/date';
import { ImageListItem } from '../common/ImageListItem';
import { TeaseCard } from '../common/TeaseCard';
import { ThemedText } from '../ui/ThemedText';

interface OfferCardProps {
  offer: OfferInterface | Offer;
  onPress?: () => void;
  onRedeem?: () => void;
  forceVariant?: 'tease' | 'image'; // Optional override for testing
  numColumns?: number;
}

export function OfferCard({ offer: offerData, onPress, forceVariant, numColumns = 1 }: OfferCardProps) {
  // Get window dimensions for responsive design
  const { width } = useWindowDimensions();

  // Convert to Offer instance if it's not already one
  const offer = offerData instanceof Offer ? offerData : createOffer(offerData);

  // Handle offer press
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(`/offer/${offer.id}` as any);
    }
  };

  // Get status of the offer
  const getStatus = () => {
    if (offer.isFullyRedeemed && offer.isFullyRedeemed()) {
      return 'Fully Redeemed';
    } else if (offer.isExpired && offer.isExpired()) {
      return 'Expired';
    } else {
      return 'Active';
    }
  };

  // Format date for display
  const getFormattedDateRange = () => {
    if (!offer.expires) return '';

    return `Valid until ${formatDate(offer.expires)}`;
  };

  // Determine which variant to use based on screen width
  const useTeaseVariant = forceVariant ? forceVariant === 'tease' : width < 768;

  // Get badge color based on status
  const status = getStatus();
  const getBadgeColor = () => {
    switch (status) {
      case 'Active':
        return '#4CAF50'; // Green
      case 'Expired':
        return '#F44336'; // Red
      case 'Fully Redeemed':
        return '#9E9E9E'; // Gray
      default:
        return '#9E9E9E'; // Gray
    }
  };

  // Calculate item width based on number of columns
  const itemWidthStyle = { width: numColumns > 1 ? `${100 / numColumns - 2}%` : '100%' };

  // For TeaseCard variant (smaller screens)
  if (useTeaseVariant) {
    return (
      <View style={itemWidthStyle as any}>
        <TeaseCard
          id={offer.id}
          title={offer.title}
          linkPath={`/offer/${offer.id}`}
          iconName={offer.iconName()}
          badge={status}
          badgeColor={getBadgeColor()}
          onPress={handlePress}
        />
        <ThemedText style={styles.dateRange} numberOfLines={1}>
          {getFormattedDateRange()}
        </ThemedText>
      </View>
    );
  }

  // For ImageListItem variant (larger screens)
  return (
    <View style={{ ...itemWidthStyle as any, margin: 8 }}>
      <ImageListItem
        id={offer.id}
        title={offer.title}
        description={offer.desc || ''}
        avatarURL={offer.avatarUrl()}
        linkPath={`/offer/${offer.id}`}
        iconName={offer.iconName()}
        onPress={handlePress}
      />
      <ThemedText style={styles.dateRange}>{getFormattedDateRange()}</ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  dateRange: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
});

