import { useApolloClient } from '@apollo/client';
import { router } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { User } from '../types';

// REST API endpoints for authentication
const LOGIN_ENDPOINT = '/auth/login';
const SIGNUP_ENDPOINT = '/auth/register';

// Ensure TOKEN_KEY and USER_KEY are defined
const TOKEN_KEY = 'auth_token';
const USER_KEY = 'auth_user';

// Define AuthContextType
interface AuthContextType {
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<boolean>;
  signup: (email: string, password: string, firstName: string, lastName: string) => Promise<boolean>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  isPlaceOwner: boolean;
  isCityManager: boolean;
  isAdmin: boolean;
}

// Ensure AuthContext is defined
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const apolloClient = useApolloClient();

  // Track loading states for GraphQL operations
  const [loginLoading, setLoginLoading] = useState(false);
  const [signupLoading, setSignupLoading] = useState(false);

  // Check for existing auth on mount
  useEffect(() => {
    async function loadAuth() {
      try {
        let storedToken: string | null = null;
        let storedUser: User | null = null;

        if (Platform.OS === 'web') {
          // Web storage
          storedToken = localStorage.getItem(TOKEN_KEY);
          const userJson = localStorage.getItem(USER_KEY);
          if (userJson) {
            storedUser = JSON.parse(userJson);
          }
        } else {
          // Native storage
          storedToken = await SecureStore.getItemAsync(TOKEN_KEY);
          const userJson = await SecureStore.getItemAsync(USER_KEY);
          if (userJson) {
            storedUser = JSON.parse(userJson);
          }
        }

        if (storedToken && storedUser) {
          setToken(storedToken);
          setUser(storedUser);
        }
      } catch (err) {
        console.error('Error loading auth data:', err);
      } finally {
        setLoading(false);
      }
    }

    loadAuth();
  }, []);

  // Save auth data
  const saveAuthData = async (newToken: string, newUser: User) => {
    try {
      if (Platform.OS === 'web') {
        // Web storage
        localStorage.setItem(TOKEN_KEY, newToken);
        localStorage.setItem(USER_KEY, JSON.stringify(newUser));
      } else {
        // Native storage
        await SecureStore.setItemAsync(TOKEN_KEY, newToken);
        await SecureStore.setItemAsync(USER_KEY, JSON.stringify(newUser));
      }

      setToken(newToken);
      setUser(newUser);
    } catch (err) {
      console.error('Error saving auth data:', err);
      throw new Error('Failed to save authentication data');
    }
  };

  // Clear auth data
  const clearAuthData = async () => {
    try {
      if (Platform.OS === 'web') {
        // Web storage
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(USER_KEY);
      } else {
        // Native storage
        await SecureStore.deleteItemAsync(TOKEN_KEY);
        await SecureStore.deleteItemAsync(USER_KEY);
      }

      setToken(null);
      setUser(null);
    } catch (err) {
      console.error('Error clearing auth data:', err);
    }
  };

  // Login function
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setError(null);
      setLoginLoading(true);

      // Use the REST API endpoint for authentication
      const baseUrl = process.env.EXPO_PUBLIC_GRAPHQL_URL?.replace('/graphql', '') || 'https://townql.fly.dev';
      const response = await fetch(`${baseUrl}${LOGIN_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
      }

      const data = await response.json();

      if (data?.token) {
        // Convert the response to match our User type
        const user: User = {
          id: data.userId || '1',
          emails: [{ address: email, verified: false }],
          email,
          zone: process.env.EXPO_PUBLIC_INITIAL_ZONE || 'default',
          userProfile: {
            id: data.profileId || '1',
            userId: data.userId || '1',
            firstName: data.firstName || email.split('@')[0],
            lastName: data.lastName || '',
            avatar: data.userProfile.avatar || undefined
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        await saveAuthData(data.token, user);
        return true;
      } else {
        setError('Invalid login response');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during login');
      return false;
    } finally {
      setLoginLoading(false);
    }
  };

  // Signup function
  const signup = async (
    email: string,
    password: string,
    firstName: string,
    lastName: string
  ): Promise<boolean> => {
    try {
      setError(null);
      setSignupLoading(true);

      // Use the REST API endpoint for registration
      const baseUrl = process.env.EXPO_PUBLIC_GRAPHQL_URL?.replace('/graphql', '') || 'https://townql.fly.dev';
      const response = await fetch(`${baseUrl}${SIGNUP_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          userProfile: {
            firstName,
            lastName
          },
          zone: process.env.EXPO_PUBLIC_INITIAL_ZONE || 'default' // Use initial zone from environment
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Registration failed');
      }

      const data = await response.json();

      if (data?.token) {
        // Convert the response to match our User type
        const user: User = {
          id: data.userId || '1',
          emails: [{ address: email, verified: false }],
          email,
          zone: process.env.EXPO_PUBLIC_INITIAL_ZONE || 'default',
          userProfile: {
            id: data.profileId || '1',
            userId: data.userId || '1',
            firstName,
            lastName,
            avatar: undefined
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        await saveAuthData(data.token, user);
        return true;
      } else {
        setError('Invalid registration response');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during signup');
      return false;
    } finally {
      setSignupLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    await clearAuthData();
    await apolloClient.resetStore();

    // Check if the current route is restricted
    const currentPath = window.location.pathname;
    const isRestricted = ['/admin', '/settings'].some((restrictedPath) => currentPath.startsWith(restrictedPath));

    if (isRestricted) {
      router.replace('/');
    } else {
      // Allow the user to remain on the current page if it's not restricted
      router.reload();
    }
  };

  // Context value
  const value = {
    user,
    token,
    loading: loading || loginLoading || signupLoading,
    error,
    login,
    signup,
    logout,
    isAuthenticated: !!token && !!user,
    isPlaceOwner: user?.roles?.includes('placeOwner') || false,
    isCityManager: user?.roles?.includes('cityManager') || false,
    isAdmin: user?.roles?.includes('admin') || false
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook for using the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
