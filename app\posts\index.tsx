import { useQuery } from '@apollo/client';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  TextInput,
  useWindowDimensions
} from 'react-native';

import { PostCard } from '../../components/posts/PostCard';
import { Button } from '../../components/ui/Button';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useZone } from '../../contexts/ZoneContext';
import { createInfiniteScroll } from '../../hooks/useInfiniteScroll';
import { GET_CATEGORIES, GET_POSTS } from '../../lib/graphql-operations';
import { createCategories, createPosts } from '../../models';
import { Post } from '../../models/Post';
import { createUser } from '../../models/User';


export default function PostsScreen() {
  const { currentZone, loading: zoneLoading } = useZone();
  const { user } = useAuth();
  const userCanAddPost = user && (createUser(user).isCityManager() || createUser(user).isAdmin());
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const { width } = useWindowDimensions();
  const { cat: routeCategory } = useLocalSearchParams();

  // Query posts for the current zone
  const queryResult = useQuery(GET_POSTS, {
    variables: {
      criteria: {
        zone: currentZone?.id || '',
        ...(selectedCategory && { categories: selectedCategory })
      },
      limit: 20,
      offset: 0,
    },
    skip: !currentZone?.id,
  });

  // Query categories for posts
  const { data: categoriesData } = useQuery(GET_CATEGORIES, {
    variables: {
      criteria: {
        zone: currentZone?.id || '',
        ctype: { $in: ['post', 'all'] }
      }
    },
    skip: !currentZone?.id,
  });

  // Process categories
  const categories = React.useMemo(() => {
    const allCategories = categoriesData?.categories
      ? createCategories(categoriesData.categories)
      : [];

    // Add "All" category at the beginning
    return [
      { id: null, title: 'All' },
      ...allCategories
    ];
  }, [categoriesData]);

  // Calculate numColumns based on screen width
  const numColumns = width >= 1024 ? 3 : width >= 768 ? 2 : 1;

  // Create infinite scroll
  const {
    items: posts,
    loading,
    refreshing,
    onRefresh,
    handleEndReached,
  } = createInfiniteScroll<Post>(queryResult, {
    getItems: (data) => data?.posts ? createPosts(data.posts) : [],
    getVariables: (offset, limit) => ({
      criteria: {
        zone: currentZone?.id || '',
        ...(selectedCategory && { categories: selectedCategory })
      },
      limit,
      offset,
    }),
    pageSize: 20,
    estimatedItemSize: 300,
    renderItem: renderPostItem,
    keyExtractor: keyExtractor,
  });

  const filteredPosts = posts.filter(post => {
    const matchesSearch = searchTerm === '' ||
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (post.desc && post.desc.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = !selectedCategory || post.categories?.includes(selectedCategory);
    return matchesSearch && matchesCategory;
  });

  useEffect(() => {
    if (routeCategory) {
      const category = Array.isArray(routeCategory) ? routeCategory[0] : routeCategory;
      setSelectedCategory(category);
      queryResult.refetch();
    }
  }, [routeCategory, queryResult]);

  // Loading state
  if (zoneLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        {/* Search input */}
        <TextInput
          style={styles.searchInput}
          placeholder="Search posts..."
          value={searchTerm}
          onChangeText={setSearchTerm}
        />

        {/* Category filters */}
        <ThemedView style={styles.categoryContainer || { flexDirection: 'row', marginBottom: 16 }}>
          {categories.map((category) => (
            <Button
              key={category.id || 'all'}
              title={category.title}
              onPress={() => setSelectedCategory(category.id)}
              variant={selectedCategory === category.id ? 'primary' : 'secondary'}
            />
          ))}
        </ThemedView>
      </ThemedView>

      {/* Posts grid */}
      {loading && filteredPosts.length === 0 ? (
        <ActivityIndicator size="large" />
      ) : filteredPosts.length > 0 ? (
        <FlatList
          data={filteredPosts}
          renderItem={({ item }) => <PostCard post={item} numColumns={numColumns} />}
          keyExtractor={(item) => item.id}
          numColumns={numColumns}
          key={`posts-list-${numColumns}`} 
          contentContainerStyle={styles.postsGrid}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}
          columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={loading ? <ActivityIndicator size="large" /> : null}
        />
      ) : (
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.emptyText}>No posts found.</ThemedText>
          {userCanAddPost && (
            <Button
              title="Create Post"
              onPress={() => router.push('/post/new')}
              style={styles.createButton}
            />
          )}
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    pointerEvents: 'auto'

  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
    backgroundColor: 'transparent'
  },
  subtitle: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
  },
  addButton: {
    marginBottom: 16,
  },
  postsGrid: {
    paddingBottom: 16,
  },
  row: {
    flex: 1,
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  createButton: {
    marginTop: 8,
  },
});

// Ensure numColumns is defined and accessible
const numColumns = 1;

// Updated renderPostItem to include numColumns
const renderPostItem = ({ item }: { item: Post }) => (
  <PostCard post={item} numColumns={numColumns} />
);

const keyExtractor = (item: Post) => item.id;


