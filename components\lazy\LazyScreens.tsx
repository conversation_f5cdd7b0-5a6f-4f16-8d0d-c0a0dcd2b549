import React, { Suspense } from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

// Loading component for lazy screens
const ScreenLoader: React.FC<{ screenName?: string }> = ({ screenName }) => (
  <ThemedView style={styles.loadingContainer}>
    <ActivityIndicator size="large" />
    <ThemedText style={styles.loadingText}>
      Loading {screenName || 'screen'}...
    </ThemedText>
  </ThemedView>
);

// Lazy-loaded screen components
export const LazyOffersScreen = React.lazy(() => import('../../app/offers/index'));
export const LazyMessagesScreen = React.lazy(() => import('../../app/messages/index'));
export const LazyHappeningsScreen = React.lazy(() => import('../../app/happenings/index'));
export const LazyPlacesScreen = React.lazy(() => import('../../app/places/index'));
export const LazyPostsScreen = React.lazy(() => import('../../app/posts/index'));
export const LazyMapScreen = React.lazy(() => import('../../app/map/index'));

// Lazy-loaded heavy components
export const LazyWebMapComponent = React.lazy(() => import('../map/WebMapComponent'));
export const LazyMobileMapComponent = React.lazy(() => import('../map/MobileMapComponent'));

// HOC to wrap lazy components with Suspense
export function withLazyLoading<T extends object>(
  LazyComponent: React.LazyExoticComponent<React.ComponentType<T>>,
  screenName?: string
) {
  return function LazyWrapper(props: T) {
    return (
      <Suspense fallback={<ScreenLoader screenName={screenName} />}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

// Pre-configured lazy screen components with proper loading states
export const OffersScreenLazy = withLazyLoading(LazyOffersScreen, 'Offers');
export const MessagesScreenLazy = withLazyLoading(LazyMessagesScreen, 'Messages');
export const HappeningsScreenLazy = withLazyLoading(LazyHappeningsScreen, 'Events');
export const PlacesScreenLazy = withLazyLoading(LazyPlacesScreen, 'Places');
export const PostsScreenLazy = withLazyLoading(LazyPostsScreen, 'Posts');
export const MapScreenLazy = withLazyLoading(LazyMapScreen, 'Map');

// Pre-configured lazy component wrappers
export const WebMapComponentLazy = withLazyLoading(LazyWebMapComponent, 'Map');
export const MobileMapComponentLazy = withLazyLoading(LazyMobileMapComponent, 'Map');

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});
