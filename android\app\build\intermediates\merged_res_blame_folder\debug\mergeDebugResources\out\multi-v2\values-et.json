{"logs": [{"outputFile": "com.townapp-mergeDebugResources-70:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,14938", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,15016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,201,274,342,422,490,565,643,725,813,887,966,1047,1124,1207,1290,1368,1442,1513,1596,1671,1755,1825", "endColumns": "70,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "121,196,269,337,417,485,560,638,720,808,882,961,1042,1119,1202,1285,1363,1437,1508,1591,1666,1750,1820,1899"}, "to": {"startLines": "33,49,88,95,96,111,112,113,161,162,163,164,169,170,171,172,173,174,175,176,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3038,4559,8977,9435,9503,10611,10679,10754,14615,14697,14785,14859,15265,15346,15423,15506,15589,15667,15741,15812,15996,16071,16155,16225", "endColumns": "70,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "3104,4629,9045,9498,9578,10674,10749,10827,14692,14780,14854,14933,15341,15418,15501,15584,15662,15736,15807,15890,16066,16150,16220,16299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c6b7a95e3d9be8d22942c383e641eb5e\\transformed\\play-services-basement-18.3.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5632", "endColumns": "139", "endOffsets": "5767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1047,1111,1196,1264,1328,1415,1479,1543,1602,1674,1738,1792,1911,1971,2032,2086,2159,2292,2376,2453,2546,2626,2719,2857,2937,3016,3142,3230,3309,3364,3415,3481,3554,3633,3704,3783,3856,3931,4005,4077,4190,4278,4355,4446,4538,4612,4686,4777,4831,4913,4982,5065,5151,5213,5277,5340,5408,5511,5614,5711,5812,5871,5926,6007,6096,6173", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "261,341,420,505,597,684,783,900,982,1042,1106,1191,1259,1323,1410,1474,1538,1597,1669,1733,1787,1906,1966,2027,2081,2154,2287,2371,2448,2541,2621,2714,2852,2932,3011,3137,3225,3304,3359,3410,3476,3549,3628,3699,3778,3851,3926,4000,4072,4185,4273,4350,4441,4533,4607,4681,4772,4826,4908,4977,5060,5146,5208,5272,5335,5403,5506,5609,5706,5807,5866,5921,6002,6091,6168,6246"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3109,3189,3268,3353,3445,4261,4360,4477,7205,7265,7329,9367,9583,9647,9734,9798,9862,9921,9993,10057,10111,10230,10290,10351,10405,10478,10832,10916,10993,11086,11166,11259,11397,11477,11556,11682,11770,11849,11904,11955,12021,12094,12173,12244,12323,12396,12471,12545,12617,12730,12818,12895,12986,13078,13152,13226,13317,13371,13453,13522,13605,13691,13753,13817,13880,13948,14051,14154,14251,14352,14411,14466,15021,15110,15187", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,166,167,168", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "311,3184,3263,3348,3440,3527,4355,4472,4554,7260,7324,7409,9430,9642,9729,9793,9857,9916,9988,10052,10106,10225,10285,10346,10400,10473,10606,10911,10988,11081,11161,11254,11392,11472,11551,11677,11765,11844,11899,11950,12016,12089,12168,12239,12318,12391,12466,12540,12612,12725,12813,12890,12981,13073,13147,13221,13312,13366,13448,13517,13600,13686,13748,13812,13875,13943,14046,14149,14246,14347,14406,14461,14542,15105,15182,15260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ac05835035c583740d1a4f8d61f55f0\\transformed\\play-services-base-18.1.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4634,4737,4899,5026,5134,5284,5413,5529,5772,5932,6040,6204,6336,6491,6636,6699,6764", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "4732,4894,5021,5129,5279,5408,5524,5627,5927,6035,6199,6331,6486,6631,6694,6759,6847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "69,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6960,7414,7514,7621", "endColumns": "99,99,106,98", "endOffsets": "7055,7509,7616,7715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "39,40,41,42,43,44,45,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3532,3627,3729,3827,3930,4036,4141,15895", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3622,3724,3822,3925,4031,4136,4256,15991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7e78917444daee60c1b50720bcd9758c\\transformed\\android-image-cropper-4.6.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,110,161,238,311,374,427", "endColumns": "54,50,76,72,62,52,67", "endOffsets": "105,156,233,306,369,422,490"}, "to": {"startLines": "70,89,90,91,92,93,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7060,9050,9101,9178,9251,9314,14547", "endColumns": "54,50,76,72,62,52,67", "endOffsets": "7110,9096,9173,9246,9309,9362,14610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\27b4d8b3dc714a89d9adc0d83f296ead\\transformed\\biometric-1.1.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,253,371,500,629,758,889,1020,1119,1258,1393", "endColumns": "107,89,117,128,128,128,130,130,98,138,134,116", "endOffsets": "158,248,366,495,624,753,884,1015,1114,1253,1388,1505"}, "to": {"startLines": "68,71,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6852,7115,7720,7838,7967,8096,8225,8356,8487,8586,8725,8860", "endColumns": "107,89,117,128,128,128,130,130,98,138,134,116", "endOffsets": "6955,7200,7833,7962,8091,8220,8351,8482,8581,8720,8855,8972"}}]}]}