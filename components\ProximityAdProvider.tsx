import { useMutation, useQuery } from '@apollo/client';
import * as Notifications from 'expo-notifications';
import React, { useCallback, useState } from 'react';
import { Modal, Platform, StyleSheet, View } from 'react-native';
import { Button } from '../components/ui/Button';
import { ThemedText } from '../components/ui/ThemedText';
import { useAuth } from '../contexts/AuthContext';
import { useZone } from '../contexts/ZoneContext';
import { useLocation } from '../hooks/useLocation';
import { ProximityAd, useProximityAds } from '../hooks/useProximityAds';
import { CHECK_IN, GET_CHECK_INS, GET_PROXIMITY_ADS_BY_ZONE } from '../lib/graphql-operations';

export const ProximityAdProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { currentZone } = useZone();
  const { location } = useLocation({ watchPosition: true });
  const [modalAd, setModalAd] = useState<ProximityAd | null>(null);

  // Fetch proximity ads for the current zone
  const { data: adsData, refetch: refetchAds } = useQuery(GET_PROXIMITY_ADS_BY_ZONE, {
    variables: { zoneId: currentZone?.id },
    skip: !currentZone?.id,
    fetchPolicy: 'cache-and-network',
  });

  // Fetch checks for an ad
  const getChecksForAd = useCallback(async (adId: string) => {
    const { data } = await client.query({
      query: GET_CHECK_INS,
      variables: { criteria: { linkedObjectId: adId, checkType: 'prox', userId: user?.id } },
      fetchPolicy: 'network-only',
    });
    return data?.checks || [];
  }, [user]);

  // Record a check
  const [checkIn] = useMutation(CHECK_IN);
  const recordCheck = useCallback(async (adId: string, loc: { lat: number; lng: number }) => {
    await checkIn({
      variables: {
        linkedObjectId: adId,
        objectType: 'proximityAd',
        checkType: 'prox',
        comment: undefined,
      },
    });
    refetchAds();
  }, [checkIn, refetchAds]);

  // Increment ad check count (refetch ads)
  const incrementAdCheckCount = async (adId: string) => {
    await refetchAds();
  };

  // Show ad (modal for web, notification for mobile)
  const showAd = (ad: ProximityAd) => {
    if (Platform.OS === 'web') {
      setModalAd(ad);
    } else {
      Notifications.scheduleNotificationAsync({
        content: {
          title: 'Nearby Offer',
          body: ad.desc.replace(/<[^>]+>/g, ''), // Strip HTML for notification
          data: { adId: ad.id },
        },
        trigger: null,
      });
    }
  };

  // Use the proximity ads logic
  useProximityAds({
    userId: user?.id,
    getUserLocation: async () => ({ lat: location?.coords.latitude, lng: location?.coords.longitude }),
    proximityAds: (adsData?.proximityAds || []).map((ad: any) => ({
      ...ad,
      location: ad.place?.loc || { lat: 0, lng: 0 },
      checksCount: ad.checksCount || 0,
      expiration: ad.expiration || ad.expires || ad.createdAt,
      showRule: ad.showRule || 'every_time',
    })),
    getChecksForAd,
    recordCheck,
    incrementAdCheckCount,
    showAd,
  });

  return (
    <>
      {children}
      {/* Web modal for proximity ad */}
      {Platform.OS === 'web' && modalAd && (
        <Modal visible transparent animationType="fade" onRequestClose={() => setModalAd(null)}>
          <View style={styles.overlay}>
            <View style={styles.modal}>
              <ThemedText variant="title">Nearby Offer</ThemedText>
              <div dangerouslySetInnerHTML={{ __html: modalAd.desc }} />
              <Button title="Close" onPress={() => setModalAd(null)} />
            </View>
          </View>
        </Modal>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  modal: {
    backgroundColor: '#fff',
    padding: 24,
    borderRadius: 12,
    minWidth: 320,
    maxWidth: 480,
    alignItems: 'center',
  },
});
