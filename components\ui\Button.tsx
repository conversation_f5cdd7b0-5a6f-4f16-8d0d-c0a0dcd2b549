import { TouchableOpacity, Text, ActivityIndicator, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
  style,
  textStyle,
}: ButtonProps) {
  const theme = useTheme();
  
  const variantStyles = {
    primary: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
      color: theme.colors.white,
    },
    secondary: {
      backgroundColor: theme.colors.secondary,
      borderColor: theme.colors.secondary,
      color: theme.colors.white,
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: theme.colors.primary,
      color: theme.colors.primary,
    },
    text: {
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      color: theme.colors.primary,
    },
  };
  
  const sizeStyles = {
    small: {
      paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.medium,
      fontSize: theme.typography.sizes.small,
    },
    medium: {
      paddingVertical: theme.spacing.small,
      paddingHorizontal: theme.spacing.large,
      fontSize: theme.typography.sizes.medium,
    },
    large: {
      paddingVertical: theme.spacing.medium,
      paddingHorizontal: theme.spacing.xl,
      fontSize: theme.typography.sizes.large,
    },
  };
  
  const buttonStyle = [
    styles.button,
    { 
      backgroundColor: variantStyles[variant].backgroundColor,
      borderColor: variantStyles[variant].borderColor,
      borderWidth: variant === 'outline' ? 1 : 0,
      paddingVertical: sizeStyles[size].paddingVertical,
      paddingHorizontal: sizeStyles[size].paddingHorizontal,
      opacity: disabled ? 0.6 : 1,
      width: fullWidth ? '100%' : undefined,
    },
    style,
  ];
  
  const textStyleFinal = [
    styles.text,
    { 
      color: variantStyles[variant].color,
      fontSize: sizeStyles[size].fontSize,
      fontFamily: theme.typography.families.medium,
    },
    textStyle,
  ];
  
  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator color={variantStyles[variant].color} />
      ) : (
        <Text style={textStyleFinal}>{title}</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600',
  },
});
