import { NetworkStatus, useQuery } from '@apollo/client';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import useReliableSubscription from '../../hooks/useSubscription';
import { GET_MESSAGES, MESSAGE_SUBSCRIPTION } from '../../lib/graphql-operations';
import { Message } from '../../types';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';
import MessageItem from './MessageItem';

interface RealtimeMessagesProps {
  conversationId: string;
  onOptimisticMessage?: (callback: (message: Message) => void) => void;
}

const MESSAGES_PER_PAGE = 20;

const RealtimeMessages: React.FC<RealtimeMessagesProps> = ({
  conversationId,
  onOptimisticMessage
}) => {
  const { user } = useAuth();
  const { theme } = useTheme();
  const [messages, setMessages] = useState<Message[]>([]);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  // Query for initial messages with pagination
  const { data, loading, error, fetchMore, refetch, networkStatus } = useQuery(GET_MESSAGES, {
    variables: {
      conversationId,
      limit: MESSAGES_PER_PAGE,
      offset: 0
    },
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: true,
  });

  // Subscribe to new messages using our reliable subscription hook
  const { data: subscriptionData, isActive: subscriptionActive } = useReliableSubscription(
    MESSAGE_SUBSCRIPTION,
    { conversationId },
    (newData) => {
      if (newData?.messageAdded) {
        handleNewMessage(newData.messageAdded);
      }
    }
  );

  // Initialize messages from query
  useEffect(() => {
    if (data?.messages) {
      // Check if we've reached the end of the available messages
      if (data.messages.length < MESSAGES_PER_PAGE) {
        setHasMoreMessages(false);
      }

      setMessages(data.messages);

      // Scroll to bottom after initial messages load
      if (data.messages.length > 0 && networkStatus !== NetworkStatus.fetchMore) {
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: false });
        }, 100);
      }
    }
  }, [data, networkStatus]);

  // Handle new message from subscription
  const handleNewMessage = useCallback((newMessage: Message) => {
    // Check if the message already exists
    setMessages((prevMessages) => {
      const exists = prevMessages.some((msg) => msg.id === newMessage.id);

      if (!exists) {
        // Scroll to bottom when new message arrives
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);

        return [...prevMessages, newMessage];
      }

      return prevMessages;
    });
  }, []);

  // Handle subscription data changes
  useEffect(() => {
    if (subscriptionData?.messageAdded) {
      handleNewMessage(subscriptionData.messageAdded);
    }
  }, [subscriptionData, handleNewMessage]);

  // Handle loading more messages
  const loadMoreMessages = useCallback(async () => {
    if (!hasMoreMessages || loading || isLoadingMore) return;

    setIsLoadingMore(true);

    try {
      await fetchMore({
        variables: {
          conversationId,
          offset: messages.length,
          limit: MESSAGES_PER_PAGE,
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult || !fetchMoreResult.messages.length) {
            setHasMoreMessages(false);
            return prev;
          }

          // Check if we've reached the end
          if (fetchMoreResult.messages.length < MESSAGES_PER_PAGE) {
            setHasMoreMessages(false);
          }

          return {
            messages: [...fetchMoreResult.messages, ...prev.messages],
          };
        },
      });
    } catch (error) {
      console.error('Error loading more messages:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [conversationId, fetchMore, hasMoreMessages, loading, isLoadingMore, messages.length]);

  // Add an optimistic message to the UI
  useEffect(() => {
    if (onOptimisticMessage) {
      onOptimisticMessage((message: Message) => {
        handleNewMessage(message);
      });
    }
  }, [onOptimisticMessage, handleNewMessage]);

  // Render loading state
  if (loading && messages.length === 0 && networkStatus !== NetworkStatus.fetchMore) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <ThemedText style={styles.loadingText}>Loading messages...</ThemedText>
      </ThemedView>
    );
  }

  // Render error state
  if (error && messages.length === 0) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading messages: {error.message}
        </ThemedText>
        <ThemedText
          style={styles.retryText}
          onPress={() => refetch()}
        >
          Tap to retry
        </ThemedText>
      </ThemedView>
    );
  }

  // Render message item
  const renderItem = ({ item }: { item: Message }) => (
    <MessageItem
      key={item.id}
      message={item}
      isOwnMessage={item.sender.id === user?.id}
    />
  );

  // Render loading indicator for pagination
  const renderFooter = () => {
    if (!isLoadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <ThemedText style={styles.footerText}>Loading more messages...</ThemedText>
      </View>
    );
  };

  // Render header (older messages loader)
  const renderHeader = () => {
    if (!hasMoreMessages) return null;

    return (
      <ThemedText
        style={styles.loadMoreText}
        onPress={loadMoreMessages}
      >
        Load older messages
      </ThemedText>
    );
  };

  return (
    <FlatList
      ref={flatListRef}
      data={messages}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      contentContainerStyle={styles.messagesContainer}
      ListHeaderComponent={renderHeader}
      ListFooterComponent={renderFooter}
                keyboardShouldPersistTaps="handled"
          scrollEnabled={true}

      ListEmptyComponent={
        <ThemedView style={styles.emptyContainer}>
          <ThemedText>No messages yet. Start the conversation!</ThemedText>
        </ThemedView>
      }
      refreshControl={
        <RefreshControl
          refreshing={networkStatus === NetworkStatus.refetch}
          onRefresh={refetch}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
      inverted={false}
      onEndReached={hasMoreMessages ? loadMoreMessages : undefined}
      onEndReachedThreshold={0.3}
    />
  );
};

const styles = StyleSheet.create({
  messagesContainer: {
    padding: 10,
    flexGrow: 1,
        pointerEvents: 'auto'

  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
        pointerEvents: 'auto'

  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    padding: 20,
    backgroundColor: '#ffeeee',
    borderRadius: 5,
    margin: 10,
        pointerEvents: 'auto'

  },
  errorText: {
    color: '#ff0000',
    marginBottom: 10,
  },
  retryText: {
    color: '#0000ff',
    textDecorationLine: 'underline',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
        pointerEvents: 'auto'

  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
  footerText: {
    marginLeft: 10,
    fontSize: 14,
  },
  loadMoreText: {
    textAlign: 'center',
    padding: 10,
    color: '#0000ff',
    textDecorationLine: 'underline',
  },
});

export default RealtimeMessages;
