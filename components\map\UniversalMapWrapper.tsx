import React from 'react';
import { Platform } from 'react-native';
import { MobileMapComponentLazy } from '../lazy/LazyScreens';
import WebMapComponent from './WebMapComponent';

type UniversalMapWrapperProps = {
  selectedCategory: string | null;
  markerPosition?: [number, number];
};

// Handle platform detection at the top level instead of in dynamic imports
const isWeb = Platform.OS === 'web';
const UniversalMapWrapper: React.FC<UniversalMapWrapperProps> = ({ selectedCategory, markerPosition }) => {
  // For web, use direct import for better performance on initial load
  // since web users expect immediate map rendering
  if (isWeb) {
    return <WebMapComponent selectedCategory={selectedCategory} />;
  }

  // For mobile, use lazy loading to reduce initial bundle size
  return (
    <MobileMapComponentLazy
      selectedCategory={selectedCategory}
      markerPosition={markerPosition ? { lat: markerPosition[0], lng: markerPosition[1] } : undefined}
    />
  );
}

export default UniversalMapWrapper;