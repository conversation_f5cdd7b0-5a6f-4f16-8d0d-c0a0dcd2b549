import React from 'react';
import { Platform } from 'react-native';
import WebMapComponent from './WebMapComponent';

type UniversalMapWrapperProps = {
  selectedCategory: string | null;
  markerPosition?: [number, number];
};

// Handle platform detection at the top level instead of in dynamic imports
const isWeb = Platform.OS === 'web';
const UniversalMapWrapper: React.FC<UniversalMapWrapperProps> = ({ selectedCategory, markerPosition }) => {
  // Use React.lazy instead of dynamic require for better compatibility
  const MobileMapComponent = React.useMemo(() => {
    if (!isWeb) {
      // Only import the mobile component on non-web platforms
      return React.lazy(() => 
        import('./MobileMapComponent')
      );
    }
    return null;
  }, []);

  // For web, directly return the web component
  if (isWeb) {
    return <WebMapComponent selectedCategory={selectedCategory} />;
  }

  // For mobile, render the lazy-loaded component with a suspense fallback
  return (
    <React.Suspense fallback={null}>
      {MobileMapComponent && (
        <MobileMapComponent
          selectedCategory={selectedCategory}
          markerPosition={
            markerPosition ? { lat: markerPosition[0], lng: markerPosition[1] } : undefined
          }
        />
      )}
    </React.Suspense>
  );
}

export default UniversalMapWrapper;