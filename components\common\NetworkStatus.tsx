import NetInfo from '@react-native-community/netinfo';
import { useEffect, useState } from 'react';
import { Animated, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemedText } from '../ui/ThemedText';

/**
 * Component that displays a banner when the device is offline
 * and data is being served from cache
 */
export function NetworkStatus() {
  const [isConnected, setIsConnected] = useState(true);
  const [bannerHeight] = useState(new Animated.Value(0));
  const theme = useTheme();

  useEffect(() => {
    // Subscribe to network state changes
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(!!state.isConnected);
    });

    // Initial check
    NetInfo.fetch().then(state => {
      setIsConnected(!!state.isConnected);
    });

    // Cleanup
    return () => {
      unsubscribe();
    };
  }, []);

  // Animate banner height when connection status changes
  useEffect(() => {
    Animated.timing(bannerHeight, {
      toValue: isConnected ? 0 : 40,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isConnected, bannerHeight]);

  // Don't render anything if connected
  if (isConnected) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          height: bannerHeight,
          backgroundColor: theme.colors.warning,
        },
      ]}
    >
      <ThemedText style={styles.text}>
        You're offline. Data is being served from cache.
      </ThemedText>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 999,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
        pointerEvents: 'auto'

  },
  text: {
    color: '#000',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
