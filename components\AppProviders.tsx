import { Apollo<PERSON><PERSON>ider } from '@apollo/client';
import { ReactNode } from 'react';
import { AuthProvider } from '../contexts/AuthContext';
import { MapProvider } from '../contexts/MapContext';
import { ThemeProvider } from '../contexts/ThemeContext';
import { ZoneProvider } from '../contexts/ZoneContext';
import { useUserPresence } from '../hooks/useUserPresence';
import client from '../lib/apollo-client';

interface AppProvidersProps {
  children: ReactNode;
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <ApolloProvider client={client}>
      <AuthProvider>
        <ThemeProvider>
          <ZoneProvider>
            <MapProvider>
              <UserPresenceWrapper>{children}</UserPresenceWrapper>
            </MapProvider>
          </ZoneProvider>
        </ThemeProvider>
      </AuthProvider>
    </ApolloProvider>
  );
}

function UserPresenceWrapper({ children }: { children: ReactNode }) {
  useUserPresence(); // Initialize user presence tracking
  return <>{children}</>;
}
