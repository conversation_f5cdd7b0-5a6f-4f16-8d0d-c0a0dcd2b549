import { User, UserData } from './User';
import { UserProfile, UserProfileData } from './UserProfile';

// Factory functions to create model instances from raw data
export const createUser = (data: UserData): User => {
  return new User(data);
};

export const createUserProfile = (data: UserProfileData): UserProfile => {
  return new UserProfile(data);
};

// Helper to transform arrays of data
export const createUsers = (dataArray: UserData[]): User[] => {
  return dataArray.map(data => createUser(data));
};