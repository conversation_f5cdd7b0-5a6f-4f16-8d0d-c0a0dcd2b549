import { gql } from '@apollo/client';

export const BLOCK_USER = gql`
  mutation BlockUser($userId: String!) {
    blockUser(userId: $userId) {
      id
      userId
      blockedUserId
      createdAt
    }
  }
`;

export const UNBLOCK_USER = gql`
  mutation UnblockUser($userId: String!) {
    unblockUser(userId: $userId) {
      success
    }
  }
`;

// Authentication Operations
export const LOGIN = gql`
  mutation Login($email: String!, $password: String!) {
    login(email: $email, password: $password) {
      token
      user {
        id
        email
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
    }
  }
`;

export const SIGNUP = gql`
  mutation Signup($input: SignupInput!) {
    signup(input: $input) {
      token
      user {
        id
        email
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
    }
  }
`;

export const REQUEST_PASSWORD_RESET = gql`
  mutation RequestPasswordReset($email: String!) {
    requestPasswordReset(email: $email)
  }
`;

export const CHANGE_PASSWORD = gql`
  mutation ChangePassword($currentPassword: String!, $newPassword: String!) {
    changePassword(currentPassword: $currentPassword, newPassword: $newPassword)
  }
`;

// User Operations
export const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    me {
      id
      emails {
        fullAddress
        verified
      }
      zone
      lastOnline
      userProfile {
        id
        firstName
        lastName
        bio
        avatar
      }
      zones {
        id
        name
      }
    }
  }
`;

export const UPDATE_PROFILE = gql`
  mutation UpdateProfile($input: UpdateProfileInput!) {
    updateProfile(input: $input) {
      id
      userProfile {
        id
        firstName
        lastName
        bio
        avatar
      }
    }
  }
`;

// Zone Operations
export const GET_ZONES = gql`
  query GetZones {
    zones {
      id
    }
  }
`;

export const GET_ZONE_DETAILS = gql`
  query GetZoneDetails($id: String!) {
    zone(id: $id) {
      id
      title
      center {
        lat
        lng
      }
      createdAt
      updatedAt
      appName
      temp
      skytext
      weatherUpdatedAt
      subscriptionsEnabled
      useGiftCards
      giftcardHoldback
      welcomePage
      rootPage
    }
  }
`;

export const JOIN_ZONE = gql`
  mutation JoinZone($id: String!) {
    joinZone(id: $id) {
      id
      isMember
    }
  }
`;

export const LEAVE_ZONE = gql`
  mutation LeaveZone($id: String!) {
    leaveZone(id: $id) {
      id
      isMember
    }
  }
`;

// Post Operations
export const GET_POSTS = gql`
  query GetZonePosts($criteria: JSON, $limit: Int, $skip: Int) {
    posts(criteria: $criteria, limit: $limit, skip: $skip) {
      id
      title
      desc
      zone
      avatar
      userId
      createdAt
      updatedAt
      categories
      owner {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      likes {
        id
        userId
        createdAt
      }
      comments {
        id
        body
        createdAt
        userId
      }
    }
  }
`;

// Add `tags` field to `GET_POST`
export const GET_POST = gql`
  query GetPost($id: String!) {
    post(id: $id) {
      id
      title
      desc
      zone
      avatar
      userId
      createdAt
      updatedAt
      categories
      tags
      owner {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      likes {
        id
        userId
        createdAt
      }
      comments {
        id
        body
        createdAt
        userId
        owner {
          id
          emails {
            address
            verified
          }
          userProfile {
            id
            firstName
            lastName
            avatar
          }
        }
      }
      categoryDocs {
        id
        title
        ctype
      }
    }
  }
`;

export const CREATE_POST = gql`
  mutation CreatePost($input: CreatePostInput!) {
    createPost(input: $input) {
      id
      title
      content
    }
  }
`;

// Message Operations
export const GET_CONVERSATIONS = gql`
  query GetConversations {
    conversations {
      id
      lastMessage {
        id
        content
        createdAt
        read
      }
      participant {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      unreadCount
    }
  }
`;

export const GET_CONVERSATION_BY_ID = gql`
  query GetConversationById($id: String!) {
    conversation(id: $id) {
      id
      participant {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
        lastOnline
      }
      messages {
        id
        content
        createdAt
        sender {
          id
        }
        read
      }
    }
  }
`;

export const GET_CONVERSATION_MESSAGES = gql`
  query GetConversationMessages($conversationId: String!) {
    messages(conversationId: $conversationId) {
      id
      content
      createdAt
      sender {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      read
    }
  }
`;

export const SEND_CONVERSATION_MESSAGE = gql`
  mutation SendConversationMessage($input: SendMessageInput!) {
    sendMessage(input: $input) {
      id
      content
      createdAt
      sender {
        id
      }
      read
    }
  }
`;

// Place Operations
export const GET_PLACES = gql`
  query GetPlaces($criteria: JSON, $limit: Int, $skip: Int) {
    places(criteria: $criteria, limit: $limit, skip: $skip) {
      id
      name
      desc
      zone
      loc {
        lat
        lng
      }
      avatar
      categories
      categoryDocs {
        id
        title
        ctype
      }
      properties
      active
      type
      userId
      createdAt
      updatedAt
    }
  }
`;

export const GET_PLACE = gql`
  query GetPlace($id: String!) {
    place(id: $id) {
      id
      name
      desc
      zone
      loc {
        lat
        lng
      }
      fullLocation {
        fullAddress
        street
        city
        state
        zip
        country
      }
      categories
      categoryDocs {
        id
        title
        ctype
      }
      likes {
        id
        userId
        createdAt
      }
      comments {
        id
        body
        createdAt
        userId
        owner {
          id
          emails {
            address
            verified
          }
          userProfile {
            id
            firstName
            lastName
            avatar
          }
        }
      }
      happenings {
        id
        title
        when {
          start
          end
        }
      }
      offers {
        id
        title
        expires
      }
      checks {
        createdAt
        userId
      }
    }
  }
`;

// User Search Operations
export const GET_USERS = gql`
  query GetUsers($criteria: JSON) {
    users(criteria: $criteria) {
      id
      userProfile {
        id
        firstName
        lastName
      }
      checks {
        id
        userId
        createdAt
      }
    }
  }
`;

// Conversation Operations
export const CREATE_CONVERSATION = gql`
  mutation CreateConversation($input: CreateConversationInput!) {
    createConversation(input: $input) {
      id
    }
  }
`;

// Subscription Operations
export const CONVERSATION_MESSAGE_RECEIVED = gql`
  subscription ConversationMessageReceived {
    messageReceived {
      id
      content
      createdAt
      sender {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      read
    }
  }
`;

// Check-in Operations
export const GET_CHECK_INS = gql`
  query GetCheckIns($criteria: JSON) {
    checks(criteria: $criteria) {
      id
      userId
      linkedObjectId
      objectType
      type
      createdAt
      comment
    }
  }
`;

export const REDEEM_OFFER = gql`
  mutation RedeemOffer($offerId: String!, $comment: String) {
    createCheck(input: {
      linkedObjectId: $offerId,
      objectType: "offer",
      type: "redeem",
      comment: $comment
    }) {
      id
      userId
      linkedObjectId
      objectType
      type
      createdAt
      comment
    }
  }
`;

export const MESSAGE_SUBSCRIPTION = gql`
  subscription MessageSubscription($conversationId: String!) {
    messageAdded(conversationId: $conversationId) {
      id
      content
      createdAt
      sender {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      read
    }
  }
`;

export const TYPING_SUBSCRIPTION = gql`
  subscription TypingSubscription($conversationId: String!) {
    typingStatus(conversationId: $conversationId) {
      userId
      isTyping
      conversationId
    }
  }
`;

export const SET_TYPING_STATUS = gql`
  mutation SetTypingStatus($conversationId: String!, $isTyping: Boolean!) {
    setTypingStatus(conversationId: $conversationId, isTyping: $isTyping) {
      userId
      isTyping
      conversationId
    }
  }
`;

export const POST_CREATED = gql`
  subscription PostCreated($zoneId: String!) {
    postCreated(zoneId: $zoneId) {
      id
      title
      content
      images
      createdAt
      owner {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      likes
      commentCount
    }
  }
`;

export const NOTIFICATION_RECEIVED = gql`
  subscription NotificationReceived {
    notificationReceived {
      id
      type
      title
      body
      createdAt
      read
      data
      sender {
        id
        userProfile {
          firstName
          lastName
          avatar
        }
      }
      entityId
      entityType
    }
  }
`;

export const GET_NOTIFICATIONS = gql`
  query GetNotifications($limit: Int, $offset: Int) {
    notifications(limit: $limit, offset: $offset) {
      id
      type
      title
      body
      createdAt
      read
      data
      sender {
        id
        userProfile {
          firstName
          lastName
          avatar
        }
      }
      entityId
      entityType
    }
  }
`;

export const MARK_NOTIFICATION_READ = gql`
  mutation MarkNotificationRead($id: String!) {
    markNotificationRead(id: $id) {
      id
      read
    }
  }
`;

export const REGISTER_PUSH_TOKEN = gql`
  mutation RegisterPushToken($token: String!, $deviceType: String!) {
    registerPushToken(token: $token, deviceType: $deviceType) {
      success
    }
  }
`;

// Like Operations
export const LIKE_CONTENT = gql`
  mutation LikeContent($input: LikeInput!) {
    likeContent(input: $input) {
      id
      likes
      isLiked
    }
  }
`;

export const UNLIKE_CONTENT = gql`
  mutation UnlikeContent($input: LikeInput!) {
    unlikeContent(input: $input) {
      id
      likes
      isLiked
    }
  }
`;

export const GET_LIKES = gql`
  query GetLikes($objectId: String!, $objectType: String!) {
    likes(objectId: $objectId, objectType: $objectType) {
      id
      user {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      createdAt
    }
  }
`;

// Happening Operations
export const GET_HAPPENINGS = gql`
  query GetHappenings($criteria: JSON, $limit: Int, $skip: Int) {
    happenings(criteria: $criteria, limit: $limit, skip: $skip) {
      id
      title
      desc
      zone
      userId
      place
      avatar
      when {
        start
        end
      }
      owner {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      categories
      categoryDocs {
        id
        title
        ctype
      }
    }
  }
`;

export const GET_HAPPENING = gql`
  query GetHappening($id: String!) {
    happening(id: $id) {
      id
      title
      desc
      zone
      userId
      isLiked
      place {
        name
        loc {
          lat
          lng
        }
        address
      }
      when {
        start
        end
      }
      placeDetails {
        id
        name
        desc
        fullLocation {
          fullAddress
          street
          city
          state
          zip
          country
        }
        loc {
          lat
          lng
        }
      }
      owner {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      likes {
        id
        userId
        createdAt
      }
      comments {
        id
        body
        createdAt
        userId
        owner {
          id
          emails {
            address
            verified
          }
          userProfile {
            id
            firstName
            lastName
            avatar
          }
        }
      }
    }
  }
`;

// Offer Operations
export const GET_OFFERS = gql`
  query GetOffers($criteria: JSON) {
    offers(criteria: $criteria) {
      id
      title
      desc
      zone
      userId
      placeId
      expires
      place {
        id
        name
        desc
        fullLocation {
          fullAddress
          street
          city
          state
          zip
          country
        }
      }
    }
  }
`;

export const GET_OFFER = gql`
  query GetOffer($id: String!) {
    offer(id: $id) {
      id
      title
      desc
      zone
      userId
      isLiked
      isRedeemed
      redeemers {
        id
      }
      place {
        id
        name
        loc {
          lat
          lng
        }
        address
        fullLocation {
          fullAddress
          street
          city
          state
          zip
          country
        }
      }
      expires
      maxRedeems
      redeems
      owner {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      likes {
        id
        userId
        createdAt
      }
      comments {
        id
        body
        createdAt
        userId
        owner {
          id
          emails {
            address
            verified
          }
          userProfile {
            id
            firstName
            lastName
            avatar
          }
        }
      }
    }
  }
`;

// Campaign Operations
export const GET_CAMPAIGNS = gql`
  query GetCampaigns($criteria: JSON) {
    campaigns(criteria: $criteria) {
      id
      title
      desc
      starts
      stops
      zone
    }
  }
`;

// Check-in Operations
export const CHECK_IN = gql`
  mutation CheckIn($linkedObjectId: String!, $objectType: String!, $comment: String, $checkType: String) {
    createCheck(input: {
      linkedObjectId: $linkedObjectId,
      objectType: $objectType,
      comment: $comment,
      type: $checkType
    }) {
      id
      linkedObjectId
      objectType
      comment
      type
      createdAt
    }
  }
`;

// GET_CHECK_INS is already defined above

// Direct Message Operations
export const GET_USER_MESSAGES = gql`
  query GetUserMessages($userId: String!) {
    messages(userId: $userId) {
      id
      content
      sender {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      recipient {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      read
      createdAt
    }
  }
`;

export const GET_USER_CONVERSATION = gql`
  query GetUserConversation($userId: String!, $otherUserId: String!) {
    conversation(userId: $userId, otherUserId: $otherUserId) {
      id
      content
      sender {
        id

        avatar
      }
      recipient {
        id

        avatar
      }
      read
      createdAt
    }
  }
`;

export const SEND_DIRECT_MESSAGE = gql`
  mutation SendDirectMessage($senderId: String!, $recipientId: String!, $content: String!) {
    sendMessage(senderId: $senderId, recipientId: $recipientId, content: $content) {
      id
      content
      sender {
        id

      }
      recipient {
        id

      }
      read
      createdAt
    }
  }
`;

export const USER_MESSAGE_RECEIVED = gql`
  subscription UserMessageReceived($userId: String!) {
    messageReceived(userId: $userId) {
      id
      content
      sender {
        id

        avatar
      }
      recipient {
        id

        avatar
      }
      read
      createdAt
    }
  }
`;

// Page Operations
export const GET_PAGE = gql`
  query GetPage($id: String!) {
    page(id: $id) {
      id
      title
      body
      zone
      userId
      createdAt
      updatedAt
      owner {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      likes {
        id
        userId
        createdAt
      }
      comments {
        id
        body
        createdAt
        userId
        owner {
          id
          emails {
            address
            verified
          }
          userProfile {
            id
            firstName
            lastName
            avatar
          }
        }
      }
    }
  }
`;

export const GET_CATEGORIES = gql`
  query GetCategories($criteria: JSON) {
    categories(criteria: $criteria) {
      id
      title
      ctype
      zone
      iconName
      createdAt
      updatedAt
    }
  }
`;

// Create and Update Happening Operations
export const CREATE_HAPPENING = gql`
  mutation CreateHappening($input: CreateHappeningInput!) {
    createHappening(input: $input) {
      id
      title
      desc
      when {
        start
        end
      }
      place
      zone
      categories
    }
  }
`;

export const UPDATE_HAPPENING = gql`
  mutation UpdateHappening($id: String!, $input: UpdateHappeningInput!) {
    updateHappening(id: $id, input: $input) {
      id
      title
      desc
      when {
        start
        end
      }
      place
      zone
      categories
    }
  }
`;

export const DELETE_HAPPENING = gql`
  mutation DeleteHappening($id: String!) {
    deleteHappening(id: $id)
  }
`;

// Create and Update Offer Operations
export const CREATE_OFFER = gql`
  mutation CreateOffer($input: CreateOfferInput!) {
    createOffer(input: $input) {
      id
      title
      desc
      expires
      placeId
      zone
      categories
      price
      maxRedeems
    }
  }
`;

export const UPDATE_OFFER = gql`
  mutation UpdateOffer($id: String!, $input: UpdateOfferInput!) {
    updateOffer(id: $id, input: $input) {
      id
      title
      desc
      expires
      placeId
      zone
      categories
      price
      maxRedeems
    }
  }
`;

export const DELETE_OFFER = gql`
  mutation DeleteOffer($id: String!) {
    deleteOffer(id: $id)
  }
`;

// Social Interaction Operations
export const LIKE_OBJECT = gql`
  mutation LikeObject($objectId: String!, $objectType: String!) {
    like(objectId: $objectId, objectType: $objectType) {
      id
      userId
      linkedObjectId
      objectType
      createdAt
    }
  }
`;

export const UNLIKE_OBJECT = gql`
  mutation UnlikeObject($objectId: String!, $objectType: String!) {
    unlike(objectId: $objectId, objectType: $objectType)
  }
`;

export const ADD_COMMENT = gql`
  mutation AddComment($input: CreateCommentInput!) {
    createComment(input: $input) {
      id
      body
      linkedObjectId
      objectType
      userId
      createdAt
    }
  }
`;

// Friend Request Operations
export const GET_FRIEND_REQUESTS = gql`
  query GetFriendRequests($criteria: JSON) {
    requests(
      criteria: $criteria
    ) {
      id
      linkedObjectId
      objectType
      type
      requesterId
      createdAt
      requester {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
      requestee {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
    }
  }
`;

export const GET_INCOMING_FRIEND_REQUESTS = gql`
  query GetIncomingFriendRequests {
    requests(
      criteria: {
        objectType: "users",
        type: "friend",
        linkedObjectId: "me"
      }
    ) {
      id
      linkedObjectId
      objectType
      type
      requesterId
      createdAt
      requester {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
    }
  }
`;

export const GET_OUTGOING_FRIEND_REQUESTS = gql`
  query GetOutgoingFriendRequests {
    requests(
      criteria: {
        objectType: "users",
        type: "friend",
        requesterId: "me"
      }
    ) {
      id
      linkedObjectId
      objectType
      type
      requesterId
      createdAt
      requestee {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
      }
    }
  }
`;

export const SEND_FRIEND_REQUEST = gql`
  mutation SendFriendRequest($targetUserId: String!) {
    createRequest(
      input: {
        linkedObjectId: $targetUserId,
        objectType: "users",
        type: "friend"
      }
    ) {
      id
      linkedObjectId
      objectType
      type
      requesterId
      createdAt
    }
  }
`;

export const ACCEPT_FRIEND_REQUEST = gql`
  mutation AcceptFriendRequest($requestId: String!) {
    acceptFriendRequest(requestId: $requestId) {
      id
      userId
      friendId
      createdAt
    }
  }
`;

export const REJECT_FRIEND_REQUEST = gql`
  mutation RejectFriendRequest($requestId: String!) {
    deleteRequest(id: $requestId)
  }
`;

export const GET_USER_FRIENDS = gql`
  query GetUserFriends($userId: String!, $limit: Int, $skip: Int) {
    user(id: $userId) {
      id
      friends(limit: $limit, skip: $skip) {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
        lastOnline
      }
    }
  }
`;

export const GET_CURRENT_USER_WITH_FRIENDS = gql`
  query GetCurrentUserWithFriends($limit: Int, $skip: Int) {
    currentUser {
      id
      friends(limit: $limit, skip: $skip) {
        id
        emails {
          address
          verified
        }
        userProfile {
          id
          firstName
          lastName
          avatar
        }
        lastOnline
      }
    }
  }
`;

// Map Operations
export const GET_PLACES_IN_POLYGON = gql`
  query PlacesInPolygon($coordinates: [[JSON!]!]!, $criteria: JSON, $limit: Int) {
    placesInPolygon(
      coordinates: $coordinates,
      criteria: $criteria,
      limit: $limit
    ) {
      id
      name
      desc
      categories
      loc {
        lat
        lng
      }
      avatar
      userId
      owner {
        id
        userProfile {
          firstName
          lastName
          avatar
        }
      }
    }
  }
`;

export const GET_PLACES_NEAR_LOCATION = gql`
  query PlacesNearLocation($lat: Float!, $lng: Float!, $maxDistance: Float, $limit: Int) {
    placesNearLocation(
      lat: $lat,
      lng: $lng,
      maxDistance: $maxDistance,
      limit: $limit
    ) {
      id
      name
      desc
      categories
      loc {
        lat
        lng
      }
      avatar
      userId
      owner {
        id
        userProfile {
          firstName
          lastName
          avatar
        }
      }
      distance
    }
  }
`;

// User Activity Operations
export const GET_USER_ACTIVITY = gql`
  query GetUserActivity($userId: String!, $limit: Int, $offset: Int) {
    userActivity(userId: $userId, limit: $limit, offset: $offset) {
      id
      type
      createdAt
      user {
        id
        emails {
          address
          verified
        }
        userProfile {
          firstName
          lastName
          avatar
        }
      }
      targetId
      targetType
      targetTitle
      targetImage
    }
  }
`;

// Social Sharing Operations
export const SHARE_CONTENT = gql`
  mutation ShareContent($input: ShareContentInput!) {
    shareContent(input: $input) {
      id
      success
      url
    }
  }
`;

// Entity Search Operations
export const SEARCH_ENTITIES = gql`
  query SearchEntities($query: String!, $type: String, $zoneId: String!, $limit: Int) {
    searchEntities(query: $query, type: $type, zoneId: $zoneId, limit: $limit) {
      ... on Post {
        id
        title
        desc
        avatar
        createdAt
        categories
      }
      ... on Place {
        id
        name
        desc
        avatar
        categories
        loc {
          lat
          lng
        }
      }
      ... on Happening {
        id
        title
        desc
        avatar
        when {
          start
          end
        }
        place
        categories
      }
      ... on Offer {
        id
        title
        desc
        avatar
        redemptionCode
        place
        categories
      }
      ... on User {
        id
        emails {
          address
        }
        userProfile {
          firstName
          lastName
          avatar
          bio
        }
      }
    }
  }
`;

export const SEARCH_PLACES = gql`
  query SearchPlaces($zone: String!, $searchText: String!) {
    searchPlaces(zone: $zone, searchText: $searchText) {
      id
      name
      desc
      type
      loc {
        lat
        lng
      }
      marker
      active
      url
      phone
      avatar
      categories
      categoryDocs {
        id
        title
        ctype
        iconName
      }
      zone
      createdAt
      updatedAt
      userId
    }
  }
`;

export const GET_PLACE_CATEGORIES = gql`
  query GetPlaceCategories($criteria: JSON!) {
    categories(criteria: $criteria) {
      id
      title
      ctype
      iconName
      zone
      createdAt
      updatedAt
    }
  }
`;

export const GET_PLACE_MEMBERS = gql`
  query GetPlaceMembers($placeId: String!) {
    placeMembers(placeId: $placeId) {
      id
      userProfile {
        firstName
        lastName
        avatar
        bio
      }
      roles
    }
  }
`;

export const CREATE_PLACE = gql`
  mutation CreatePlace($input: CreatePlaceInput!) {
    createPlace(input: $input) {
      id
      name
      desc
      type
      loc {
        lat
        lng
      }
      fullLocation {
        fullAddress
        street
        city
        state
        zip
        country
      }
      marker
      active
      url
      phone
      avatar
      categories
      categoryDocs {
        id
        title
        ctype
        iconName
      }
      zone
    }
  }
`;

export const UPDATE_PLACE = gql`
  mutation UpdatePlace($id: String!, $input: UpdatePlaceInput!) {
    updatePlace(id: $id, input: $input) {
      id
      name
      desc
      type
      loc {
        lat
        lng
      }
      fullLocation {
        fullAddress
        street
        city
        state
        zip
        country
      }
      marker
      active
      url
      phone
      avatar
      categories
      zone
    }
  }
`;

export const DELETE_PLACE = gql`
  mutation DeletePlace($id: String!) {
    deletePlace(id: $id)
  }
`;

export const GET_PLACE_WITH_RELATED = gql`
  query GetPlaceWithRelated($id: String!) {
    place(id: $id) {
      id
      name
      desc
      type
      loc {
        lat
        lng
      }
      fullLocation {
        fullAddress
        street
        city
        state
        zip
        country
      }
      marker
      active
      url
      phone
      avatar
      offerCards
      cardBackground

      categories
      zone
      createdAt
      updatedAt
      userId
      members {
        id
        userProfile {
          firstName
          lastName
          avatar
          bio
        }
        roles
      }
      likes {
        id
        userId
        linkedObjectId
        objectType
        createdAt
      }
      checks {
        id
        userId
        linkedObjectId
        objectType
        checkType
        createdAt
      }
      comments {
        id
        body
        userId
        createdAt
      }
      happenings {
        id
        title
        desc
        when {
        start
        end
        }
        avatar
        categories
        zone
        userId
        createdAt
      }
      offers {
        id
        title
        desc
        expires
        avatar
        maxRedeems
        categories
        zone
        userId
        createdAt
      }
      proximityads {
        id
        title
        desc
        active
        createdAt
        updatedAt
        userId
      }
    }
  }
`;

export const LIKE_PLACE = gql`
  mutation LikePlace($placeId: String!) {
    likePlace(placeId: $placeId) {
      id
      likes {
        id
        userId
        linkedObjectId
        objectType
        createdAt
      }
    }
  }
`;

export const UNLIKE_PLACE = gql`
  mutation UnlikePlace($placeId: String!) {
    unlikePlace(placeId: $placeId) {
      id
      likes {
        id
        userId
        linkedObjectId
        objectType
        createdAt
      }
    }
  }
`;

export const CHECKIN_PLACE = gql`
  mutation CheckinPlace($placeId: String!) {
    checkinPlace(placeId: $placeId) {
      id
      checks {
        id
        userId
        linkedObjectId
        objectType
        checkType
        createdAt
      }
    }
  }
`;

export const ADD_PLACE_COMMENT = gql`
  mutation AddPlaceComment($placeId: String!, $body: String!) {
    addPlaceComment(placeId: $placeId, body: $body) {
      id
      body
      userId
      createdAt
    }
  }
`;

export const CREATE_HAPPENING_FOR_PLACE = gql`
  mutation CreateHappeningForPlace($input: CreateHappeningInput!) {
    createHappening(input: $input) {
      id
      title
      desc
      when {
        start
        end
      }
      place
      avatar
      categories
      zone
      userId
    }
  }
`;

export const CREATE_OFFER_FOR_PLACE = gql`
  mutation CreateOfferForPlace($input: CreateOfferInput!) {
    createOffer(input: $input) {
      id
      title
      desc
      expires
      place
      avatar
      maxRedeems
      categories
      zone
      userId
    }
  }
`;

export const CREATE_PROXIMITY_AD = gql`
  mutation CreateProximityAd($input: CreateProximityAdInput!) {
    createProximityAd(input: $input) {
      id
      title
      desc
      active
      place
      userId
    }
  }
`;

// Get all happenings in a zone
export const GET_HAPPENINGS_BY_ZONE = gql`
  query GetHappeningsByZone($zoneId: String!) {
    happenings(criteria: { zone: $zoneId }) {
      id
      title
      desc
      when {
        start
        end
      }
      place
      avatar
      categories
      categoryDocs {
        id
        title
        ctype
        iconName
      }
      zone
      userId
      createdAt
    }
  }
`;

// Get all offers in a zone
export const GET_OFFERS_BY_ZONE = gql`
  query GetOffersByZone($zoneId: String!) {
    offers(criteria: { zone: $zoneId }) {
      id
      title
      desc
      expires
      place
      avatar
      maxRedeems
      categories
      categoryDocs {
        id
        title
        ctype
        iconName
      }
      zone
      userId
      createdAt
    }
  }
`;

// Get all proximity ads in a zone
export const GET_PROXIMITY_ADS_BY_ZONE = gql`
  query GetProximityAdsByZone($zoneId: String!) {
    proximityAds(criteria: { zone: $zoneId }) {
      id
      title
      desc
      active
      place
      createdAt
      updatedAt
      userId
    }
  }
`;
export const UPDATE_USER_PRESENCE = gql`
  mutation UpdateUserPresence($status: String!) {
    updateUserPresence(status: $status) {
      success
      message
    }
  }
`;
