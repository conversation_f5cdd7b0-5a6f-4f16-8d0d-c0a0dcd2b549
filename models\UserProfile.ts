// Base interface for the raw data
export interface UserProfileData {
  _id: string;  // Unique ObjectId for the profile
  id?: string;  // Kept for backward compatibility
  userId: string; // ID of the corresponding user
  avatar?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
}

// Class that wraps the data and provides methods
export class UserProfile {
  _id: string;  // Unique ObjectId for the profile
  id?: string;  // Kept for backward compatibility
  userId: string; // ID of the corresponding user
  avatar?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;

  constructor(data: UserProfileData) {
    this._id = data._id;
    this.id = data.id || data._id; // Fallback to _id for backward compatibility
    this.userId = data.userId;
    this.avatar = data.avatar;
    this.firstName = data.firstName;
    this.lastName = data.lastName;
    this.bio = data.bio;
  }

  // Method to get full name
  fullName(): string {
    if (this.firstName || this.lastName) {
      return `${this.firstName || ''} ${this.lastName || ''}`.trim();
    }
    return '';
  }

  // Method to get avatar URL
  avatarURL(size?: 'wide' | 'square'): string {
    // Get the default avatar from settings or use a fallback
    const defaultAvatar = process.env.NEXT_PUBLIC_DEFAULT_AVATAR || '/default-avatar.png';

    // Use the profile avatar or default
    let url = this.avatar || defaultAvatar;

    // Apply transformations based on size
    if (size === 'wide') {
      url = url.replace('/upload/', '/upload/c_fill,g_auto,h_320,w_1000/');
    } else if (size === 'square') {
      url = url.replace('/upload/', '/upload/c_fill,g_auto,h_400,w_400/');
    }

    return url;
  }
}