import { Category, Offer as OfferInterface } from '@/types';
import { Asset } from 'expo-asset';
/**
 * Offer class that implements the OfferInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Offer implements OfferInterface {
  id: string;
  title: string;
  desc?: string;
  price?: string;
  expires?: string;
  maxRedeems?: number;
  redeems?: number;
  place?: string;
  avatar?: string;
  categories?: string[];
  categoryDocs?: Category[];
  zone: string;
  userId: string;
  createdAt: string;
  updatedAt?: string;
  likes?: string[];
  comments?: string[];

  constructor(offerData: OfferInterface) {
    Object.assign(this, offerData);
  }

  /**
   * Get the URL for this offer
   * @returns The URL for the offer
   */
  linkToMe(): string {
    return `/offer/${this.id}`;
  }

  /**
   * Get the avatar URL for this offer
   * @returns The URL for the avatar image
   */
  avatarUrl(): string {
    if (this.avatar) {
      return this.ensureProperUrl(this.avatar);
    }
    // Return a default avatar if none is set
    return Asset.fromModule(require('../assets/images/offer-placeholder.png')).uri;
  }

  /**
   * Check if the offer has expired
   * @returns True if the offer expiration date is in the past
   */
  isExpired(): boolean {
    if (!this.expires) {
      return false;
    }
    
    const expirationDate = new Date(this.expires);
    const now = new Date();
    return expirationDate < now;
  }

  /**
   * Check if the offer has reached its maximum redemptions
   * @returns True if the offer has reached its maximum redemptions
   */
  isFullyRedeemed(): boolean {
    if (!this.maxRedeems || !this.redeems) {
      return false;
    }
    
    return this.redeems >= this.maxRedeems;
  }

  /**
   * Format the expiration date
   * @returns A formatted string with the expiration date
   */
  formattedExpirationDate(): string {
    if (!this.expires) {
      return 'No expiration';
    }
    
    const date = new Date(this.expires);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Get the icon name for this offer based on its categories
   * @returns The name of the icon to use for this offer
   */
  iconName(): string {
    // If no categories or categoryDocs, return a default icon
    if ((!this.categories || this.categories.length === 0) &&
        (!this.categoryDocs || this.categoryDocs.length === 0)) {
      return 'faTag';
    }

    // Try to get the icon from the first categoryDoc if available
    if (this.categoryDocs && this.categoryDocs.length > 0) {
      const firstCategoryDoc = this.categoryDocs[0];
      return firstCategoryDoc.iconName || 'faTag';
    }

    // Default fallback
    return 'faTag';
  }

  /**
   * Get a short excerpt of the description
   * @param length Maximum length of the excerpt
   * @returns A truncated version of the description
   */
  excerpt(length: number = 150): string {
    if (!this.desc) {
      return '';
    }
    
    // Remove HTML tags
    const textOnly = this.desc.replace(/<[^>]*>/g, '');
    
    if (textOnly.length <= length) {
      return textOnly;
    }
    
    // Truncate and add ellipsis
    return textOnly.substring(0, length) + '...';
  }

  /**
   * Ensure a URL has the proper protocol
   * @param url The URL to check
   * @returns The URL with proper protocol
   */
  private ensureProperUrl(url: string): string {
    if (!url) return url;

    // If it already has a protocol, return as is
    if (url.substring(0, 4) === 'http') {
      return url;
    }

    // Add // if needed
    if (url.substring(0, 2) !== '//') {
      url = '//' + url;
    }

    // Add https: protocol
    return 'https:' + url;
  }
}

/**
 * Factory function to create an Offer instance from a plain object
 * @param data The offer data
 * @returns A new Offer instance
 */
export function createOffer(data: OfferInterface): Offer {
  return new Offer(data);
}

/**
 * Factory function to create multiple Offer instances from an array
 * @param dataArray Array of offer data
 * @returns Array of Offer instances
 */
export function createOffers(dataArray: OfferInterface[]): Offer[] {
  return dataArray.map(data => createOffer(data));
}
