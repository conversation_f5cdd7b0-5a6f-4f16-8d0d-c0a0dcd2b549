import { useQuery } from '@apollo/client';
import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useZone } from '../../contexts/ZoneContext';
import { GET_CAMPAIGNS } from '../../lib/graphql-operations';
import { Campaign } from '../../types';

export default function CampaignsScreen() {
  const { currentZone, loading: zoneLoading } = useZone();
  const [refreshing, setRefreshing] = useState(false);

  // Query campaigns for the current zone
  const { data, loading, error, refetch } = useQuery(GET_CAMPAIGNS, {
    variables: {
      zone: currentZone?.id || '',
    },
    skip: !currentZone?.id,
  });

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing campaigns:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Format date range
  const formatDateRange = (starts: string, stops?: string) => {
    const start = new Date(starts);
    const startStr = start.toLocaleDateString();

    if (!stops) return `Started ${startStr}`;

    const end = new Date(stops);
    const stopstr = end.toLocaleDateString();

    return `${startStr} to ${stopstr}`;
  };

  // Render campaign item
  const renderCampaignItem = ({ item }: { item: Campaign }) => (
    <Card style={styles.campaignCard} onPress={() => router.push(`/campaign/${item.id}`)}>
      <ThemedText variant="subtitle">{item.title}</ThemedText>
      <ThemedText style={styles.campaignDescription} numberOfLines={2}>
        {item.desc}
      </ThemedText>
      <View style={styles.campaignFooter}>
        <ThemedText variant="caption">
          {formatDateRange(item.starts, item.stops)}
        </ThemedText>
        <ThemedText variant="caption">
          {item.challenges?.length || 0} challenges
        </ThemedText>
      </View>
    </Card>
  );

  // Loading state
  if (zoneLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={data?.campaigns || []}
        renderItem={renderCampaignItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        keyboardShouldPersistTaps="handled"
        scrollEnabled={true}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListHeaderComponent={
          <View>
            <Button
              title="Create New Campaign"
              onPress={() => router.push('/campaign/new')}
              style={styles.addButton}
            />
          </View>
        }
        ListEmptyComponent={
          loading ? (
            <ThemedView style={styles.emptyContainer}>
              <ActivityIndicator size="large" />
              <ThemedText style={styles.emptyText}>Loading campaigns...</ThemedText>
            </ThemedView>
          ) : error ? (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.errorText}>
                Error loading campaigns: {error.message}
              </ThemedText>
              <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
            </ThemedView>
          ) : (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.emptyText}>
                No campaigns found in this zone. Be the first to create one!
              </ThemedText>
              <Button
                title="Create Campaign"
                onPress={() => router.push('/campaign/new')}
                style={styles.createButton}
              />
            </ThemedView>
          )
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  listContent: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 16,
  },
  addButton: {
    marginBottom: 16,
  },
  campaignCard: {
    marginBottom: 16,
    padding: 16,
  },
  campaignDescription: {
    marginTop: 8,
    marginBottom: 8,
  },
  campaignFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  createButton: {
    marginTop: 8,
  },
});
