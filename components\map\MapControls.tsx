import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Zone } from '../../types';
import { Button } from '../ui/Button';

interface MapControlsProps {
  currentZone?: Zone;
  showNearbyPlaces: boolean;
  onCenterUserLocation: () => void;
  onToggleNearbyPlaces: () => void;
  onCenterZone: (zone: Zone) => void;
}

export function MapControls({
  currentZone,
  showNearbyPlaces,
  onCenterUserLocation,
  onToggleNearbyPlaces,
  onCenterZone,
}: MapControlsProps) {
  return (
    <View style={styles.container}>
      <Button
        title="My Location"
        onPress={onCenterUserLocation}
        variant="primary"
        size="small"
        style={styles.button}
      />

      <Button
        title={showNearbyPlaces ? "Show All Places" : "Show Nearby Places"}
        onPress={onToggleNearbyPlaces}
        variant="secondary"
        size="small"
        style={styles.button}
      />

      {currentZone && (
        <Button
          title={`Center on ${currentZone.title}`}
          onPress={() => onCenterZone(currentZone)}
          variant="outline"
          size="small"
          style={styles.button}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    flexDirection: 'column',
    gap: 8,
        pointerEvents: 'auto'

  },
  button: {
    borderRadius: 8,
    padding: 8,
    minWidth: 120,
  },
});
