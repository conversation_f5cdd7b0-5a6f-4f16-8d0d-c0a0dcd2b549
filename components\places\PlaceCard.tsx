import { Place, createPlace } from '@/models';
import { Place as PlaceInterface } from '@/types';
import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, useWindowDimensions } from 'react-native';
import { ImageListItem } from '../common/ImageListItem';
import { TeaseCard } from '../common/TeaseCard';

interface PlaceCardProps {
  place: PlaceInterface | Place;
  onPress?: () => void;
  forceVariant?: 'tease' | 'image'; // Optional override for testing
  numColumns?: number;
}

export function PlaceCard({ place: placeData, onPress, forceVariant, numColumns = 1 }: PlaceCardProps) {
  // Get window dimensions for responsive design
  const { width } = useWindowDimensions();
  
  // Convert to Place instance if it's not already one
  const place = placeData instanceof Place ? placeData : createPlace(placeData);

  // Handle place press
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(place.linkToMe());
    }
  };

  // Determine which variant to use based on screen width
  // Use image variant for larger screens to ensure grid layout works properly
  const useTeaseVariant = forceVariant ? forceVariant === 'tease' : width < 768;

  // Calculate item width based on number of columns
  const itemWidth = numColumns > 1 ? `${100 / numColumns - 2}%` : '100%';

  // For TeaseCard variant (smaller screens)
  if (useTeaseVariant) {
    return (
      <TeaseCard
        id={place.id}
        title={place.name || 'Unnamed Place'}
        linkPath={place.linkToMe()}
        iconName={place.iconName()}
        onPress={handlePress}
        style={{ width: itemWidth }}
      />
    );
  }

  // For ImageListItem variant (larger screens)
  return (
    <ImageListItem
      id={place.id}
      title={place.name || 'Unnamed Place'}
      description={place.desc}
      avatarURL={place.avatarUrl()}
      linkPath={place.linkToMe()}
      iconName={place.iconName()}
      onPress={handlePress}
      style={[styles.imageListItem, { width: itemWidth }]}
    />
  );
}

const styles = StyleSheet.create({
  imageListItem: {
    margin: 8,
  }
});
