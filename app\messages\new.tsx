import { useMutation, useQuery } from '@apollo/client';
import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Alert, FlatList, KeyboardAvoidingView, Platform, StyleSheet, TouchableOpacity } from 'react-native';
import { Avatar } from '../../components/ui/Avatar';
import { Button } from '../../components/ui/Button';
import { TextInput } from '../../components/ui/TextInput';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useTheme } from '../../contexts/ThemeContext';
import { CREATE_CONVERSATION, GET_USERS } from '../../lib/graphql-operations';

export default function NewMessageScreen() {
  const theme = useTheme();
  const [search, setSearch] = useState('');
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [message, setMessage] = useState('');

  // Query users
  const { data, loading, error } = useQuery(GET_USERS, {
    variables: { search: search || null },
  });

  // Create conversation mutation
  const [createConversation, { loading: createLoading }] = useMutation(CREATE_CONVERSATION, {
    onCompleted: (data) => {
      router.replace(`/messages/${data.createConversation.id}`);
    },
    onError: (error) => {
      Alert.alert('Error', `Failed to create conversation: ${error.message}`);
    },
  });

  // Handle user selection
  const handleSelectUser = (user: any) => {
    setSelectedUser(user);
  };

  // Handle send message
  const handleSendMessage = () => {
    if (!selectedUser || !message.trim()) {
      Alert.alert('Error', 'Please select a user and enter a message');
      return;
    }

    createConversation({
      variables: {
        input: {
          recipientId: selectedUser.id,
          initialMessage: message.trim(),
        },
      },
    });
  };

  // Render user item
  const renderUserItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[
        styles.userItem,
        selectedUser?.id === item.id && styles.selectedUserItem,
      ]}
      onPress={() => handleSelectUser(item)}
    >
      <Avatar
        source={item.userProfile.userProfile.avatar}
        name={item.userProfile ? 
          `${item.userProfile.firstName || ''} ${item.userProfile.lastName || ''}`.trim() :
          item?.email?.split('@')[0].address
        }
        online={item.isOnline}
        size="medium"
      />
      <ThemedView style={styles.userInfo}>
        <ThemedText variant="subtitle">{item.userProfile ? 
          `${item.userProfile.firstName || ''} ${item.userProfile.lastName || ''}`.trim() :
          item?.email?.split('@')[0].address
        }</ThemedText>
      </ThemedView>
    </TouchableOpacity>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      pointerEvents: 'auto',
    },
    header: {
      padding: 16,
    },
    subtitle: {
      marginTop: 8,
    },
    searchInput: {
      marginHorizontal: 16,
      marginBottom: 16,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 16,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorText: {
      color: theme.colors.error,
      textAlign: 'center',
    },
    userList: {
      padding: 16,
    },
    userItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    selectedUserItem: {
      backgroundColor: theme.colors.primary, // Replaced `primaryLight` with `primary`
    },
    userInfo: {
      marginLeft: 12,
    },
    emptyContainer: {
      padding: 24,
      alignItems: 'center',
    },
    emptyText: {
      textAlign: 'center',
    },
    messageContainer: {
      padding: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    selectedUserContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    messageInput: {
      height: 100,
      textAlignVertical: 'top',
    },
    sendButton: {
      marginTop: 16,
    },
  });

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container} // Ensure `styles` is referenced here
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <ThemedView style={styles.header}>
        <ThemedText variant="title">New Message</ThemedText>
        <ThemedText style={styles.subtitle}>
          Select a user to start a conversation
        </ThemedText>
      </ThemedView>

      <TextInput
        label="Search Users"
        value={search}
        onChangeText={setSearch}
        placeholder="Enter name or email"
        style={styles.searchInput}
      />

      {loading ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <ThemedText style={styles.loadingText}>Searching users...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>
            Error loading users: {error.message}
          </ThemedText>
        </ThemedView>
      ) : (
        <FlatList
          data={data?.users || []}
          renderItem={renderUserItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.userList}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}
          ListEmptyComponent={
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.emptyText}>
                {search
                  ? 'No users found matching your search'
                  : 'Start typing to search for users'}
              </ThemedText>
            </ThemedView>
          }
        />
      )}

      {selectedUser && (
        <ThemedView style={styles.messageContainer}>
          <ThemedView style={styles.selectedUserContainer}>
            <ThemedText variant="subtitle">To: {selectedUser?.userProfile ?
              `${selectedUser.userProfile.firstName || ''} ${selectedUser.userProfile.lastName || ''}`.trim() :
              selectedUser?.email?.split('@')[0]
            }</ThemedText>
            <Button
              title="Change"
              onPress={() => setSelectedUser(null)}
              variant="text"
              size="small"
            />
          </ThemedView>

          <TextInput
            label="Message"
            value={message}
            onChangeText={setMessage}
            placeholder="Type your message..."
            multiline
            numberOfLines={3}
            style={styles.messageInput}
          />

          <Button
            title={createLoading ? 'Sending...' : 'Send Message'}
            onPress={handleSendMessage}
            disabled={createLoading || !message.trim()}
            loading={createLoading}
            style={styles.sendButton}
          />
        </ThemedView>
      )}
    </KeyboardAvoidingView>
  );
}
