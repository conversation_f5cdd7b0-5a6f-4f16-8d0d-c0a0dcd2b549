import { useEffect, useRef, useState } from 'react';

// Types for proximity ads and checks
export type ProximityAd = {
  id: string;
  desc: string; // HTML bundle
  location: { lat: number; lng: number };
  showRule: 'first_time' | 'every_time' | 'first_time_nearby';
  expiration: string; // ISO date string
  checksCount: number;
};

export type Check = {
  userId: string;
  adId: string;
  checkType: 'prox';
  timestamp: string;
  location: { lat: number; lng: number };
};

// Helper: Haversine formula for distance in feet
function getDistanceFeet(lat1: number, lng1: number, lat2: number, lng2: number) {
  const toRad = (v: number) => (v * Math.PI) / 180;
  const R = 6371e3; // meters
  const dLat = toRad(lat2 - lat1);
  const dLng = toRad(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const meters = R * c;
  return meters * 3.28084; // feet
}

// Main hook
export function useProximityAds({
  userId,
  getUserLocation,
  proximityAds,
  getChecksForAd,
  recordCheck,
  incrementAdCheckCount,
  showAd,
}: {
  userId: string;
  getUserLocation: () => Promise<{ lat: number; lng: number }>;
  proximityAds: ProximityAd[];
  getChecksForAd: (adId: string) => Promise<Check[]>;
  recordCheck: (adId: string, location: { lat: number; lng: number }) => Promise<void>;
  incrementAdCheckCount: (adId: string) => Promise<void>;
  showAd: (ad: ProximityAd) => void;
}) {
  const lastShownRef = useRef<{ [adId: string]: string }>({});
  const [lastGlobalShown, setLastGlobalShown] = useState<string | null>(null);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    let cancelled = false;

    async function checkProximity() {
      const now = new Date();
      const userLoc = await getUserLocation();
      for (const ad of proximityAds) {
        if (new Date(ad.expiration) < now) continue;
        const dist = getDistanceFeet(userLoc.lat, userLoc.lng, ad.location.lat, ad.location.lng);
        if (dist > 50) continue;
        // Only one ad per hour
        if (lastGlobalShown && now.getTime() - new Date(lastGlobalShown).getTime() < 3600_000) continue;
        const checks = await getChecksForAd(ad.id);
        const hasSeen = checks.some(c => c.userId === userId);
        const hasSeenNearby = checks.some(c => c.userId === userId && c.location && getDistanceFeet(c.location.lat, c.location.lng, ad.location.lat, ad.location.lng) <= 50);
        let shouldShow = false;
        if (ad.showRule === 'every_time') shouldShow = true;
        else if (ad.showRule === 'first_time' && !hasSeen) shouldShow = true;
        else if (ad.showRule === 'first_time_nearby' && !hasSeenNearby) shouldShow = true;
        if (shouldShow) {
          showAd(ad);
          await recordCheck(ad.id, userLoc);
          await incrementAdCheckCount(ad.id);
          setLastGlobalShown(now.toISOString());
          break; // Only one ad per hour
        }
      }
    }

    interval = setInterval(() => {
      if (!cancelled) checkProximity();
    }, 60_000);
    checkProximity(); // initial
    return () => {
      cancelled = true;
      clearInterval(interval);
    };
  }, [userId, proximityAds]);
}
