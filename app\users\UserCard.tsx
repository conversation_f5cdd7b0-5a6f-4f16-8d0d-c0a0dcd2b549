'use client';

import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import { Alert, Image, StyleSheet, Text, TouchableOpacity, useWindowDimensions, View } from 'react-native';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useTheme } from '../../contexts/ThemeContext';

interface UserCardProps {
  user: any;
  isFriend?: boolean;
  isPending?: boolean;

  onSendRequest?: (userId: string) => void;
  onAcceptRequest?: (requestId: string) => void;
  onRejectRequest?: (requestId: string) => void;
  onRemoveFriend?: (friendId: string) => void;
  onBlockUser?: (userId: string) => void;
  onUnblockUser?: (userId: string) => void;
  requestId?: string;
}

export default function UserCard({
  user,
  isFriend,
  isPending,

  onSendRequest,
  onAcceptRequest,
  onRejectRequest,
  onRemoveFriend,
  onBlockUser,
  onUnblockUser,
  requestId,
}: UserCardProps) {
  const { width } = useWindowDimensions();
  const theme = useTheme();

  const handleSendRequest = () => {
    if (onSendRequest) {
      onSendRequest(user.id);
    }
  };

  const handleAcceptRequest = () => {
    if (onAcceptRequest && requestId) {
      onAcceptRequest(requestId);
    }
  };

  const handleRejectRequest = () => {
    if (onRejectRequest && requestId) {
      onRejectRequest(requestId);
    }
  };

  const handleRemoveFriend = () => {
    if (onRemoveFriend) {
      onRemoveFriend(user.id);
    }
  };

  const handleBlockUser = () => {
    if (onBlockUser) {
      Alert.alert(
        'Confirm Block',
        'Are you sure you want to block this user?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Block', style: 'destructive', onPress: () => onBlockUser(user.id) },
        ],
        { cancelable: true }
      );
    }
  };

  const handleUnblockUser = () => {
    if (onUnblockUser) {
      onUnblockUser(user.id);
    }
  };

  const navigateToProfile = () => {
    router.push(`/user/${user.id}`);
  };

  const styles = StyleSheet.create({
    card: {
      flexDirection: 'row',
      backgroundColor: 'white',
      borderRadius: 8,
      marginVertical: 8,
      padding: 12,
      elevation: 2,
      pointerEvents: 'auto',
    },
    avatarContainer: {
      marginRight: 12,
    },
    contentContainer: {
      flex: 1,
      pointerEvents: 'auto',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 6,
    },
    statusBadge: {
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 2,
    },
    statusText: {
      color: 'white',
      fontSize: 12,
    },
    bio: {
      fontSize: 14,
      marginBottom: 6,
    },
    lastOnline: {
      fontSize: 12,
      color: 'gray',
      marginBottom: 6,
    },
    chipContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 6,
    },
    chip: {
      backgroundColor: '#eee',
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
      marginRight: 6,
      marginBottom: 6,
    },
    chipText: {
      fontSize: 12,
      color: '#333',
    },
    actions: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 8,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 4,
      paddingHorizontal: 10,
      paddingVertical: 6,
      marginRight: 8,
      marginBottom: 8,
      backgroundColor: theme.colors.card,
    },
    actionText: {
      fontSize: 14,
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
    },
    primaryButtonText: {
      color: theme.colors.text,
    },
    acceptButton: {
      backgroundColor: theme.colors.success,
    },
    acceptButtonText: {
      color: theme.colors.text,
    },
    rejectButton: {
      backgroundColor: theme.colors.error,
    },
    rejectButtonText: {
      color: theme.colors.text,
    },
    removeButton: {
      backgroundColor: theme.colors.warning,
    },
    removeButtonText: {
      color: theme.colors.error,
    },
    unblockButton: {
      backgroundColor: theme.colors.success,
    },
    unblockButtonText: {
      color: theme.colors.text,
    },
    blockButton: {
      backgroundColor: theme.colors.error,
    },
    blockButtonText: {
      color: theme.colors.text,
    },
    imageContainer: {
      width: '100%',
      height: 180, // Taller image area to match web version
    },
    image: {
      width: '100%',
      height: '100%',
      backgroundColor: theme.colors.backgroundSecondary,
    },
    placeholderImage: {
      width: '100%',
      height: '100%',
      backgroundColor: theme.colors.backgroundSecondary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emojiIcon: {
      fontSize: 48, // Large emoji
    },
    title: {
      fontWeight: 'bold',
      marginBottom: 8,
      fontSize: 16,
      color: theme.colors.primary,
    },
  });

  return (
    <TouchableOpacity style={styles.card} onPress={navigateToProfile} activeOpacity={0.8}>
      <View style={styles.imageContainer}>
        {user.avatarURL() ? (
          <Image
            source={{ uri: user.avatarURL() }}
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <ThemedView style={styles.placeholderImage}>
            <ThemedText style={styles.emojiIcon}>👪</ThemedText>
          </ThemedView>
        )}
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.header}>
        <ThemedText style={styles.title} numberOfLines={1}>
          {user.fullName()}
        </ThemedText>
          <View style={[styles.statusBadge, { backgroundColor: user.status === 'online' ? 'green' : 'grey' }]}>
            <Text style={styles.statusText}>{user.status || 'offline'}</Text>
          </View>
        </View>
        {user.userProfile?.bio ? (
          <Text style={styles.bio} numberOfLines={3}>{user.userProfile.bio}</Text>
        ) : null}
        {user.lastOnline ? (
          <Text style={styles.lastOnline}>
            Last seen: {new Date(user.lastOnline).toLocaleString()}
          </Text>
        ) : null}
        {user.categories && user.categories.length > 0 ? (
          <View style={styles.chipContainer}>
            {user.categories.map((cat: string, index: number) => (
              <View key={index} style={styles.chip}>
                <Text style={styles.chipText}>{cat}</Text>
              </View>
            ))}
          </View>
        ) : null}
        <View style={styles.actions}>
          <TouchableOpacity style={styles.actionButton} onPress={navigateToProfile}>
            <Text style={styles.actionText}>View Profile</Text>
          </TouchableOpacity>
          {!isFriend && !isPending && onSendRequest && (
            <TouchableOpacity style={[styles.actionButton, styles.primaryButton]} onPress={handleSendRequest}>
              <Ionicons name="person-add" size={16} color="white" />
              <Text style={[styles.actionText, styles.primaryButtonText]}> Add Friend</Text>
            </TouchableOpacity>
          )}
          {isPending && onAcceptRequest && onRejectRequest && (
            <>
              <TouchableOpacity style={[styles.actionButton, styles.acceptButton]} onPress={handleAcceptRequest}>
                <Ionicons name="checkmark" size={16} color="white" />
                <Text style={[styles.actionText, styles.acceptButtonText]}> Accept</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.actionButton, styles.rejectButton]} onPress={handleRejectRequest}>
                <Ionicons name="close" size={16} color="white" />
                <Text style={[styles.actionText, styles.rejectButtonText]}> Reject</Text>
              </TouchableOpacity>
            </>
          )}
          {isFriend && onRemoveFriend && (
            <TouchableOpacity style={[styles.actionButton, styles.removeButton]} onPress={handleRemoveFriend}>
              <Ionicons name="person-remove" size={16} color="red" />
              <Text style={[styles.actionText, styles.removeButtonText]}> Remove Friend</Text>
            </TouchableOpacity>
          )}
          {user.blockedByMe && onUnblockUser && (
            <TouchableOpacity style={[styles.actionButton, styles.unblockButton]} onPress={handleUnblockUser}>
              <Ionicons name="close-circle" size={16} color="green" />
              <Text style={[styles.actionText, styles.unblockButtonText]}> Unblock</Text>
            </TouchableOpacity>
          )}
          {!user.blockedByMe && onBlockUser && (
            <TouchableOpacity style={[styles.actionButton, styles.blockButton]} onPress={handleBlockUser}>
              <Ionicons name="ban" size={16} color="red" />
              <Text style={[styles.actionText, styles.blockButtonText]}> Block</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
}
