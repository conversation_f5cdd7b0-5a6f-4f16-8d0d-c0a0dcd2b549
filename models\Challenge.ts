import { ChallengeAction, Challenge as ChallengeInterface, Completion } from '@/types/challenge';
import { Asset } from 'expo-asset';
/**
 * Challenge class that implements the ChallengeInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Challenge implements ChallengeInterface {
  id: string;
  title: string;
  desc?: string;
  avatar?: string;
  campaignId: string;
  action: ChallengeAction;
  points: number;
  answer?: string;
  zone: string;
  userId: string;
  createdAt: string;
  updatedAt?: string;
  likes?: {
    id: string;
    userId: string;
  }[];
  comments?: {
    id: string;
    body: string;
    userId: string;
    createdAt: string;
  }[];
  completions?: Completion[];

  constructor(challengeData: ChallengeInterface) {
    Object.assign(this, challengeData);
  }

  /**
   * Get the URL for this challenge
   * @returns The URL for the challenge
   */
  linkToMe(): string {
    return `/challenge/${this.id}`;
  }

  /**
   * Get the avatar URL for this challenge
   * @returns The URL for the avatar image
   */
  avatarUrl(): string {
    if (this.avatar) {
      return this.ensureProperUrl(this.avatar);
    }
    // Return a default avatar if none is set
    return Asset.fromModule(require('../assets/images/challenge-placeholder.png')).uri;
  }

  /**
   * Get the icon name for this challenge based on its action type
   * @returns The name of the icon to use for this challenge
   */
  iconName(): string {
    switch (this.action) {
      case 'check-in':
        return 'faMapMarkerAlt';
      case 'redeem':
        return 'faTicketAlt';
      case 'attend':
        return 'faCalendarCheck';
      case 'solve':
        return 'faPuzzlePiece';
      case 'photo':
        return 'faCamera';
      default:
        return 'faTrophy';
    }
  }

  /**
   * Get a human-readable label for the challenge action
   * @returns A formatted string describing the action
   */
  actionLabel(): string {
    switch (this.action) {
      case 'check-in':
        return 'Check in at a location';
      case 'redeem':
        return 'Redeem an offer';
      case 'attend':
        return 'Attend an event';
      case 'solve':
        return 'Solve a puzzle';
      case 'photo':
        return 'Take a photo';
      default:
        return 'Complete a challenge';
    }
  }

  /**
   * Check if a user has completed this challenge
   * @param userId The ID of the user to check
   * @returns True if the user has completed the challenge
   */
  isCompletedBy(userId: string): boolean {
    if (!this.completions || this.completions.length === 0) {
      return false;
    }
    
    return this.completions.some(completion => 
      completion.userId === userId && completion.correct
    );
  }

  /**
   * Get the completion for a specific user
   * @param userId The ID of the user
   * @returns The completion object or undefined if not found
   */
  getCompletionByUser(userId: string): Completion | undefined {
    if (!this.completions || this.completions.length === 0) {
      return undefined;
    }
    
    return this.completions.find(completion => completion.userId === userId);
  }

  /**
   * Get the total number of completions
   * @returns The number of completions
   */
  completionCount(): number {
    return this.completions ? this.completions.length : 0;
  }

  /**
   * Get a short excerpt of the description
   * @param length Maximum length of the excerpt
   * @returns A truncated version of the description
   */
  excerpt(length: number = 150): string {
    if (!this.desc) {
      return '';
    }
    
    // Remove HTML tags
    const textOnly = this.desc.replace(/<[^>]*>/g, '');
    
    if (textOnly.length <= length) {
      return textOnly;
    }
    
    // Truncate and add ellipsis
    return textOnly.substring(0, length) + '...';
  }

  /**
   * Ensure a URL has the proper protocol
   * @param url The URL to check
   * @returns The URL with proper protocol
   */
  private ensureProperUrl(url: string): string {
    if (!url) return url;

    // If it already has a protocol, return as is
    if (url.substring(0, 4) === 'http') {
      return url;
    }

    // Add // if needed
    if (url.substring(0, 2) !== '//') {
      url = '//' + url;
    }

    // Add https: protocol
    return 'https:' + url;
  }
}

/**
 * Factory function to create a Challenge instance from a plain object
 * @param data The challenge data
 * @returns A new Challenge instance
 */
export function createChallenge(data: ChallengeInterface): Challenge {
  return new Challenge(data);
}

/**
 * Factory function to create multiple Challenge instances from an array
 * @param dataArray Array of challenge data
 * @returns Array of Challenge instances
 */
export function createChallenges(dataArray: ChallengeInterface[]): Challenge[] {
  return dataArray.map(data => createChallenge(data));
}
