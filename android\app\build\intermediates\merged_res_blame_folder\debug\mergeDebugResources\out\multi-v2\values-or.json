{"logs": [{"outputFile": "com.townapp-mergeDebugResources-70:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1017,1082,1171,1236,1295,1381,1445,1509,1572,1645,1709,1763,1875,1933,1995,2049,2121,2243,2330,2406,2498,2580,2666,2806,2883,2964,3091,3182,3259,3313,3364,3430,3500,3577,3648,3723,3794,3871,3940,4009,4116,4207,4279,4368,4457,4531,4603,4689,4739,4818,4884,4964,5048,5110,5174,5237,5306,5406,5501,5593,5685,5743,5798,5882,5963,6038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "267,349,427,504,590,674,768,873,952,1012,1077,1166,1231,1290,1376,1440,1504,1567,1640,1704,1758,1870,1928,1990,2044,2116,2238,2325,2401,2493,2575,2661,2801,2878,2959,3086,3177,3254,3308,3359,3425,3495,3572,3643,3718,3789,3866,3935,4004,4111,4202,4274,4363,4452,4526,4598,4684,4734,4813,4879,4959,5043,5105,5169,5232,5301,5401,5496,5588,5680,5738,5793,5877,5958,6033,6108"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,69,70,71,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3074,3156,3234,3311,3397,4216,4310,4415,7092,7152,7217,8943,9008,9067,9153,9217,9281,9344,9417,9481,9535,9647,9705,9767,9821,9893,10015,10102,10178,10270,10352,10438,10578,10655,10736,10863,10954,11031,11085,11136,11202,11272,11349,11420,11495,11566,11643,11712,11781,11888,11979,12051,12140,12229,12303,12375,12461,12511,12590,12656,12736,12820,12882,12946,13009,13078,13178,13273,13365,13457,13515,13570,13744,13825,13900", "endLines": "5,33,34,35,36,37,45,46,47,69,70,71,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,147,148,149", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "317,3151,3229,3306,3392,3476,4305,4410,4489,7147,7212,7301,9003,9062,9148,9212,9276,9339,9412,9476,9530,9642,9700,9762,9816,9888,10010,10097,10173,10265,10347,10433,10573,10650,10731,10858,10949,11026,11080,11131,11197,11267,11344,11415,11490,11561,11638,11707,11776,11883,11974,12046,12135,12224,12298,12370,12456,12506,12585,12651,12731,12815,12877,12941,13004,13073,13173,13268,13360,13452,13510,13565,13649,13820,13895,13970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\27b4d8b3dc714a89d9adc0d83f296ead\\transformed\\biometric-1.1.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,263,381,508,637,773,904,1051,1153,1291,1435", "endColumns": "119,87,117,126,128,135,130,146,101,137,143,137", "endOffsets": "170,258,376,503,632,768,899,1046,1148,1286,1430,1568"}, "to": {"startLines": "66,68,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6774,7004,7633,7751,7878,8007,8143,8274,8421,8523,8661,8805", "endColumns": "119,87,117,126,128,135,130,146,101,137,143,137", "endOffsets": "6889,7087,7746,7873,8002,8138,8269,8416,8518,8656,8800,8938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "67,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6894,7306,7411,7524", "endColumns": "109,104,112,108", "endOffsets": "6999,7406,7519,7628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,326,433,519,623,743,822,903,994,1087,1188,1283,1383,1476,1571,1667,1758,1848,1937,2047,2151,2257,2368,2470,2588,2751,2857", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "211,321,428,514,618,738,817,898,989,1082,1183,1278,1378,1471,1566,1662,1753,1843,1932,2042,2146,2252,2363,2465,2583,2746,2852,2942"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,433,543,650,736,840,960,1039,1120,1211,1304,1405,1500,1600,1693,1788,1884,1975,2065,2154,2264,2368,2474,2585,2687,2805,2968,13654", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "428,538,645,731,835,955,1034,1115,1206,1299,1400,1495,1595,1688,1783,1879,1970,2060,2149,2259,2363,2469,2580,2682,2800,2963,3069,13739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,150", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3481,3584,3686,3789,3894,3995,4097,13975", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3579,3681,3784,3889,3990,4092,4211,14071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c6b7a95e3d9be8d22942c383e641eb5e\\transformed\\play-services-basement-18.3.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5529", "endColumns": "139", "endOffsets": "5664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ac05835035c583740d1a4f8d61f55f0\\transformed\\play-services-base-18.1.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4494,4605,4766,4898,5015,5170,5305,5419,5669,5836,5949,6110,6243,6393,6550,6615,6687", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "4600,4761,4893,5010,5165,5300,5414,5524,5831,5944,6105,6238,6388,6545,6610,6682,6769"}}]}]}