/**
 * Custom Babel plugin to transform graphql imports
 * This helps resolve issues with the graphql package in Metro bundler
 */
module.exports = function(babel) {
  const { types: t } = babel;
  
  return {
    name: 'transform-graphql-imports',
    visitor: {
      ImportDeclaration(path) {
        // Check if this is a graphql import
        if (path.node.source.value === 'graphql') {
          // Replace with our custom resolver
          path.node.source.value = '../graphql-resolver';
        }
        
        // Check if this is a graphql-tag import
        if (path.node.source.value === 'graphql-tag') {
          // Replace with our custom resolver
          path.node.source.value = '../graphql-tag-resolver';
        }
      },
      CallExpression(path) {
        // Check if this is a require('graphql')
        if (
          t.isIdentifier(path.node.callee, { name: 'require' }) &&
          path.node.arguments.length === 1 &&
          t.isStringLiteral(path.node.arguments[0]) &&
          path.node.arguments[0].value === 'graphql'
        ) {
          // Replace with our custom resolver
          path.node.arguments[0].value = '../graphql-resolver';
        }
        
        // Check if this is a require('graphql-tag')
        if (
          t.isIdentifier(path.node.callee, { name: 'require' }) &&
          path.node.arguments.length === 1 &&
          t.isStringLiteral(path.node.arguments[0]) &&
          path.node.arguments[0].value === 'graphql-tag'
        ) {
          // Replace with our custom resolver
          path.node.arguments[0].value = '../graphql-tag-resolver';
        }
      }
    }
  };
};
