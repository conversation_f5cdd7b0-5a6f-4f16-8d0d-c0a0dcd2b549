import { StyleSheet, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { User } from '../../models/User';
import { OptimizedImage } from '../common/OptimizedImage';
import { ThemedText } from './ThemedText';

interface AvatarProps {
  source?: string;
  size?: 'small' | 'medium' | 'large';
  name?: string;
  online?: boolean;
  user?: User; // Add user prop to handle profile data directly
}

// Helper function to get display name from user object
export function getDisplayName(user?: User): string {
  if (!user) return '';

  if (user.userProfile?.firstName) {
    return `${user.userProfile.firstName} ${user.userProfile.lastName || ''}`.trim();
  }

  // Fallbacks
  if (user.email) {
    return user.email.split('@')[0];
  }

  return '';
}

// Helper function to get avatar from user object
export function getAvatar(user?: User): string | undefined {
  return user?.avatarURL()};

export function Avatar({ source, size = 'medium', name, online, user }: AvatarProps) {
  const theme = useTheme();

  const sizeValues = {
    small: 32,
    medium: 48,
    large: 64,
  };

  const sizeValue = sizeValues[size];

  const styles = StyleSheet.create({
    container: {
      position: 'relative',
      pointerEvents: 'auto',
    },
    image: {
      backgroundColor: theme.colors.backgroundSecondary,
    },
    placeholder: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    initials: {
      color: theme.colors.text, // Replaced `textOnPrimary` with `text`
      fontWeight: 'bold',
    },
    onlineIndicator: {
      position: 'absolute',
      backgroundColor: theme.colors.success,
      borderWidth: 2,
      borderColor: theme.colors.background,
    },
  });

  const displayName = user ? getDisplayName(user) : name;
  const avatarSource = user ? getAvatar(user) : source;

  const initials = displayName
    ? displayName
        .split(' ')
        .map(part => part[0])
        .join('')
        .toUpperCase()
        .substring(0, 2)
    : '';

  return (
    <View style={styles.container}>
      {avatarSource ? (
        <OptimizedImage
          source={avatarSource}
          style={{ width: sizeValue, height: sizeValue, borderRadius: sizeValue / 2 }}
          priority="high"
          fallbackSource={`https://via.placeholder.com/${sizeValue}?text=${initials}`}
        />
      ) : (
        <View
          style={[
            styles.placeholder,
            {
              width: sizeValue,
              height: sizeValue,
              borderRadius: sizeValue / 2,
              backgroundColor: theme.colors.primary,
            },
          ]}
        >
          <ThemedText
            style={[
              styles.initials,
              { fontSize: sizeValue * 0.4 },
            ]}
          >
            {initials}
          </ThemedText>
        </View>
      )}

      {online && (
        <View
          style={[
            styles.onlineIndicator,
            {
              width: sizeValue * 0.25,
              height: sizeValue * 0.25,
              borderRadius: sizeValue * 0.125,
              right: 0,
              bottom: 0,
            },
          ]}
        />
      )}
    </View>
  );
}
