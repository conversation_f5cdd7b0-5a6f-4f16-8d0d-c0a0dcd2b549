# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Address {
  city: String
  country: String
  fullAddress: String
  geometry: JSON
  lat: Float
  lng: Float
  placeId: String
  state: String
  street: String
  zip: String
}

input AddressInput {
  city: String
  country: String
  fullAddress: String
  geometry: JSON
  lat: Float
  lng: Float
  placeId: String
  state: String
  street: String
  zip: String
}

type AuthPayload {
  access_token: String!
  user: User!
}

type Campaign {
  autoEnroll: Boolean
  avatar: String

  """The date when this entity was created"""
  createdAt: String
  desc: String

  """The unique identifier for this entity"""
  id: String!
  owner: User
  prize: String
  starts: DateTime
  stops: DateTime
  targetScore: Float
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
  winnerCount: Float
  zone: String!
}

type Category {
  active: Boolean

  """The date when this entity was created"""
  createdAt: String
  ctype: String!
  happenings: [Happening!]
  iconName: String

  """The unique identifier for this entity"""
  id: String!
  offers: [Offer!]
  parent: String
  places: [Place!]
  posts: [Post!]
  rank: Float
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  zone: String!
}

type Center {
  lat: Float!
  lng: Float!
}

input CenterInput {
  lat: Float!
  lng: Float!
}

type Challenge {
  action: String!
  answer: String
  avatar: String
  campaignId: String

  """The date when this entity was created"""
  createdAt: String
  desc: String

  """The unique identifier for this entity"""
  id: String!
  owner: User
  points: Float!
  target: String
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
}

type Check {
  checkType: String!

  """The date when this entity was created"""
  createdAt: String
  faveDetails: Fave
  happeningDetails: Happening

  """The unique identifier for this entity"""
  id: String!
  linkedObjectId: String!
  objectType: String!
  offerDetails: Offer
  owner: User
  placeDetails: Place
  postDetails: Post
  proximityAdDetails: ProximityAd

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
}

type Comment {
  body: String!
  campaign: Campaign
  childComments(limit: Int, skip: Int): [Comment!]
  commentCount: Float

  """The date when this entity was created"""
  createdAt: String
  fave: Fave
  happening: Happening

  """The unique identifier for this entity"""
  id: String!
  likeCount: Float
  likes(limit: Int, skip: Int): [Like!]

  """Dynamically resolved based on objectType"""
  linkedObject: JSON
  linkedObjectId: String!
  objectType: String!
  offer: Offer
  owner: User
  page: Page
  parentComment: Comment
  place: Place
  post: Post
  proximityAd: ProximityAd
  text: String

  """The date when this entity was last updated"""
  updatedAt: String
  user: User
  userId: String!
}

type Completion {
  answer: String
  challengeId: String!
  correct: Boolean

  """The date when this entity was created"""
  createdAt: String

  """The unique identifier for this entity"""
  id: String!
  link: String
  owner: User
  photo: String
  score: Float

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
}

input CreateCampaignInput {
  autoEnroll: Boolean
  avatar: String
  desc: String
  prize: String
  starts: DateTime
  stops: DateTime
  targetScore: Float
  title: String!
  userId: String
  winnerCount: Float
  zone: String!
}

input CreateCategoryInput {
  active: Boolean
  ctype: String!
  iconName: String
  parent: String
  rank: Float
  title: String!
  zone: String!
}

input CreateCheckInput {
  checkType: String!
  linkedObjectId: String!
  objectType: String!
  userId: String
}

input CreateCommentInput {
  body: String!
  commentCount: Float
  likeCount: Float
  linkedObjectId: String!
  objectType: String!
  text: String
  userId: String
}

input CreateCompletionInput {
  answer: String
  challengeId: String!
  correct: Boolean
  link: String
  photo: String
  score: Float
  userId: String
}

input CreateFriendInput {
  friendId: String!
  userId: String
}

input CreateGiftCardInput {
  amountReimbursed: Float!
  amountUsed: Float!
  comments: [String!]
  date: DateTime!
  faceAmt: Float!
  faceBalance: Float!
  holderId: String!
  isMovable: Boolean!
  likes: [String!]
  payDate: DateTime
  payTx: String
  payoutAmt: Float!
  place: String!
  placeId: String!
  receiverId: String!
  redeemerId: String!
  redemptions: [RedemptionInput!]
  status: String
  userId: String
}

input CreateHappeningInput {
  address: String
  avatar: String
  categories: [String!]
  comments: [String!]
  desc: String
  enddate: DateTime
  likes: [String!]
  place: String
  recurrence: RecurrenceInput
  title: String!
  userId: String
  when: WhenInput
  zone: String!
}

input CreateLikeInput {
  linkedObjectId: String!
  objectType: String!
  userId: String
}

input CreateMessageInput {
  body: String!
  conversationId: String!
  inFlight: Boolean
  userId: String
}

input CreatePageInput {
  body: String
  comments: [String!]
  fullScreen: Boolean
  likes: [String!]
  title: String!
  userId: String
  zone: String!
}

input CreatePlaceInput {
  active: Boolean
  alexaDesc: String
  avatar: String
  cardBackground: String
  categories: [String!]
  desc: String
  fullLocation: AddressInput
  geometry: GeometryInput
  loc: LocationInput
  marker: JSON
  members: [String!]
  name: String!
  offerCards: Boolean
  phone: String
  properties: JSON
  type: String! = "Feature"
  url: String
  wakeWords: String
  warn: String
  zone: String!
}

input CreateProfileInput {
  avatar: String
  bio: String
  email: String
  firstName: String
  lastName: String
  userId: String
  visible: Boolean
}

input CreateRequestInput {
  linkedObjectId: String!
  objectType: String!
  requesterId: String!
  type: String!
}

input CreateVisitInput {
  active: Boolean
  leftAt: DateTime
  place: String
  userId: String
}

input CreateZoneInput {
  appName: String
  center: CenterInput!
  giftcardHoldback: Float
  maxBusinesses: Int
  ownersAddPlaces: Boolean
  pagesMenuLabel: String
  showSubscribeBtn: Boolean
  signUpText: String
  subscriptionsEnabled: Boolean
  title: String!
  useGiftCards: Boolean
}

"""Date custom scalar type"""
scalar DateTime

type Email {
  address: String!
  verified: Boolean!
}

type Fave {
  avatar: String
  comments: [Comment!]

  """The date when this entity was created"""
  createdAt: String
  desc: String

  """The unique identifier for this entity"""
  id: String!
  likes: [Like!]
  owner: User
  place: String
  placeDetails: Place
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
  zone: String!
}

type Friend {
  """The date when this entity was created"""
  createdAt: String
  friendId: String!

  """The unique identifier for this entity"""
  id: String!
  owner: User

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
}

type Geometry {
  coordinates: JSON!
  geotype: String!
}

input GeometryInput {
  coordinates: JSON!
  geotype: String! = "Point"
}

type GiftCard {
  amountReimbursed: Float!
  amountUsed: Float!
  comments: [Comment!]

  """The date when this entity was created"""
  createdAt: String
  date: DateTime!
  faceAmt: Float!
  faceBalance: Float!
  holder: User
  holderId: String!

  """The unique identifier for this entity"""
  id: String!
  isMovable: Boolean!
  likes: [Like!]
  payDate: DateTime
  payTx: String
  payoutAmt: Float!
  place: String!
  placeId: String!
  receiverId: String!
  redeemerId: String!
  redemptions: [Redemption!]
  status: String

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
}

type Happening {
  address: String
  avatar: String
  categories: [String!]
  categoryDocs: [Category!]
  comments: [Comment!]

  """The date when this entity was created"""
  createdAt: String
  desc: String
  enddate: DateTime

  """The unique identifier for this entity"""
  id: String!
  likes: [Like!]
  owner: User
  place: String
  placeDetails: Place
  recurrence: JSON
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
  when: When
  zone: String!
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON

type Like {
  campaign: JSON
  comment: Comment

  """The date when this entity was created"""
  createdAt: String
  fave: Fave
  happening: Happening

  """The unique identifier for this entity"""
  id: String!

  """Dynamically resolved based on objectType"""
  linkedObject: JSON
  linkedObjectId: String!
  objectType: String!
  offer: Offer
  owner: User
  page: Page
  place: Place
  post: Post
  proximityAd: ProximityAd

  """The date when this entity was last updated"""
  updatedAt: String
  user: User
  userId: String!
}

type Location {
  lat: Float!
  lng: Float!
}

input LocationInput {
  lat: Float!
  lng: Float!
}

type Message {
  body: String!
  conversationId: String!

  """The date when this entity was created"""
  createdAt: String

  """The unique identifier for this entity"""
  id: String!
  inFlight: Boolean
  owner: User

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
}

type Mutation {
  completePasswordReset(newPassword: String!, token: String!): SuccessResponse!
  createCampaign(input: CreateCampaignInput!): Campaign!
  createCategory(input: CreateCategoryInput!): Category!
  createCheck(input: CreateCheckInput!): Check!
  createComment(input: CreateCommentInput!): Comment!
  createCompletion(input: CreateCompletionInput!): Completion!
  createFriend(input: CreateFriendInput!): Friend!
  createGiftCard(input: CreateGiftCardInput!): GiftCard!
  createHappening(input: CreateHappeningInput!): Happening!
  createLike(input: CreateLikeInput!): Like!
  createMessage(input: CreateMessageInput!): Message!
  createPage(input: CreatePageInput!): Page!
  createPlace(input: CreatePlaceInput!): Place!
  createProfile(input: CreateProfileInput!): Profile!
  createRequest(input: CreateRequestInput!): Request!
  createUser(email: String!, password: String!, zone: String!): AuthPayload!
  createVisit(input: CreateVisitInput!): Visit!
  createZone(input: CreateZoneInput!): Zone!
  deleteCampaign(id: String!): Boolean!
  deleteCategory(id: String!): Boolean!
  deleteCheck(id: String!): Boolean!
  deleteComment(id: String!): Boolean!
  deleteCompletion(id: String!): Boolean!
  deleteFriend(id: String!): Boolean!
  deleteGiftCard(id: String!): Boolean!
  deleteHappening(id: String!): Boolean!
  deleteLike(id: String!): Boolean!
  deleteMessage(id: String!): Boolean!
  deletePage(id: String!): Boolean!
  deletePlace(id: String!): Boolean!
  deleteProfile(id: String!): Boolean!
  deleteRequest(id: String!): Boolean!
  deleteVisit(id: String!): Boolean!
  deleteZone(id: String!): Boolean!
  resetPassword(email: String!): SuccessResponse!
  updateCampaign(id: String!, input: UpdateCampaignInput!): Campaign!
  updateCategory(id: String!, input: UpdateCategoryInput!): Category!
  updateCheck(id: String!, input: UpdateCheckInput!): Check!
  updateComment(id: String!, input: UpdateCommentInput!): Comment!
  updateCompletion(id: String!, input: UpdateCompletionInput!): Completion!
  updateFriend(id: String!, input: UpdateFriendInput!): Friend!
  updateGiftCard(id: String!, input: UpdateGiftCardInput!): GiftCard!
  updateHappening(id: String!, input: UpdateHappeningInput!): Happening!
  updateLike(id: String!, input: UpdateLikeInput!): Like!
  updateMessage(id: String!, input: UpdateMessageInput!): Message!
  updatePage(id: String!, input: UpdatePageInput!): Page!
  updatePlace(id: String!, input: UpdatePlaceInput!): Place!
  updateProfile(id: String!, input: UpdateProfileInput!): Profile!
  updateRequest(id: String!, input: UpdateRequestInput!): Request!
  updateVisit(id: String!, input: UpdateVisitInput!): Visit!
  updateZone(id: String!, input: UpdateZoneInput!): Zone!
  verifyEmail(token: String!): SuccessResponse!
}

type Offer {
  avatar: String
  categories: [String!]
  categoryDocs: [Category!]
  comments: [Comment!]

  """The date when this entity was created"""
  createdAt: String
  desc: String
  expires: DateTime

  """The unique identifier for this entity"""
  id: String!
  likes: [Like!]
  maxRedeems: Float
  owner: User
  place: String
  placeDetails: Place
  redemptions: [Check!]
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
  zone: String!
}

type Page {
  body: String
  comments: [Comment!]

  """The date when this entity was created"""
  createdAt: String
  fullScreen: Boolean

  """The unique identifier for this entity"""
  id: String!
  likes: [Like!]
  owner: User
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
  zone: String!
}

type PaymentMethod {
  expMonth: Float
  expYear: Float
  id: String!
  isDefault: Boolean
  last4: String
  type: String!
}

type Place {
  active: Boolean!
  alexaDesc: String
  avatar: String
  cardBackground: String
  categories: [String!]
  categoryDocs: [Category!]
  checkins: [Check!]
  checks: [Check!]
  comments(limit: Int, skip: Int): [Comment!]

  """The date when this entity was created"""
  createdAt: String
  desc: String
  faves: [Fave!]
  fullLocation: Address
  geometry: Geometry
  happenings: [Happening!]
  hist: [String!]

  """The unique identifier for this entity"""
  id: String!
  likes(limit: Int, skip: Int): [Like!]
  loc: Location
  marker: JSON
  members(limit: Int, skip: Int): [User!]
  name: String!
  offerCards: Boolean
  offers: [Offer!]
  owner: User
  phone: String
  posts: [Post!]
  properties: JSON
  proximityads: [ProximityAd!]
  source: JSON
  type: String!

  """The date when this entity was last updated"""
  updatedAt: String
  url: String
  userId: String
  visits: [Visit!]
  wakeWords: String
  warn: String
  zone: String
}

type Post {
  avatar: String
  categories: [String!]
  categoryDocs: [Category!]
  comments: [Comment!]

  """The date when this entity was created"""
  createdAt: String
  desc: String

  """The unique identifier for this entity"""
  id: String!
  likes: [Like!]
  owner: User
  place: String
  placeDetails: Place
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
  zone: String!
}

type Profile {
  avatar: String
  bio: String

  """The date when this entity was created"""
  createdAt: String
  email: String
  firstName: String

  """The unique identifier for this entity"""
  id: String!
  lastName: String

  """The date when this entity was last updated"""
  updatedAt: String
  user: User
  userId: String!
  visible: Boolean
}

type ProximityAd {
  active: Boolean
  avatar: String
  checks: [Check!]

  """The date when this entity was created"""
  createdAt: String
  desc: String

  """The unique identifier for this entity"""
  id: String!
  likes: [Like!]
  owner: User
  place: String
  placeDetails: Place
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
  whenShown: String!
}

type Query {
  campaign(id: String!): Campaign!
  campaigns(criteria: JSON, limit: Int, skip: Int, sort: JSON): [Campaign!]!
  categories(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Category!]!
  category(id: String!): Category!
  challenge(id: String!): Challenge!
  challenges(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Challenge!]!
  check(id: String!): Check!
  checks(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Check!]!
  comment(id: String!): Comment!
  comments(criteria: String, limit: Int, skip: Int, sort: String): [Comment!]!
  completion(id: String!): Completion!
  completions(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Completion!]!
  currentUser: User
  fave(id: String!): Fave!
  faves(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Fave!]!
  friend(id: String!): Friend!
  friends(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Friend!]!
  giftCard(id: String!): GiftCard!
  giftCards(criteria: JSON, limit: Float, skip: Float, sort: JSON): [GiftCard!]!
  happening(id: String!): Happening!
  happenings(criteria: JSON, limit: Float, skip: Float, sort: JSON, zone: String): [Happening!]!
  like(id: String!): Like!
  likes(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Like!]!
  message(id: String!): Message!
  messages(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Message!]!
  offer(id: String!): Offer!
  offers(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Offer!]!
  page(id: String!): Page!
  pages(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Page!]!
  place(id: String!): Place!
  places(criteria: JSON, limit: Int, skip: Int, sort: JSON): [Place!]!
  placesInPolygon(coordinates: [[JSON!]!]!, criteria: JSON, limit: Int, skip: Int, sort: JSON): [Place!]!
  post(id: String!): Post!
  posts(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Post!]!
  profile(id: String!): Profile!
  profileByUserId(userId: String!): Profile
  profiles(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Profile!]!
  proximityAd(id: String!): ProximityAd!
  proximityAds(criteria: JSON, limit: Float, skip: Float, sort: JSON): [ProximityAd!]!
  request(id: String!): Request!
  requests(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Request!]!
  user(id: String!): User
  users(criteria: JSON, limit: Float, skip: Float, sort: JSON): [User!]!
  visit(id: String!): Visit!
  visits(criteria: JSON, limit: Float, skip: Float, sort: JSON): [Visit!]!
  zone(id: String!): Zone
  zones(criteria: JSON, limit: Int, skip: Int, sort: JSON): [Zone!]!
}

input RecurrenceInput {
  byMonth: [Float!]
  byMonthDay: [Float!]
  byWeekday: [Float!]
  count: Float
  frequency: String!
  interval: Float!
  rrule: String
  until: String
}

type Redemption {
  amountReimbursed: Float!
  amountUsed: Float!
  date: DateTime!
  payDate: DateTime
  payTx: String
  place: String!
  receiverId: String!
  redeemerId: String!
  status: String!
}

input RedemptionInput {
  amountReimbursed: Float!
  amountUsed: Float!
  date: DateTime!
  payDate: DateTime
  payTx: String
  place: String!
  receiverId: String!
  redeemerId: String!
  status: String
}

type Request {
  """The date when this entity was created"""
  createdAt: String

  """The unique identifier for this entity"""
  id: String!
  linkedObjectId: String!
  objectType: String!
  requestee: User
  requester: User
  requesterId: String!
  type: String!

  """The date when this entity was last updated"""
  updatedAt: String
}

type RoleAssignment {
  """The date when this entity was created"""
  createdAt: String

  """The unique identifier for this entity"""
  id: String!
  inheritedRoles: [JSON!]
  role: JSON!
  scope: JSON

  """The date when this entity was last updated"""
  updatedAt: String
  user: JSON!
}

type StripeCustomer {
  created: DateTime
  email: String
  id: String!
  name: String
}

type StripePayments {
  customer: StripeCustomer
  paymentMethods: [PaymentMethod!]
  subscriptions: [StripeSubscription!]
}

type StripeSubscription {
  created: DateTime
  currentPeriodEnd: DateTime
  currentPeriodStart: DateTime
  id: String!
  plan: String
  status: String
}

type Subscription {
  newContent: String!
  zoneUpdates: String!
}

type SuccessResponse {
  message: String
  success: Boolean!
  token: String
}

input UpdateCampaignInput {
  autoEnroll: Boolean
  avatar: String
  desc: String
  prize: String
  starts: DateTime
  stops: DateTime
  targetScore: Float
  title: String
  userId: String
  winnerCount: Float
  zone: String
}

input UpdateCategoryInput {
  active: Boolean
  ctype: String
  iconName: String
  parent: String
  rank: Float
  title: String
  zone: String
}

input UpdateCheckInput {
  checkType: String
  linkedObjectId: String
  objectType: String
  userId: String
}

input UpdateCommentInput {
  body: String
  commentCount: Float
  likeCount: Float
  linkedObjectId: String
  objectType: String
  text: String
  userId: String
}

input UpdateCompletionInput {
  answer: String
  challengeId: String
  correct: Boolean
  link: String
  photo: String
  score: Float
  userId: String
}

input UpdateFriendInput {
  friendId: String
  userId: String
}

input UpdateGiftCardInput {
  amountReimbursed: Float
  amountUsed: Float
  comments: [String!]
  date: DateTime
  faceAmt: Float
  faceBalance: Float
  holderId: String
  isMovable: Boolean
  likes: [String!]
  payDate: DateTime
  payTx: String
  payoutAmt: Float
  place: String
  placeId: String
  receiverId: String
  redeemerId: String
  redemptions: [RedemptionInput!]
  status: String
  userId: String
}

input UpdateHappeningInput {
  address: String
  avatar: String
  categories: [String!]
  comments: [String!]
  desc: String
  likes: [String!]
  place: String
  title: String
  userId: String
  when: WhenInput
  zone: String
}

input UpdateLikeInput {
  linkedObjectId: String
  objectType: String
  userId: String
}

input UpdateMessageInput {
  body: String
  conversationId: String
  inFlight: Boolean
  userId: String
}

input UpdatePageInput {
  body: String
  comments: [String!]
  fullScreen: Boolean
  likes: [String!]
  title: String
  userId: String
  zone: String
}

input UpdatePlaceInput {
  active: Boolean
  alexaDesc: String
  avatar: String
  cardBackground: String
  categories: [String!]
  desc: String
  fullLocation: AddressInput
  geometry: GeometryInput
  loc: LocationInput
  marker: JSON
  members: [String!]
  name: String
  offerCards: Boolean
  phone: String
  properties: JSON
  type: String
  url: String
  wakeWords: String
  warn: String
}

input UpdateProfileInput {
  avatar: String
  bio: String
  email: String
  firstName: String
  lastName: String
  userId: String
  visible: Boolean
}

input UpdateRequestInput {
  linkedObjectId: String
  objectType: String
  requesterId: String
  type: String
}

input UpdateVisitInput {
  active: Boolean
  leftAt: DateTime
  place: String
  userId: String
}

input UpdateZoneInput {
  center: CenterInput
  rootPage: String
  signUpText: String
  skytext: String
  temp: String
  title: String
  weatherUpdatedAt: DateTime
  welcomePage: String
}

type User {
  """Whether the current user has blocked this user"""
  blockedByMe: Boolean!
  categories: [String!]
  categorieshist: [String!]

  """The date when this entity was created"""
  createdAt: String
  emails: [Email!]

  """Friends of this user"""
  friends(limit: Int, skip: Int): [User!]
  heartbeat: DateTime

  """The unique identifier for this entity"""
  id: String!
  lang: String
  lastOnline: DateTime
  lastPlace: String
  lastPlaceDetails: Place
  loc: Location
  loginIp: String
  mob: Boolean
  ownedCampaign: [Campaign!]
  ownedChallenges: [Challenge!]
  ownedChecks: [Check!]
  ownedComments: [Comment!]
  ownedCompletions: [Completion!]
  ownedFaves: [Fave!]
  ownedGiftCards: [GiftCard!]
  ownedHappenings: [Happening!]
  ownedLikes: [Like!]
  ownedMessages: [Message!]
  ownedOffers: [Offer!]
  ownedPages: [Page!]
  ownedPlaces: [Place!]
  ownedPosts: [Post!]
  ownedProximityAds: [ProximityAd!]
  placeMembership: [String!]
  placeMembershipDetails(limit: Int, skip: Int): Place
  registered_emails: [Email!]

  """Friend request from this user"""
  requestsFrom: [User!]

  """Friend request to this user"""
  requestsTo: [User!]
  roles: [String!]
  services: JSON
  settings: JSON
  status: String
  statusConnection: String
  statusDefault: String
  stripe: StripePayments

  """The date when this entity was last updated"""
  updatedAt: String
  userProfile: Profile
  visible: Boolean
  visits: [Visit!]
  zone: String!
}

type Visit {
  active: Boolean

  """The date when this entity was created"""
  createdAt: String

  """The unique identifier for this entity"""
  id: String!
  leftAt: DateTime
  place: String
  placeDetails: Place
  placeVisitor: User

  """The date when this entity was last updated"""
  updatedAt: String
  userId: String!
}

type When {
  end: DateTime
  start: DateTime!
}

input WhenInput {
  end: DateTime
  start: DateTime!
}

type Zone {
  appName: String
  center: Center

  """The date when this entity was created"""
  createdAt: String
  createdBy: String
  giftcardHoldback: Float

  """The unique identifier for this entity"""
  id: String!
  maxBusinesses: Int
  ownersAddPlaces: Boolean
  pagesMenuLabel: String
  rootPage: String
  showSubscribeBtn: Boolean
  signUpText: String
  skytext: String
  subscriptionCode: String
  subscriptionsEnabled: Boolean
  temp: String
  title: String!

  """The date when this entity was last updated"""
  updatedAt: String
  useGiftCards: Boolean
  weatherUpdatedAt: String
  welcomePage: String
}