# Town App - Deployment Strategy for Expo

## Overview

This document outlines the deployment strategy for the Expo version of the Town App. It covers the build process, distribution channels, and release management for iOS, Android, and web platforms.

## Build System

We'll use Expo Application Services (EAS) for building and distributing the application. EAS provides a cloud-based build service that simplifies the process of creating native binaries for iOS and Android.

### EAS Configuration

The `eas.json` file configures the build process:

```json
{
  "cli": {
    "version": ">= 5.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_GRAPHQL_URL": "http://localhost:3003/graphql"
      }
    },
    "preview": {
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_GRAPHQL_URL": "https://staging-api.townapp.com/graphql"
      }
    },
    "production": {
      "autoIncrement": true,
      "env": {
        "EXPO_PUBLIC_GRAPHQL_URL": "https://api.townapp.com/graphql"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "**********",
        "appleTeamId": "ABC123DEF456"
      },
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "production"
      }
    }
  }
}
```

## Build Environments

### Development

Development builds are used for local testing and development:

```bash
eas build --profile development --platform ios
eas build --profile development --platform android
```

Features:
- Development client enabled
- Debug tools available
- Connected to local or development API
- Internal distribution only

### Preview (Staging)

Preview builds are used for testing before production:

```bash
eas build --profile preview --platform ios
eas build --profile preview --platform android
```

Features:
- Connected to staging API
- Internal distribution for testing
- Similar to production build but with some debug capabilities
- Used for QA and stakeholder review

### Production

Production builds are used for public distribution:

```bash
eas build --profile production --platform ios
eas build --profile production --platform android
```

Features:
- Connected to production API
- Optimized for performance
- No debug tools
- Distributed through app stores

## Distribution Channels

### iOS

1. **TestFlight (Internal Testing)**
   - For development and preview builds
   - Limited to registered test users
   - Quick review process

2. **TestFlight (External Testing)**
   - For beta testing with external users
   - Limited to 10,000 users
   - Requires App Store review

3. **App Store**
   - For production releases
   - Available to all users
   - Requires full App Store review

### Android

1. **Internal Testing**
   - For development builds
   - Limited to specific testers
   - No review required

2. **Closed Testing**
   - For preview builds
   - Limited to specific test groups
   - No review required

3. **Open Testing**
   - For beta testing with external users
   - Available to users who opt-in
   - Limited review required

4. **Production**
   - For production releases
   - Available to all users
   - Requires Google Play review

### Web

1. **Development**
   - Deployed to development environment
   - Access restricted to development team

2. **Staging**
   - Deployed to staging environment
   - Access restricted to internal stakeholders

3. **Production**
   - Deployed to production environment
   - Publicly accessible

## Release Process

### Pre-Release Checklist

1. **Code Freeze**
   - Complete all feature development
   - Fix critical bugs
   - Update documentation

2. **Testing**
   - Run automated tests
   - Perform manual testing
   - Verify on multiple devices

3. **Version Update**
   - Update version number in app.json
   - Update changelog

4. **Build Configuration**
   - Verify environment variables
   - Check API endpoints
   - Configure analytics and monitoring

### Release Steps

1. **Build Production Version**
   ```bash
   eas build --profile production --platform all
   ```

2. **Submit to App Stores**
   ```bash
   eas submit --platform ios
   eas submit --platform android
   ```

3. **Deploy Web Version**
   ```bash
   eas build --platform web
   # Deploy the web build to hosting service
   ```

4. **Monitor Release**
   - Track crash reports
   - Monitor user feedback
   - Address critical issues

### Hotfix Process

For urgent fixes after a release:

1. Create a hotfix branch from the release tag
2. Implement and test the fix
3. Increment the patch version
4. Build and submit the hotfix
5. Merge the fix back to the main branch

## Continuous Integration/Continuous Deployment (CI/CD)

We'll use GitHub Actions for CI/CD:

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test

  build-preview:
    needs: test
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - name: Install dependencies
        run: npm ci
      - name: Build preview
        run: eas build --profile preview --platform all --non-interactive

  build-production:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - name: Install dependencies
        run: npm ci
      - name: Build production
        run: eas build --profile production --platform all --non-interactive
```

## App Store Optimization

### iOS App Store

1. **App Name**: Town App - Local Community
2. **Subtitle**: Discover Your Neighborhood
3. **Keywords**: community, local, events, places, social, neighborhood
4. **Description**: Highlight key features and benefits
5. **Screenshots**: Show main screens and features
6. **App Preview Video**: Demonstrate key user flows

### Google Play Store

1. **App Name**: Town App - Local Community
2. **Short Description**: Connect with your local community
3. **Full Description**: Detailed description with features and benefits
4. **Feature Graphic**: Eye-catching banner image
5. **Screenshots**: Show main screens and features
6. **Promo Video**: Demonstrate key user flows

## Implementation Status

### Completed

1. ✅ **Basic Configuration**
   - Set up app.json with proper configuration
   - Configured environment variables

### In Progress

1. 🔄 **Build System Setup**
   - Setting up EAS configuration
   - Creating build profiles

### Pending

1. ⏳ **CI/CD Pipeline**
   - GitHub Actions workflow setup
   - Automated testing and building

2. ⏳ **App Store Preparation**
   - Creating store listings
   - Preparing screenshots and videos

3. ⏳ **Release Management**
   - Version management strategy
   - Changelog tracking

## Next Steps

1. **Complete EAS Configuration**
   - Finalize build profiles
   - Set up submission configuration

2. **Set Up CI/CD Pipeline**
   - Configure GitHub Actions
   - Set up automated testing

3. **Prepare App Store Assets**
   - Create screenshots
   - Write descriptions
   - Design promotional graphics

4. **Create Release Plan**
   - Define release schedule
   - Set up version tracking
   - Create release checklist
