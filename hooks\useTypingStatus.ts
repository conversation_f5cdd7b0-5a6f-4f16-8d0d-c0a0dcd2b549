import { useMutation } from '@apollo/client';
import { useCallback, useEffect, useRef, useState } from 'react';
import { SET_TYPING_STATUS, TYPING_SUBSCRIPTION } from '../lib/graphql-operations';
import useReliableSubscription from './useSubscription';

// Typing timeout in milliseconds
const TYPING_TIMEOUT = 3000;

/**
 * Hook to manage typing status for a conversation
 * @param conversationId The ID of the conversation
 * @param userId The ID of the current user
 * @returns An object with typing status and methods to update it
 */
export function useTypingStatus(conversationId: string, userId: string) {
  // Track if the current user is typing
  const [isTyping, setIsTyping] = useState(false);
  
  // Track if the other user is typing
  const [isOtherUserTyping, setIsOtherUserTyping] = useState(false);
  const [otherUserInfo, setOtherUserInfo] = useState<{
    userId: string;
    username?: string;
  } | null>(null);
  
  // Timeout reference to clear typing status
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Last typing status sent to the server
  const lastTypingStatusRef = useRef(false);
  
  // Mutation to set typing status
  const [setTypingStatus] = useMutation(SET_TYPING_STATUS);
  
  // Subscribe to typing status updates
  const { data: typingData } = useReliableSubscription(
    TYPING_SUBSCRIPTION,
    { conversationId },
    (data) => {
      if (data?.typingStatus) {
        const { userId: typingUserId, isTyping: typingStatus } = data.typingStatus;
        
        // Only update if it's not the current user
        if (typingUserId !== userId) {
          setIsOtherUserTyping(typingStatus);
          setOtherUserInfo((prev) => ({
            ...prev,
            userId: typingUserId,
          }));
        }
      }
    }
  );
  
  // Update typing status on the server
  const updateTypingStatus = useCallback(
    async (status: boolean) => {
      // Only update if the status has changed
      if (lastTypingStatusRef.current !== status) {
        try {
          await setTypingStatus({
            variables: {
              conversationId,
              isTyping: status,
            },
          });
          
          lastTypingStatusRef.current = status;
        } catch (error) {
          console.error('Error updating typing status:', error);
        }
      }
    },
    [conversationId, setTypingStatus]
  );
  
  // Handle text input changes
  const handleTextChange = useCallback(
    (text: string) => {
      // Clear any existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }
      
      // Set typing status to true if there's text
      const newTypingStatus = text.length > 0;
      setIsTyping(newTypingStatus);
      
      // Update typing status on the server
      updateTypingStatus(newTypingStatus);
      
      // Set a timeout to clear typing status
      if (newTypingStatus) {
        typingTimeoutRef.current = setTimeout(() => {
          setIsTyping(false);
          updateTypingStatus(false);
        }, TYPING_TIMEOUT);
      }
    },
    [updateTypingStatus]
  );
  
  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Clear typing status when component unmounts
      updateTypingStatus(false);
    };
  }, [updateTypingStatus]);
  
  return {
    isTyping,
    isOtherUserTyping,
    otherUserInfo,
    handleTextChange,
    setOtherUserInfo,
  };
}

export default useTypingStatus;
