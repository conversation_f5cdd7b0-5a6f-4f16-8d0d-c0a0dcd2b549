# --- Builder Stage: Generates static assets ---
FROM node:20-alpine AS builder

WORKDIR /app

# Copy lock files first to leverage Docker caching for npm install
COPY package.json package-lock.json* pnpm-lock.yaml* ./
RUN npm install --frozen-lockfile

# Copy the rest of your application code
COPY . .

ENV NODE_ENV=production

# This command performs the static export based on your app.json `web.output: "server"`
# The output will be placed in the 'dist' directory.
RUN npx expo export -p web

# --- Runner Stage: Nginx Server ---
FROM nginx:alpine

# Copy the custom Nginx configuration file into the container
COPY nginx.conf /etc/nginx/nginx.conf

# Remove Nginx's default HTML content
RUN rm -rf /usr/share/nginx/html/*

# Copy the static assets from the builder stage into Nginx's serving directory
# IMPORTANT: You MUST verify this path.
# If `npx expo export -p web` puts your main index.html into a subfolder like `dist/server`,
# you would change this line to: `COPY --from=builder /app/dist/server /usr/share/nginx/html`
COPY --from=builder /app/dist /usr/share/nginx/html

# Expose port 8080, as configured in nginx.conf and expected by Fly.io
EXPOSE 8080

# Nginx alpine image typically runs Nginx by default as its CMD.
# No custom CMD is usually needed unless you want to pass specific arguments to Nginx.