import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Card } from '../ui/Card';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';
import { useTheme } from '../../contexts/ThemeContext';

interface TeaseCardProps {
  id: string;
  title: string;
  linkPath: string;
  iconName?: string;
  badge?: string;
  badgeColor?: string;
  onPress?: () => void;
}

export function TeaseCard({ 
  id, 
  title, 
  linkPath, 
  iconName, 
  badge, 
  badgeColor,
  onPress 
}: TeaseCardProps) {
  const theme = useTheme();
  
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(linkPath);
    }
  };

  return (
    <Card style={styles.card} elevation="small">
      <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
        <ThemedView style={styles.content}>
          {iconName && (
            <Ionicons 
              name={iconName as any} 
              size={20} 
              color={theme.colors.primary} 
              style={styles.icon} 
            />
          )}
          <ThemedText variant="subtitle" style={styles.title} numberOfLines={1}>
            {title}
          </ThemedText>
          {badge && (
            <ThemedView 
              style={[
                styles.badge, 
                { backgroundColor: badgeColor || theme.colors.primary }
              ]}
            >
              <ThemedText variant="caption" style={styles.badgeText}>
                {badge}
              </ThemedText>
            </ThemedView>
          )}
        </ThemedView>
      </TouchableOpacity>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    overflow: 'hidden',
  },
  content: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  title: {
    flex: 1,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
  },
});