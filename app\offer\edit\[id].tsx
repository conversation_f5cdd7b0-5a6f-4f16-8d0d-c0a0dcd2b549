import { useMutation, useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';

import { createUser } from '@/models/User';
import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { ThemedText } from '../../../components/ui/ThemedText';
import { ThemedView } from '../../../components/ui/ThemedView';
import { useAuth } from '../../../contexts/AuthContext';
import { useZone } from '../../../contexts/ZoneContext';
import { GET_CATEGORIES, GET_OFFER, GET_PLACES, UPDATE_OFFER } from '../../../lib/graphql-operations';
import { createCategories, createOffer, createPlaces } from '../../../models';

export default function EditOfferScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { currentZone, loading: zoneLoading } = useZone();
  const { user } = useAuth();
  
  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [endDate, setEndDate] = useState(new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000));
  const [selectedPlace, setSelectedPlace] = useState<string | null>(null);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [maxRedeems, setMaxRedeems] = useState('');
  const [price, setPrice] = useState('');

  // Query offer details
  const { data: offerData, loading: offerLoading, error: offerError } = useQuery(GET_OFFER, {
    variables: { id },
    skip: !id,
  });

  // Query categories
  const { data: categoriesData, loading: categoriesLoading } = useQuery(GET_CATEGORIES, {
    variables: {
      criteria: { 
        zone: currentZone?.id || '',
        ctype: { $in: ['offer', 'all'] }
      }
    },
    skip: !currentZone?.id,
  });

  // Query places
  const { data: placesData, loading: placesLoading } = useQuery(GET_PLACES, {
    variables: {
      criteria: { 
        zone: currentZone?.id || '',
      },
      limit: 100,
      skip: 0,
    },
    skip: !currentZone?.id,
  });

  // Update offer mutation
  const [updateOffer, { loading: updateLoading, error: updateError }] = useMutation(UPDATE_OFFER);

  // Process offer data
  const offer = offerData?.offer ? createOffer(offerData.offer) : null;
  const canEdit = user && ((user.id === offer?.userId) || createUser(user).isAdmin());

  // Process categories
  const categories = categoriesData?.categories 
    ? createCategories(categoriesData.categories)
    : [];

  // Process places
  const places = placesData?.places 
    ? createPlaces(placesData.places)
    : [];

  // Initialize form with offer data
  useEffect(() => {
    if (offer) {
      setTitle(offer.title);
      setDescription(offer.desc || '');
      
      if (offer.expires) {
        setEndDate(new Date(offer.expires));
      }
      
      if (offer.place) {
        setSelectedPlace(offer.place.id);
      }
      
      if (offer.categories && offer.categories.length > 0) {
        setSelectedCategories(offer.categories);
      }

      if (offer.maxRedeems) {
        setMaxRedeems(offer.maxRedeems.toString());
      }

      if (offer.price) {
        setPrice(offer.price);
      }
    }
  }, [offer]);

  // Handle category selection
  const toggleCategory = (categoryId: string) => {
    if (selectedCategories.includes(categoryId)) {
      setSelectedCategories(selectedCategories.filter(id => id !== categoryId));
    } else {
      setSelectedCategories([...selectedCategories, categoryId]);
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const onEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setEndDate(selectedDate);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a title for the offer');
      return;
    }

    if (!selectedPlace) {
      Alert.alert('Error', 'Please select a place for the offer');
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await updateOffer({
        variables: {
          id,
          input: {
            title,
            desc: description,
            expires: endDate.toISOString(),
            placeId: selectedPlace,
            categories: selectedCategories,
            maxRedeems: maxRedeems ? parseInt(maxRedeems) : undefined,
            price: price || undefined,
          }
        }
      });

      if (result.data?.updateOffer) {
        Alert.alert('Success', 'Offer updated successfully', [
          { text: 'OK', onPress: () => router.push(`/offer/${id}`) }
        ]);
      }
    } catch (error) {
      console.error('Error updating offer:', error);
      Alert.alert('Error', 'Failed to update offer. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check permissions
  useEffect(() => {
    if (offer && user && !canEdit) {
      Alert.alert('Permission Denied', 'You do not have permission to edit this offer', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    }
  }, [offer, user, canEdit]);

  // Loading state
  if (offerLoading || zoneLoading || categoriesLoading || placesLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (offerError) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading offer: {offerError.message}
        </ThemedText>
        <Button title="Go Back" onPress={() => router.back()} style={styles.errorButton} />
      </ThemedView>
    );
  }

  // Not found state
  if (!offer) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>Offer not found</ThemedText>
        <Button title="Go Back" onPress={() => router.back()} style={styles.errorButton} />
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}
    >
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <ThemedText variant="title" style={styles.screenTitle}>Edit Offer</ThemedText>

        <Card style={styles.formSection}>
          <ThemedText variant="subtitle">Offer Details</ThemedText>
          
          <ThemedText style={styles.label}>Title *</ThemedText>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="Enter offer title"
            maxLength={100}
          />

          <ThemedText style={styles.label}>Description</ThemedText>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Enter offer description"
            multiline
            numberOfLines={4}
            maxLength={1000}
          />

          <ThemedText style={styles.label}>Price</ThemedText>
          <TextInput
            style={styles.input}
            value={price}
            onChangeText={setPrice}
            placeholder="Enter price (optional)"
            keyboardType="default"
          />

          <ThemedText style={styles.label}>Maximum Redemptions</ThemedText>
          <TextInput
            style={styles.input}
            value={maxRedeems}
            onChangeText={(text) => setMaxRedeems(text.replace(/[^0-9]/g, ''))}
            placeholder="Enter maximum number of redemptions (optional)"
            keyboardType="numeric"
          />

          <ThemedText style={styles.label}>End Date *</ThemedText>
          <TouchableOpacity
            style={styles.datePickerButton}
            onPress={() => setShowEndDatePicker(true)}
          >
            <ThemedText>{formatDate(endDate)}</ThemedText>
            <Ionicons name="calendar-outline" size={20} color="#666" />
          </TouchableOpacity>

          {showEndDatePicker && (
            <DateTimePicker
              value={endDate}
              mode="date"
              display="default"
              onChange={onEndDateChange}
              minimumDate={new Date()}
            />
          )}
        </Card>

        <Card style={styles.formSection}>
          <ThemedText variant="subtitle">Location</ThemedText>
          <ThemedText style={styles.label}>Place *</ThemedText>
          <ScrollView style={styles.placesContainer} horizontal={false}>
            {places.map(place => (
              <TouchableOpacity
                key={place.id}
                style={[
                  styles.placeItem,
                  selectedPlace === place.id && styles.selectedPlaceItem
                ]}
                onPress={() => setSelectedPlace(place.id)}
              >
                <ThemedText style={styles.placeName}>{place.name}</ThemedText>
                {place.fullLocation && (
                  <ThemedText style={styles.placeAddress}>
                    {place.fullLocation.street}, {place.fullLocation.city}
                  </ThemedText>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </Card>

        <Card style={styles.formSection}>
          <ThemedText variant="subtitle">Categories</ThemedText>
          <ThemedText style={styles.helperText}>Select categories that apply to your offer</ThemedText>
          <View style={styles.categoriesContainer}>
            {categories.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryItem,
                  selectedCategories.includes(category.id) && styles.selectedCategoryItem
                ]}
                onPress={() => toggleCategory(category.id)}
              >
                <ThemedText
                  style={[
                    styles.categoryText,
                    selectedCategories.includes(category.id) && styles.selectedCategoryText
                  ]}
                >
                  {category.title}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={() => router.back()}
            style={styles.button}
          />
          <Button
            title={isSubmitting ? 'Saving...' : 'Save Changes'}
            onPress={handleSubmit}
            disabled={isSubmitting || !title || !selectedPlace}
            style={styles.button}
          />
        </View>

        {updateError && (
          <ThemedText style={styles.errorText}>
            Error: {updateError.message}
          </ThemedText>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorButton: {
    minWidth: 120,
  },
  screenTitle: {
    marginBottom: 16,
  },
  formSection: {
    marginBottom: 16,
    padding: 16,
  },
  label: {
    marginTop: 12,
    marginBottom: 4,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    marginBottom: 12,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 12,
    marginBottom: 12,
  },
  placesContainer: {
    maxHeight: 200,
    marginTop: 8,
  },
  placeItem: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    marginBottom: 8,
  },
  selectedPlaceItem: {
    borderColor: '#2196F3',
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  placeName: {
    fontWeight: '500',
  },
  placeAddress: {
    fontSize: 12,
    marginTop: 4,
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
    marginBottom: 8,
    opacity: 0.7,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  categoryItem: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedCategoryItem: {
    borderColor: '#2196F3',
    backgroundColor: '#2196F3',
  },
  categoryText: {
    fontSize: 12,
  },
  selectedCategoryText: {
    color: 'white',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
});
