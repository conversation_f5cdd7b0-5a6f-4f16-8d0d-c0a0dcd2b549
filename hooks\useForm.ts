import { useState, useCallback } from 'react';

interface FormErrors {
  [key: string]: string;
}

interface Validator {
  (value: any, formValues: any): string | null;
}

interface FieldValidators {
  [key: string]: Validator[];
}

export function useForm<T extends Record<string, any>>(initialValues: T, validators?: FieldValidators) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  
  // Handle change for a specific field
  const handleChange = useCallback((field: keyof T, value: any) => {
    setValues((prevValues) => ({
      ...prevValues,
      [field]: value,
    }));
    
    // Validate field if validators exist
    if (validators && validators[field as string]) {
      const fieldValidators = validators[field as string];
      let error: string | null = null;
      
      for (const validator of fieldValidators) {
        error = validator(value, values);
        if (error) break;
      }
      
      setErrors((prevErrors) => ({
        ...prevErrors,
        [field]: error,
      }));
    }
  }, [values, validators]);
  
  // Handle blur for a specific field
  const handleBlur = useCallback((field: keyof T) => {
    setTouched((prevTouched) => ({
      ...prevTouched,
      [field]: true,
    }));
    
    // Validate field if validators exist
    if (validators && validators[field as string]) {
      const fieldValidators = validators[field as string];
      let error: string | null = null;
      
      for (const validator of fieldValidators) {
        error = validator(values[field], values);
        if (error) break;
      }
      
      setErrors((prevErrors) => ({
        ...prevErrors,
        [field]: error,
      }));
    }
  }, [values, validators]);
  
  // Reset form to initial values
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  }, [initialValues]);
  
  // Set form values
  const setFormValues = useCallback((newValues: Partial<T>) => {
    setValues((prevValues) => ({
      ...prevValues,
      ...newValues,
    }));
  }, []);
  
  // Validate all fields
  const validateForm = useCallback((): boolean => {
    if (!validators) return true;
    
    const newErrors: FormErrors = {};
    let isValid = true;
    
    // Mark all fields as touched
    const newTouched: Record<string, boolean> = {};
    Object.keys(values).forEach((field) => {
      newTouched[field] = true;
    });
    setTouched(newTouched);
    
    // Validate all fields
    Object.keys(validators).forEach((field) => {
      const fieldValidators = validators[field];
      let error: string | null = null;
      
      for (const validator of fieldValidators) {
        error = validator(values[field as keyof T], values);
        if (error) {
          newErrors[field] = error;
          isValid = false;
          break;
        }
      }
    });
    
    setErrors(newErrors);
    return isValid;
  }, [values, validators]);
  
  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    resetForm,
    setFormValues,
    validateForm,
    setErrors,
  };
}

// Common validators
export const validators = {
  required: (value: any): string | null => {
    if (value === undefined || value === null || value === '') {
      return 'This field is required';
    }
    return null;
  },
  
  email: (value: string): string | null => {
    if (!value) return null;
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  },
  
  minLength: (length: number) => (value: string): string | null => {
    if (!value) return null;
    
    if (value.length < length) {
      return `Must be at least ${length} characters`;
    }
    return null;
  },
  
  maxLength: (length: number) => (value: string): string | null => {
    if (!value) return null;
    
    if (value.length > length) {
      return `Must be no more than ${length} characters`;
    }
    return null;
  },
  
  match: (field: string, fieldName: string) => (value: string, formValues: any): string | null => {
    if (!value) return null;
    
    if (value !== formValues[field]) {
      return `Must match ${fieldName}`;
    }
    return null;
  },
  
  number: (value: string): string | null => {
    if (!value) return null;
    
    if (isNaN(Number(value))) {
      return 'Must be a number';
    }
    return null;
  },
  
  min: (min: number) => (value: string): string | null => {
    if (!value) return null;
    
    const num = Number(value);
    if (isNaN(num) || num < min) {
      return `Must be at least ${min}`;
    }
    return null;
  },
  
  max: (max: number) => (value: string): string | null => {
    if (!value) return null;
    
    const num = Number(value);
    if (isNaN(num) || num > max) {
      return `Must be no more than ${max}`;
    }
    return null;
  },
};
