import { Text, TextProps } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface ThemedTextProps extends TextProps {
  variant?: 'title' | 'subtitle' | 'body' | 'caption' | 'button' | 'link';
}

export function ThemedText({ style, variant = 'body', ...props }: ThemedTextProps) {
  const theme = useTheme();
  
  const variantStyles = {
    title: {
      fontSize: theme.typography.sizes.xl,
      fontWeight: theme.typography.weights.bold,
      color: theme.colors.text,
      fontFamily: theme.typography.families.bold,
    },
    subtitle: {
      fontSize: theme.typography.sizes.large,
      fontWeight: theme.typography.weights.semibold,
      color: theme.colors.text,
      fontFamily: theme.typography.families.medium,
    },
    body: {
      fontSize: theme.typography.sizes.medium,
      color: theme.colors.text,
      fontFamily: theme.typography.families.regular,
    },
    caption: {
      fontSize: theme.typography.sizes.small,
      color: theme.colors.textSecondary,
      fontFamily: theme.typography.families.regular,
    },
    button: {
      fontSize: theme.typography.sizes.medium,
      fontWeight: theme.typography.weights.semibold,
      color: theme.colors.primary,
      fontFamily: theme.typography.families.medium,
    },
    link: {
      fontSize: theme.typography.sizes.medium,
      color: theme.colors.primary,
      fontFamily: theme.typography.families.regular,
      textDecorationLine: 'underline',
    },
  };
  
  return (
    <Text 
      style={[variantStyles[variant], style]} 
      {...props} 
    />
  );
}
