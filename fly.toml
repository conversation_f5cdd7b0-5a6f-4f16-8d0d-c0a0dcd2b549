# fly.toml app configuration file generated for townckt on 2025-05-19T17:13:50-04:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'townckt'
primary_region = 'iad'

[build]
  dockerfile = 'Dockerfile'

[env]
  NODE_ENV = 'production'
  PORT = '8080'
  EXPO_PUBLIC_INITIAL_ZONE = '5fac5dc69a25e8cfbe217c6b'
  ORS_KEY = '5b3ce3597851110001cf6248aeca65980e3a4ade8121177089b95f3d'
  EXPO_PUBLIC_INITIAL_LAT = '28.012019'
  EXPO_PUBLIC_INITIAL_LNG = '-82.787583'


[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

  [http_service.concurrency]
    type = 'connections'
    hard_limit = 1000
    soft_limit = 500

[[vm]]
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
