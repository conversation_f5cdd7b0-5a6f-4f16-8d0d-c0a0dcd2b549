import { useQuery } from '@apollo/client';
import { useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import { ActivityIndicator, RefreshControl, ScrollView, StyleSheet, useWindowDimensions } from 'react-native';
import RenderHtml from 'react-native-render-html';
import { Button } from '../../components/ui/Button';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useTheme } from '../../contexts/ThemeContext';
import { GET_PAGE } from '../../lib/graphql-operations';

export default function PageScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [refreshing, setRefreshing] = useState(false);
  const { width } = useWindowDimensions();
  const theme = useTheme();

  // Query page details
  const { data, loading, error, refetch } = useQuery(GET_PAGE, {
    variables: { id },
  });

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing page:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading page...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading page: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
      </ThemedView>
    );
  }

  const page = data?.page;

  if (!page) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>Page not found</ThemedText>
      </ThemedView>
    );
  }

  // Render HTML content using react-native-render-html
  const renderContent = () => {
    // If the content is HTML, render it with RenderHtml
    if (page.body && (page.body.includes('<') || page.body.includes('&'))) {
      const htmlContent = {
        html: page.body
      };

      // Define base style for the HTML content
      const tagsStyles: Record<string, any> = {
        body: {
          fontFamily: theme.typography?.families?.regular,
          color: theme.colors.text,
          fontSize: 16,
          lineHeight: 24,
        },
        p: {
          marginBottom: 16,
        },
        h1: {
          fontSize: 24,
          fontWeight: 'bold',
          marginVertical: 16,
          color: theme.colors.text,
        },
        h2: {
          fontSize: 20,
          fontWeight: 'bold',
          marginVertical: 12,
          color: theme.colors.text,
        },
        h3: {
          fontSize: 18,
          fontWeight: 'bold',
          marginVertical: 8,
          color: theme.colors.text,
        },
        a: {
          color: theme.colors.primary,
          textDecorationLine: 'underline',
        },
        img: {
          marginVertical: 16,
        },
        ul: {
          marginBottom: 16,
        },
        ol: {
          marginBottom: 16,
        },
        li: {
          marginBottom: 8,
        },
      };

      return (
        <RenderHtml
          contentWidth={width - 32} // Account for padding
          source={htmlContent}
          tagsStyles={tagsStyles}
        />
      );
    }

    // Fallback to plain text if not HTML
    return (
      <ThemedText style={styles.content}>
        {page.body}
      </ThemedText>
    );
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.bodyContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <ThemedView style={styles.header}>
        <ThemedText variant="title">{page.title}</ThemedText>
      </ThemedView>

      {renderContent()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    pointerEvents: 'auto',
    backgroundColor: '#fff',
  },
  bodyContainer: {
    padding: 16,
    backgroundColor: '#ffffff'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  header: {
    marginBottom: 16,
  },
  zone: {
    marginTop: 4,
  },
  body: {
    lineHeight: 24,
  },
});
