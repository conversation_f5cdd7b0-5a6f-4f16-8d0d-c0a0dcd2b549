import { Offer, createOffer } from '@/models';
import { useMutation, useQuery } from '@apollo/client';
import { useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Dimensions, Image, RefreshControl, ScrollView } from 'react-native';
import RenderHtml from 'react-native-render-html';
import { CommentsList } from '../../components/common/CommentsList';
import UniversalMapWrapper from '../../components/map/UniversalMapWrapper';
import { CheckInButton } from '../../components/social/CheckInButton';
import { LikeButton } from '../../components/social/LikeButton';
import { RedeemButton } from '../../components/social/RedeemButton';
import { RedemptionHistory } from '../../components/social/RedemptionHistory';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { ADD_COMMENT, GET_OFFER } from '../../lib/graphql-operations';
import { sharedStyles } from '../../styles/sharedStyles';
import { handleAddComment } from '../../utils/commentUtils'; // Import shared comment utility
import { getShareableUrl, shareContent } from '../../utils/sharing';

const { width } = Dimensions.get('window');

export default function OfferDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user } = useAuth(); // Added user context
  const [refreshing, setRefreshing] = useState(false);
  const [comment, setComment] = useState('');
  const theme = useTheme();

  // Query offer details
  const { data, loading, error, refetch } = useQuery<{ offer: Offer & { isLiked: boolean; isRedeemed: boolean; redeemers: { id: string }[]; place: { id: string; name: string; loc: { lat: number; lng: number }; address: string } } }>(GET_OFFER, {
    variables: { id },
  });

  // Add comment mutation
  const [addComment] = useMutation(ADD_COMMENT);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing offer:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Replace local function with shared utility
  const handleAddCommentWrapper = async () => {
    await handleAddComment(addComment, {
      linkedObjectId: id,
      objectType: 'offer',
      body: comment,
    }, (cache, newComment) => {
      const existingOffer = cache.readQuery({ query: GET_OFFER, variables: { id } });
      if (existingOffer?.offer) {
        cache.writeQuery({
          query: GET_OFFER,
          variables: { id },
          data: {
            offer: {
              ...existingOffer.offer,
              comments: [...(existingOffer.offer.comments || []), newComment],
            },
          },
        });
      }
    });
  };

  // Handle sharing
  const handleShare = async () => {
    if (!offer) return;

    const shareableUrl = getShareableUrl(offer.id);
    await shareContent({
      title: offer.title,
      message: offer.desc,
      url: shareableUrl,
    });
  };

  // Define openQRReader and showQRCode
  const openQRReader = () => {
    console.log('QR Reader opened');
  };

  const showQRCode = (url: string) => {
    console.log('Displaying QR Code for URL:', url);
  };

  // Redeem offer functionality
  const handleRedeem = () => {
    if (!user) {
      console.error('User is not authenticated');
      return;
    }

    if (user.roles?.includes('admin') || user.roles?.includes('placeOwner')) {
      openQRReader();
    } else {
      const redeemUrl = `/offer/${offer.id}/redeem/${user.id}`;
      showQRCode(redeemUrl);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Fix isValid function
  const isValid = (expires?: string): boolean => {
    const now = new Date();

    if (expires) {
      const end = new Date(expires);
      return now <= end;
    }
    return false;
  }

  // Calculate days remaining
  const getDaysRemaining = (expires?: string) => {
    if (!expires) return null;

    const end = new Date(expires);
    const now = new Date();

    if (end <= now) return 0;

    const diffTime = Math.abs(end.getTime() - now.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={sharedStyles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={sharedStyles.loadingText}>Loading offer...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={sharedStyles.errorContainer}>
        <ThemedText style={sharedStyles.errorText}>
          Error loading offer: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={onRefresh} style={sharedStyles.retryButton} />
      </ThemedView>
    );
  }

  // Add null checks for offer fields
  const offer = data?.offer instanceof Offer ? data?.offer : createOffer(data?.offer);

  if (!offer || !offer.id || !offer.title) {
    return (
      <ThemedView style={sharedStyles.errorContainer}>
        <ThemedText style={sharedStyles.errorText}>Offer details are incomplete or missing.</ThemedText>
      </ThemedView>
    );
  }

  const valid = isValid(offer.expires);
  const daysRemaining = getDaysRemaining(offer.expires);

  return (
    <ScrollView
      style={sharedStyles.container}
      contentContainerStyle={sharedStyles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {offer.avatar && (
        <Image
          source={{ uri: offer.avatarUrl() }}
          style={sharedStyles.avatar}
          resizeMode="cover"
        />
      )}

      <ThemedView style={sharedStyles.header}>
        <ThemedText variant="title">{offer.title}</ThemedText>

        <ThemedView style={sharedStyles.validityContainer}>
          {valid ? (
            <ThemedView style={[sharedStyles.validBadge, { backgroundColor: theme.colors.success + '20' }]}>
              <ThemedText
                variant="caption"
                style={[sharedStyles.validText, { color: theme.colors.success }]}
              >
                Active
              </ThemedText>
            </ThemedView>
          ) : (
            <ThemedView style={[sharedStyles.validBadge, { backgroundColor: theme.colors.error + '20' }]}>
              <ThemedText
                variant="caption"
                style={[sharedStyles.validText, { color: theme.colors.error }]}
              >
                Expired
              </ThemedText>
            </ThemedView>
          )}

          <ThemedText variant="caption" style={sharedStyles.date}>
            {offer.expires
              ? `Valid until ${formatDate(offer.expires)}`
              : ``}
          </ThemedText>
        </ThemedView>

        {daysRemaining !== null && daysRemaining > 0 && (
          <ThemedText variant="caption" style={sharedStyles.daysRemaining}>
            {daysRemaining} {daysRemaining === 1 ? 'day' : 'days'} remaining
          </ThemedText>
        )}

      </ThemedView>

      {offer.desc && (
        <Card style={sharedStyles.section}>
          <ThemedText variant="subtitle">About this Offer</ThemedText>
          <RenderHtml
            contentWidth={width}
            source={{ html: offer.desc }}
          />
        </Card>
      )}

      {offer.place && (
        <Card style={sharedStyles.section}>
          <ThemedText variant="subtitle">Available at</ThemedText>
          <ThemedText style={sharedStyles.placeName}>{offer.place.name}</ThemedText>
          {offer.place.address && (
            <ThemedText style={sharedStyles.address}>{offer.place.address}</ThemedText>
          )}
        </Card>
      )}

      {offer.place && (
        <Card style={sharedStyles.section}>
          {offer.place.loc && (
            <UniversalMapWrapper
              selectedCategory={null}
              markerPosition={[offer.place.loc.lat, offer.place.loc.lng]}
            />
          )}
        </Card>
      )}

      <ThemedView style={sharedStyles.actionsContainer}>
        <CheckInButton
          objectId={offer.id}
          objectType="offer"
          style={sharedStyles.actionButton}
        />
        <LikeButton
          objectId={offer.id}
          objectType="offer"
          isLiked={offer.isLiked || false}
          likeCount={offer.likes?.length || 0}
          style={sharedStyles.actionButton}
        />
        {/* Only show RedeemButton if user is placeOwner, placeMember, or user (logged in) */}
        {user && (user.isPlaceOwner(offer.place?.zone) || user.isPlaceMember(offer.place) || user.isUser()) && (
          <RedeemButton
            offerId={offer.id}
            isRedeemed={offer.isRedeemed}
            redeemCount={offer.redeems || 0}
            maxRedeems={offer.maxRedeems}
            isValid={valid}
            style={sharedStyles.actionButton}
            onPress={handleRedeem}
          />
        )}
        <Button
          title="Share"
          variant="outline"
          style={sharedStyles.actionButton}
          onPress={handleShare}
        />
        {/* TODO: Only show Edit/Delete/Assign-Member/See Metrics if user.isPlaceOwner(offer.place?.zone) or user.isAdmin() */}
      </ThemedView>

      {offer.redeemers && offer.redeemers.length > 0 && (
        <RedemptionHistory
          redeemers={offer.redeemers}
          maxRedeems={offer.maxRedeems}
        />
      )}

      <Card style={sharedStyles.section}>
        <ThemedText variant="subtitle">Comments ({offer.comments?.length || 0})</ThemedText>
        <CommentsList
          comments={offer.comments || []}
          onAddComment={handleAddCommentWrapper}
          comment={comment}
          setComment={setComment}
        />
      </Card>
    </ScrollView>
  );
}
