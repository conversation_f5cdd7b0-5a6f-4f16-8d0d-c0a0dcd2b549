import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNetworkStatus, useMutationQueue, useOfflineSupport } from '../../hooks/useOfflineSupport';

/**
 * Enhanced network status component with offline support features
 * Shows connection status and mutation queue information
 */
export function EnhancedNetworkStatus() {
  const { theme } = useTheme();
  const { isOnline, connectionType } = useNetworkStatus();
  const { queueLength } = useMutationQueue();
  const { syncMutations, isSyncing } = useOfflineSupport();

  // Don't show anything if online and no queued mutations
  if (isOnline && queueLength === 0) {
    return null;
  }

  const handleSyncPress = () => {
    if (!isSyncing) {
      syncMutations();
    }
  };

  const getStatusMessage = () => {
    if (!isOnline) {
      return `Offline (${connectionType})`;
    }
    
    if (queueLength > 0) {
      return `${queueLength} pending sync${queueLength !== 1 ? 's' : ''}`;
    }
    
    return 'Connected';
  };

  const getStatusColor = () => {
    if (!isOnline) {
      return theme.colors.error;
    }
    
    if (queueLength > 0) {
      return theme.colors.warning;
    }
    
    return theme.colors.success || theme.colors.primary;
  };

  const getTextColor = () => {
    if (!isOnline) {
      return theme.colors.onError;
    }
    
    if (queueLength > 0) {
      return theme.colors.onWarning || theme.colors.onPrimary;
    }
    
    return theme.colors.onSuccess || theme.colors.onPrimary;
  };

  return (
    <View style={[
      styles.container, 
      { backgroundColor: getStatusColor() }
    ]}>
      <View style={styles.content}>
        <Text style={[styles.text, { color: getTextColor() }]}>
          {getStatusMessage()}
        </Text>
        
        {isOnline && queueLength > 0 && (
          <TouchableOpacity 
            onPress={handleSyncPress}
            style={[styles.syncButton, { 
              backgroundColor: getTextColor(),
              opacity: isSyncing ? 0.6 : 1
            }]}
            disabled={isSyncing}
          >
            <Text style={[styles.syncText, { 
              color: getStatusColor()
            }]}>
              {isSyncing ? 'Syncing...' : 'Sync Now'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
      
      {!isOnline && (
        <Text style={[styles.subText, { color: getTextColor() }]}>
          Changes will sync when connection is restored
        </Text>
      )}
    </View>
  );
}

/**
 * Compact version for use in navigation bars or headers
 */
export function CompactNetworkStatus() {
  const { theme } = useTheme();
  const { isOnline } = useNetworkStatus();
  const { queueLength } = useMutationQueue();

  if (isOnline && queueLength === 0) {
    return null;
  }

  return (
    <View style={[
      styles.compactContainer,
      { 
        backgroundColor: isOnline 
          ? theme.colors.warning 
          : theme.colors.error 
      }
    ]}>
      <Text style={[
        styles.compactText,
        { 
          color: isOnline 
            ? theme.colors.onWarning || theme.colors.onPrimary
            : theme.colors.onError 
        }
      ]}>
        {isOnline ? `${queueLength}` : '⚠'}
      </Text>
    </View>
  );
}

/**
 * Floating network status indicator
 */
export function FloatingNetworkStatus() {
  const { theme } = useTheme();
  const { isOnline, connectionType } = useNetworkStatus();
  const { queueLength } = useMutationQueue();

  // Only show when offline or has queued mutations
  if (isOnline && queueLength === 0) {
    return null;
  }

  return (
    <View style={[
      styles.floatingContainer,
      { 
        backgroundColor: isOnline 
          ? theme.colors.warning 
          : theme.colors.error,
        shadowColor: theme.colors.shadow || '#000',
      }
    ]}>
      <Text style={[
        styles.floatingText,
        { 
          color: isOnline 
            ? theme.colors.onWarning || theme.colors.onPrimary
            : theme.colors.onError 
        }
      ]}>
        {isOnline ? `${queueLength} queued` : 'Offline'}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 40,
    justifyContent: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  text: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  subText: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.8,
  },
  syncButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  syncText: {
    fontSize: 12,
    fontWeight: '600',
  },
  compactContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  compactText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  floatingContainer: {
    position: 'absolute',
    bottom: 100,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  floatingText: {
    fontSize: 12,
    fontWeight: '600',
  },
});
