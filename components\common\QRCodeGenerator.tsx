import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Platform, Share, StyleSheet, TouchableOpacity, View } from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { useTheme } from '../../contexts/ThemeContext';
import { createDeepLink, createWebUrl, DeepLink } from '../../services/deepLinkingService';
import { Button } from '../ui/Button';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface QRCodeGeneratorProps {
  deepLink: DeepLink;
  title?: string;
  description?: string;
  size?: number;
  color?: string;
  backgroundColor?: string;
  logoSize?: number;
  showShareButton?: boolean;
  showCopyButton?: boolean;
  onPress?: () => void;
}

export function QRCodeGenerator({
  deepLink,
  title,
  description,
  size = 200,
  color,
  backgroundColor = '#FFFFFF',
  logoSize = 50,
  showShareButton = true,
  showCopyButton = true,
  onPress,
}: QRCodeGeneratorProps) {
  const theme = useTheme();
  const qrCodeColor = color || theme.colors.text;
  
  // Create the URL for the QR code
  const url = Platform.OS === 'web' ? createWebUrl(deepLink) : createDeepLink(deepLink);
  
  // Handle share button press
  const handleShare = async () => {
    try {
      const shareTitle = title || `Share ${deepLink.type}`;
      const shareMessage = description || `Check out this ${deepLink.type} on Town App!`;
      
      await Share.share({
        message: `${shareMessage} ${url}`,
        url,
        title: shareTitle,
      });
    } catch (error) {
      console.error('Error sharing QR code:', error);
    }
  };
  
  // Handle copy button press
  const handleCopy = () => {
    if (navigator?.clipboard) {
      navigator.clipboard.writeText(url);
      // You might want to show a toast or some other feedback here
    }
  };
  
  // Get the logo based on the deep link type
  const getLogo = () => {
    switch (deepLink.type) {
      case 'post':
        return require('../../assets/images/post-icon.png');
      case 'place':
        return require('../../assets/images/place-icon.png');
      case 'happening':
        return require('../../assets/images/happening-icon.png');
      case 'offer':
        return require('../../assets/images/offer-icon.png');
      case 'profile':
        return require('../../assets/images/profile-icon.png');
      default:
        return require('../../assets/images/logo.png');
    }
  };
  
  return (
    <ThemedView style={styles.container}>
      {title && <ThemedText style={styles.title}>{title}</ThemedText>}
      
      <TouchableOpacity
        style={[styles.qrCodeContainer, { backgroundColor }]}
        onPress={onPress}
        activeOpacity={onPress ? 0.7 : 1}
      >
        <QRCode
          value={url}
          size={size}
          color={qrCodeColor}
          backgroundColor={backgroundColor}
          logo={getLogo()}
          logoSize={logoSize}
          logoBackgroundColor={backgroundColor}
          logoBorderRadius={10}
        />
      </TouchableOpacity>
      
      {description && (
        <ThemedText style={styles.description}>{description}</ThemedText>
      )}
      
      {(showShareButton || showCopyButton) && (
        <View style={styles.buttonContainer}>
          {showShareButton && (
            <Button
              title="Share"
              onPress={handleShare}
              leftIcon={<Ionicons name="share-outline" size={20} color="#fff" />}
              style={styles.button}
            />
          )}
          
          {showCopyButton && Platform.OS === 'web' && (
            <Button
              title="Copy Link"
              onPress={handleCopy}
              leftIcon={<Ionicons name="copy-outline" size={20} color="#fff" />}
              style={styles.button}
              variant="secondary"
            />
          )}
        </View>
      )}
      
      <ThemedText style={styles.url}>{url}</ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 16,
        pointerEvents: 'auto'

  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  qrCodeContainer: {
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  description: {
    marginTop: 16,
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 16,
  },
  button: {
    marginHorizontal: 8,
  },
  url: {
    marginTop: 16,
    fontSize: 12,
    color: '#888',
    textAlign: 'center',
  },
});
