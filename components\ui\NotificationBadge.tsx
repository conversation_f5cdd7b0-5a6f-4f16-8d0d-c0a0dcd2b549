import React from 'react';
import { StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { useTheme } from '../../contexts/ThemeContext';

interface NotificationBadgeProps {
  count: number;
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
  textStyle?: TextStyle;
  maxCount?: number;
}

/**
 * A notification badge component that displays a count
 */
export function NotificationBadge({
  count,
  size = 'medium',
  style,
  textStyle,
  maxCount = 99,
}: NotificationBadgeProps) {
  const { theme } = useTheme();
  
  // Don't render if count is 0
  if (count <= 0) {
    return null;
  }
  
  // Format the count to display
  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();
  
  // Determine size dimensions
  const sizeStyles = {
    small: {
      minWidth: 16,
      height: 16,
      borderRadius: 8,
      fontSize: 10,
      paddingHorizontal: 4,
    },
    medium: {
      minWidth: 20,
      height: 20,
      borderRadius: 10,
      fontSize: 12,
      paddingHorizontal: 5,
    },
    large: {
      minWidth: 24,
      height: 24,
      borderRadius: 12,
      fontSize: 14,
      paddingHorizontal: 6,
    },
  };
  
  return (
    <ThemedView
      style={[
        styles.badge,
        { backgroundColor: theme.colors.notification },
        sizeStyles[size],
        style,
      ]}
    >
      <ThemedText
        style={[
          styles.text,
          { fontSize: sizeStyles[size].fontSize },
          textStyle,
        ]}
      >
        {displayCount}
      </ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  badge: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    color: 'white',
    fontWeight: 'bold',
  },
});
