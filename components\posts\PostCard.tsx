import { Post, createPost } from '@/models';
import { Post as PostInterface } from '@/types';
import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, useWindowDimensions } from 'react-native';
import { ImageListItem } from '../common/ImageListItem';
import { TeaseCard } from '../common/TeaseCard';

interface PostCardProps {
  post: PostInterface | Post;
  onPress?: () => void;
  forceVariant?: 'tease' | 'image';
  numColumns?: number;
}

export function PostCard({ post: postData, onPress, forceVariant, numColumns = 1 }: PostCardProps) {
  const { width } = useWindowDimensions();
  const post = postData instanceof Post ? postData : createPost(postData);

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(`/post/${post.id}`);
    }
  };

  const useTeaseVariant = forceVariant ? forceVariant === 'tease' : width < 768;
  const itemWidth = numColumns > 1 ? `${100 / numColumns - 2}%` : '100%';

  if (useTeaseVariant) {
    return (
      <TeaseCard
        id={post.id}
        title={post.title}
        linkPath={post.linkToMe()}
        onPress={handlePress}
        style={{ width: itemWidth }}
      />
    );
  }

  return (
    <ImageListItem
      id={post.id}
      title={post.title}
      description={post.desc}
      avatarURL={post.avatarUrl()}
      linkPath={post.linkToMe()}
      onPress={handlePress}
      style={[styles.imageListItem, { width: itemWidth }]}
    />
  );
}

const styles = StyleSheet.create({
  imageListItem: {
    margin: 8,
  },
});

