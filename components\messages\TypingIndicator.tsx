import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface TypingIndicatorProps {
  username?: string;
  isVisible: boolean;
}

/**
 * A component that displays a typing indicator animation
 */
const TypingIndicator: React.FC<TypingIndicatorProps> = ({ 
  username, 
  isVisible 
}) => {
  const { theme } = useTheme();
  const dot1Opacity = useRef(new Animated.Value(0)).current;
  const dot2Opacity = useRef(new Animated.Value(0)).current;
  const dot3Opacity = useRef(new Animated.Value(0)).current;

  // Animate the dots
  useEffect(() => {
    if (isVisible) {
      // Start the animation sequence
      const animateDots = () => {
        Animated.sequence([
          // Dot 1 animation
          Animated.timing(dot1Opacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          // Dot 2 animation
          Animated.timing(dot2Opacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          // Dot 3 animation
          Animated.timing(dot3Opacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          // Pause at the end
          Animated.delay(300),
          // Reset all dots
          Animated.parallel([
            Animated.timing(dot1Opacity, {
              toValue: 0,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(dot2Opacity, {
              toValue: 0,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(dot3Opacity, {
              toValue: 0,
              duration: 200,
              useNativeDriver: true,
            }),
          ]),
          // Pause before restarting
          Animated.delay(200),
        ]).start(() => {
          // Restart the animation
          if (isVisible) {
            animateDots();
          }
        });
      };

      // Start the animation
      animateDots();
    } else {
      // Reset the animation when not visible
      dot1Opacity.setValue(0);
      dot2Opacity.setValue(0);
      dot3Opacity.setValue(0);
    }

    // Clean up the animation when the component unmounts
    return () => {
      dot1Opacity.stopAnimation();
      dot2Opacity.stopAnimation();
      dot3Opacity.stopAnimation();
    };
  }, [isVisible, dot1Opacity, dot2Opacity, dot3Opacity]);

  if (!isVisible) {
    return null;
  }

  return (
    <ThemedView style={styles.container}>
      {username && (
        <ThemedText style={styles.text}>
          {username} is typing
        </ThemedText>
      )}
      <View style={styles.dotsContainer}>
        <Animated.View 
          style={[
            styles.dot, 
            { backgroundColor: theme.colors.text, opacity: dot1Opacity }
          ]} 
        />
        <Animated.View 
          style={[
            styles.dot, 
            { backgroundColor: theme.colors.text, opacity: dot2Opacity }
          ]} 
        />
        <Animated.View 
          style={[
            styles.dot, 
            { backgroundColor: theme.colors.text, opacity: dot3Opacity }
          ]} 
        />
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    paddingHorizontal: 16,
        pointerEvents: 'auto'

  },
  text: {
    fontSize: 12,
    fontStyle: 'italic',
    marginRight: 8,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
        pointerEvents: 'auto'

  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 2,
  },
});

export default TypingIndicator;
