import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { formatRelativeTime } from '../../utils/date';
import { Avatar } from '../ui/Avatar';
import { Card } from '../ui/Card';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface Redeemer {
  id: string;
  emails?: {
    address: string;
    verified: boolean;
  }[];
  userProfile?: {
    id: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
  };
  createdAt?: string;
}

interface RedemptionHistoryProps {
  redeemers: Redeemer[];
  maxRedeems?: number;
}

export function RedemptionHistory({ redeemers, maxRedeems }: RedemptionHistoryProps) {
  if (!redeemers || redeemers.length === 0) {
    return (
      <Card style={styles.container}>
        <ThemedText variant="subtitle">Redemption History</ThemedText>
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.emptyText}>No redemptions yet</ThemedText>
        </ThemedView>
      </Card>
    );
  }

  // Sort redeemers by createdAt date (most recent first)
  const sortedRedeemers = [...redeemers].sort((a, b) => {
    if (!a.createdAt || !b.createdAt) return 0;
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  return (
    <Card style={styles.container}>
      <ThemedText variant="subtitle">
        Redemption History {maxRedeems ? `(${redeemers.length}/${maxRedeems})` : `(${redeemers.length})`}
      </ThemedText>
      
      <FlatList
        data={sortedRedeemers}
        keyExtractor={(item) => item.id}
        keyboardShouldPersistTaps="handled"
        scrollEnabled={true}
        renderItem={({ item }) => (
          <ThemedView style={styles.redeemerItem}>
            <Avatar
              source={item.userProfile?.avatar}
              name={getUserName(item)}
              size="small"
            />
            <View style={styles.redeemerInfo}>
              <ThemedText style={styles.redeemerName}>{getUserName(item)}</ThemedText>
              {item.createdAt && (
                <ThemedText style={styles.redeemerTime}>
                  {formatRelativeTime(new Date(item.createdAt))}
                </ThemedText>
              )}
            </View>
          </ThemedView>
        )}
        style={styles.list}
      />
    </Card>
  );
}

// Helper function to get user's name
function getUserName(user: Redeemer): string {
  if (user.userProfile?.firstName || user.userProfile?.lastName) {
    return `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim();
  }
  
  if (user.emails && user.emails.length > 0) {
    return user.emails[0].address.split('@')[0];
  }
  
  return 'Unknown User';
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    padding: 16,
        pointerEvents: 'auto'

  },
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    opacity: 0.6,
  },
  list: {
    marginTop: 8,
  },
  redeemerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#ccc',
  },
  redeemerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  redeemerName: {
    fontWeight: '500',
  },
  redeemerTime: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
});
