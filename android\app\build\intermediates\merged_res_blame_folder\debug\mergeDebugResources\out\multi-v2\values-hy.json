{"logs": [{"outputFile": "com.townapp-mergeDebugResources-70:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3436,3536,3641,3739,3838,3943,4045,14614", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3531,3636,3734,3833,3938,4040,4151,14710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "68,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "6968,7382,7485,7596", "endColumns": "103,102,110,102", "endOffsets": "7067,7480,7591,7694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,281,349,421,496", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "127,195,276,344,416,491,565"}, "to": {"startLines": "48,87,88,90,104,155,156", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4462,9053,9121,9264,10297,14465,14540", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "4534,9116,9197,9327,10364,14535,14609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1044,1108,1205,1290,1352,1439,1501,1565,1626,1693,1754,1808,1930,1987,2047,2101,2182,2317,2401,2477,2567,2646,2731,2867,2942,3017,3160,3255,3335,3391,3444,3510,3584,3663,3734,3817,3888,3964,4040,4117,4223,4311,4391,4487,4583,4657,4735,4835,4886,4970,5039,5126,5217,5279,5343,5406,5477,5582,5688,5788,5891,5951,6008,6093,6176,6250", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "260,336,412,492,584,672,767,897,978,1039,1103,1200,1285,1347,1434,1496,1560,1621,1688,1749,1803,1925,1982,2042,2096,2177,2312,2396,2472,2562,2641,2726,2862,2937,3012,3155,3250,3330,3386,3439,3505,3579,3658,3729,3812,3883,3959,4035,4112,4218,4306,4386,4482,4578,4652,4730,4830,4881,4965,5034,5121,5212,5274,5338,5401,5472,5577,5683,5783,5886,5946,6003,6088,6171,6245,6325"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,70,71,72,86,89,91,92,93,94,95,96,97,98,99,100,101,102,103,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3100,3176,3256,3348,4156,4251,4381,7160,7221,7285,8968,9202,9332,9419,9481,9545,9606,9673,9734,9788,9910,9967,10027,10081,10162,10369,10453,10529,10619,10698,10783,10919,10994,11069,11212,11307,11387,11443,11496,11562,11636,11715,11786,11869,11940,12016,12092,12169,12275,12363,12443,12539,12635,12709,12787,12887,12938,13022,13091,13178,13269,13331,13395,13458,13529,13634,13740,13840,13943,14003,14060,14228,14311,14385", "endLines": "5,33,34,35,36,37,45,46,47,70,71,72,86,89,91,92,93,94,95,96,97,98,99,100,101,102,103,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,152,153,154", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "310,3095,3171,3251,3343,3431,4246,4376,4457,7216,7280,7377,9048,9259,9414,9476,9540,9601,9668,9729,9783,9905,9962,10022,10076,10157,10292,10448,10524,10614,10693,10778,10914,10989,11064,11207,11302,11382,11438,11491,11557,11631,11710,11781,11864,11935,12011,12087,12164,12270,12358,12438,12534,12630,12704,12782,12882,12933,13017,13086,13173,13264,13326,13390,13453,13524,13629,13735,13835,13938,13998,14055,14140,14306,14380,14460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\27b4d8b3dc714a89d9adc0d83f296ead\\transformed\\biometric-1.1.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,257,372,498,621,752,871,1033,1136,1268,1403", "endColumns": "113,87,114,125,122,130,118,161,102,131,134,122", "endOffsets": "164,252,367,493,616,747,866,1028,1131,1263,1398,1521"}, "to": {"startLines": "67,69,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6854,7072,7699,7814,7940,8063,8194,8313,8475,8578,8710,8845", "endColumns": "113,87,114,125,122,130,118,161,102,131,134,122", "endOffsets": "6963,7155,7809,7935,8058,8189,8308,8470,8573,8705,8840,8963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c6b7a95e3d9be8d22942c383e641eb5e\\transformed\\play-services-basement-18.3.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5582", "endColumns": "150", "endOffsets": "5728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ac05835035c583740d1a4f8d61f55f0\\transformed\\play-services-base-18.1.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4539,4645,4810,4944,5052,5206,5342,5469,5733,5900,6008,6176,6312,6474,6640,6705,6772", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "4640,4805,4939,5047,5201,5337,5464,5577,5895,6003,6171,6307,6469,6635,6700,6767,6849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,14145", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,14223"}}]}]}