import { useMutation, useQuery } from '@apollo/client';
import { router } from 'expo-router';
import React from 'react';
import { ActivityIndicator, Alert, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import {
  ACCEPT_FRIEND_REQUEST,
  GET_INCOMING_FRIEND_REQUESTS,
  REJECT_FRIEND_REQUEST,
} from '../../lib/graphql-operations';
import { formatRelativeTime } from '../../utils/date';
import { Avatar } from '../ui/Avatar';
import { Button } from '../ui/Button';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface FriendRequestsListProps {
  showHeader?: boolean;
  onViewAllPress?: () => void;
}

export function FriendRequestsList({ showHeader = true, onViewAllPress }: FriendRequestsListProps) {
  const theme = useTheme();

  // Query incoming friend requests
  const { data, loading, error, refetch } = useQuery(GET_INCOMING_FRIEND_REQUESTS);

  // Accept friend request mutation
  const [acceptFriendRequest, { loading: acceptLoading }] = useMutation(ACCEPT_FRIEND_REQUEST, {
    onCompleted: () => {
      refetch();
    },
    onError: (error) => {
      Alert.alert('Error', `Failed to accept friend request: ${error.message}`);
    },
  });

  // Reject friend request mutation
  const [rejectFriendRequest, { loading: rejectLoading }] = useMutation(REJECT_FRIEND_REQUEST, {
    onCompleted: () => {
      refetch();
    },
    onError: (error) => {
      Alert.alert('Error', `Failed to reject friend request: ${error.message}`);
    },
  });

  // Handle user profile press
  const handleUserPress = (userId: string) => {
    router.push(`/profile/${userId}`);
  };

  // Handle view all press
  const handleViewAllPress = () => {
    if (onViewAllPress) {
      onViewAllPress();
    } else {
      router.push('/friend-requests');
    }
  };

  // Handle accept request
  const handleAcceptRequest = (requestId: string) => {
    acceptFriendRequest({
      variables: {
        requestId,
      },
    });
  };

  // Handle reject request
  const handleRejectRequest = (requestId: string) => {
    rejectFriendRequest({
      variables: {
        requestId,
      },
    });
  };

  // Render request item
  const renderRequestItem = ({ item }: { item: any }) => (
    <ThemedView style={styles.requestItem}>
      <TouchableOpacity
        style={styles.userInfo}
        onPress={() => handleUserPress(item.requester.id)}
      >
        <Avatar
          source={item.requester.userProfile?.avatar}
          name={item.requester.userProfile ? 
            `${item.requester.userProfile.firstName || ''} ${item.requester.userProfile.lastName || ''}`.trim() :
            item.requester.emails?.[0]?.address?.split('@')[0]
          }
          size="medium"
        />
        <ThemedView style={styles.nameContainer}>
          <ThemedText variant="subtitle">{item.requester.userProfile ? 
            `${item.requester.userProfile.firstName || ''} ${item.requester.userProfile.lastName || ''}`.trim() :
            item.requester.emails?.[0]?.address?.split('@')[0]
          }</ThemedText>
          <ThemedText variant="caption">{formatRelativeTime(item.createdAt)}</ThemedText>
        </ThemedView>
      </TouchableOpacity>
      
      <ThemedView style={styles.actions}>
        <Button
          title="Accept"
          variant="primary"
          size="small"
          style={styles.actionButton}
          onPress={() => handleAcceptRequest(item.id)}
          loading={acceptLoading}
        />
        <Button
          title="Reject"
          variant="outline"
          size="small"
          style={styles.actionButton}
          onPress={() => handleRejectRequest(item.id)}
          loading={rejectLoading}
        />
      </ThemedView>
    </ThemedView>
  );

  // Render empty state
  const renderEmptyState = () => (
    <ThemedView style={styles.emptyState}>
      <ThemedText>No friend requests</ThemedText>
    </ThemedView>
  );

  // Get requests from data
  const requests = data?.requests || [];

  return (
    <ThemedView style={styles.container}>
      {showHeader && (
        <ThemedView style={styles.header}>
          <ThemedText variant="title">Friend Requests</ThemedText>
          {requests.length > 0 && (
            <TouchableOpacity onPress={handleViewAllPress}>
              <ThemedText style={{ color: theme.colors.primary }}>View All</ThemedText>
            </TouchableOpacity>
          )}
        </ThemedView>
      )}

      {loading ? (
        <ActivityIndicator size="large" color={theme.colors.primary} style={styles.loader} />
      ) : error ? (
        <ThemedText style={styles.errorText}>Error loading requests: {error.message}</ThemedText>
      ) : (
        <FlatList
          data={requests}
                    keyboardShouldPersistTaps="handled"
          scrollEnabled={true}

          keyExtractor={(item) => item.id}
          renderItem={renderRequestItem}
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={styles.listContent}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: 16,
  },
  requestItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  nameContainer: {
    marginLeft: 12,
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    marginLeft: 8,
    minWidth: 80,
  },
  loader: {
    marginTop: 20,
  },
  errorText: {
    padding: 16,
    color: 'red',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
});
