import { router } from 'expo-router';
import React from 'react';
import { StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Place } from '../../types';
import { Marker } from './WebMapComponent';

interface PlaceMarkerProps {
  place: Place;
  onPress?: (place: Place) => void;
}

export function PlaceMarker({ place, onPress }: PlaceMarkerProps) {
  const theme = useTheme();

  // Get marker color based on place category
  const getMarkerColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'restaurant':
      case 'food':
      case 'cafe':
        return theme.colors.food || '#FF9800';
      case 'shopping':
      case 'retail':
      case 'store':
        return theme.colors.shopping || '#4CAF50';
      case 'entertainment':
      case 'event':
        return theme.colors.entertainment || '#9C27B0';
      case 'service':
        return theme.colors.service || '#2196F3';
      case 'landmark':
      case 'attraction':
        return theme.colors.landmark || '#F44336';
      default:
        return theme.colors.primary || '#3F51B5';
    }
  };

  // Handle marker press
  const handleMarkerPress = () => {
    if (onPress) {
      onPress(place);
    }
  };

  // Handle callout press
  const handleCalloutPress = () => {
    router.push(`/place/${place.id}`);
  };

  // Get the primary category for the place
  const primaryCategory = place.category || 
    (place.categories && place.categories.length > 0 ? place.categories[0] : '');

  // Get coordinates from place
  const latitude = place.location?.coordinates?.[1] || place.loc?.lat || 0;
  const longitude = place.location?.coordinates?.[0] || place.loc?.lng || 0;

  return (
    <Marker
      coordinate={{
        latitude,
        longitude,
      }}
      title={place.name}
      description={place.desc}
      color={getMarkerColor(primaryCategory)}
      onPress={handleMarkerPress}
      onCalloutPress={handleCalloutPress}
    />
  );
}

const styles = StyleSheet.create({
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  marker: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'white',
  },
});
