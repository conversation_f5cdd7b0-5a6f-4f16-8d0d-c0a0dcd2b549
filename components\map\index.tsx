import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useZone } from '../../contexts/ZoneContext';
import { Category, Place, Region } from '../../types';
import { CategoryFilter } from './CategoryFilter';
import { MapControls } from './MapControls';
import { PlaceMarker } from './PlaceMarker';
import WebMapComponent from './WebMapComponent';

interface MapIndexProps {
  places: Place[];
  categories: Category[];
}

export default function MapIndex({ places, categories }: MapIndexProps) {
  const theme = useTheme();
  const { currentZone } = useZone();

  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showNearbyPlaces, setShowNearbyPlaces] = useState(false);
  const [region, setRegion] = useState<Region | undefined>(undefined);
  const [filteredPlaces, setFilteredPlaces] = useState<Place[]>([]);

  // Initialize region to currentZone or default
  useEffect(() => {
    if (currentZone?.center) {
      setRegion({
        latitude: currentZone.center.latitude,
        longitude: currentZone.center.longitude,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      });
    }
  }, [currentZone]);

  // Filter places by selected categories
  useEffect(() => {
    if (selectedCategories.length === 0) {
      setFilteredPlaces(places);
    } else {
      setFilteredPlaces(
        places.filter((place) =>
          place.categories?.some((cat) => selectedCategories.includes(cat))
        )
      );
    }
  }, [places, selectedCategories]);

  // Handle category selection toggle
  const onSelectCategory = (categoryId: string) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  // Center map on user location (stub)
  const onCenterUserLocation = () => {
    // TODO: Implement user location centering
  };

  // Toggle showing nearby places (stub)
  const onToggleNearbyPlaces = () => {
    setShowNearbyPlaces((prev) => !prev);
  };

  // Center map on current zone
  const onCenterZone = (zone: typeof currentZone) => {
    if (zone?.center) {
      setRegion({
        latitude: zone.center.latitude,
        longitude: zone.center.longitude,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      });
    }
  };

  return (
    <View style={styles.container}>
      <WebMapComponent
        style={styles.map}
        region={region}
        onRegionChangeComplete={(newRegion) => setRegion(newRegion)}
        showsUserLocation
        showsMyLocationButton
        showsCompass
        showsScale
      >
        {filteredPlaces.map((place) => (
          <PlaceMarker key={place.id} place={place} />
        ))}
      </WebMapComponent>

      <CategoryFilter
        categories={categories}
        selectedCategories={selectedCategories}
        onSelectCategory={onSelectCategory}
      />

      <MapControls
        currentZone={currentZone}
        showNearbyPlaces={showNearbyPlaces}
        onCenterUserLocation={onCenterUserLocation}
        onToggleNearbyPlaces={onToggleNearbyPlaces}
        onCenterZone={onCenterZone}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 0,
  },
});
