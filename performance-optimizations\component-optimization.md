## React Component Optimization

### Memoization
- Implement React.memo for pure functional components
- Use useMemo for expensive calculations
- Apply useCallback for event handlers passed to child components

### Component Rendering
- Avoid unnecessary re-renders with proper dependency arrays
- Implement shouldComponentUpdate or React.PureComponent for class components
- Use key props correctly in lists to optimize reconciliation