import { ApolloQueryResult, NetworkStatus, OperationVariables, QueryResult } from '@apollo/client';
import { useCallback, useEffect, useState } from 'react';
import { ListRenderItem } from 'react-native';

interface UseInfiniteScrollOptions<T> {
  initialData?: T[];
  pageSize?: number;
  fetchMore: (variables: { offset: number; limit: number }) => Promise<ApolloQueryResult<any>>;
  getItems: (data: any) => T[];
  enabled?: boolean;
  estimatedItemSize?: number;
}

export function useInfiniteScroll<T>({
  initialData = [],
  pageSize = 10,
  fetchMore,
  getItems,
  enabled = true,
  estimatedItemSize = 100,
}: UseInfiniteScrollOptions<T>) {
  const [items, setItems] = useState<T[]>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(initialData.length);

  // Reset when initialData changes
  useEffect(() => {
    setItems(initialData);
    setOffset(initialData.length);
    setHasMore(initialData.length >= pageSize);
    setError(null);
  }, [initialData, pageSize]);

  // Load more items
  const loadMore = useCallback(async () => {
    if (!enabled || loading || !hasMore) return;

    try {
      setLoading(true);
      setError(null);

      const result = await fetchMore({
        offset,
        limit: pageSize,
      });

      const newItems = getItems(result.data);

      if (newItems.length === 0) {
        setHasMore(false);
      } else {
        setItems((prevItems) => [...prevItems, ...newItems]);
        setOffset((prevOffset) => prevOffset + newItems.length);
        setHasMore(newItems.length >= pageSize);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Error loading more items'));
      console.error('Error loading more items:', err);
    } finally {
      setLoading(false);
    }
  }, [enabled, loading, hasMore, fetchMore, offset, pageSize, getItems]);

  // Handle end reached
  const handleEndReached = useCallback(() => {
    if (!loading && hasMore) {
      loadMore();
    }
  }, [loading, hasMore, loadMore]);

  // Refresh data
  const refresh = useCallback(async () => {
    if (!enabled) return;

    try {
      setLoading(true);
      setError(null);

      const result = await fetchMore({
        offset: 0,
        limit: pageSize,
      });

      const newItems = getItems(result.data);

      setItems(newItems);
      setOffset(newItems.length);
      setHasMore(newItems.length >= pageSize);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Error refreshing items'));
      console.error('Error refreshing items:', err);
    } finally {
      setLoading(false);
    }
  }, [enabled, fetchMore, pageSize, getItems]);

  return {
    items,
    loading,
    error,
    hasMore,
    loadMore,
    handleEndReached,
    refresh,
    estimatedItemSize,
  };
}

// Helper function to create infinite scroll from a query result
export function createInfiniteScroll<T, TData = any>(
  queryResult: QueryResult<TData, OperationVariables>,
  options: {
    getItems: (data: TData) => T[];
    getVariables: (offset: number, limit: number) => OperationVariables;
    pageSize?: number;
    estimatedItemSize?: number;
    renderItem: ListRenderItem<T>;
    keyExtractor: (item: T, index: number) => string;
  }
) {
  const {
    data,
    loading: queryLoading,
    error: queryError,
    fetchMore,
    refetch,
    networkStatus
  } = queryResult;

  const {
    getItems,
    getVariables,
    pageSize = 10,
    estimatedItemSize = 100,
    renderItem,
    keyExtractor
  } = options;

  const initialData = data ? getItems(data) : [];

  const [refreshing, setRefreshing] = useState(false);

  const {
    items,
    loading: loadingMore,
    error: loadMoreError,
    hasMore,
    handleEndReached,
  } = useInfiniteScroll<T>({
    initialData,
    pageSize,
    estimatedItemSize,
    fetchMore: async ({ offset, limit }) => {
      return fetchMore({
        variables: getVariables(offset, limit),
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;

          // Combine previous and new items
          // This depends on the structure of your query result
          // You may need to customize this based on your GraphQL schema
          return {
            ...prev,
            ...fetchMoreResult,
          };
        },
      });
    },
    getItems: (data) => getItems(data),
    enabled: !queryLoading && !queryError,
  });

  // Handle refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch(getVariables(0, pageSize));
    } catch (err) {
      console.error('Error refreshing:', err);
    } finally {
      setRefreshing(false);
    }
  }, [refetch, getVariables, pageSize]);

  // Determine if we're currently loading
  const isLoading = queryLoading || loadingMore || networkStatus === NetworkStatus.refetch;

  // Determine if we're refreshing
  const isRefreshing = refreshing || networkStatus === NetworkStatus.refetch;

  return {
    items,
    loading: isLoading,
    error: queryError || loadMoreError,
    refreshing: isRefreshing,
    onRefresh,
    handleEndReached,
    hasMore,
    renderItem,
    keyExtractor,
    estimatedItemSize,
  };
}

// Helper types for FlashList
export interface VirtualizedListProps<T> {
  data: T[];
  renderItem: ListRenderItem<T>;
  keyExtractor: (item: T, index: number) => string;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  estimatedItemSize: number;
  numColumns?: number;
  ListEmptyComponent?: React.ReactElement;
  ListHeaderComponent?: React.ReactElement;
  ListFooterComponent?: React.ReactElement;
  contentContainerStyle?: any;
  refreshing?: boolean;
  onRefresh?: () => void;
}
