import React, { useEffect, useRef } from 'react';
import { StyleSheet, View } from 'react-native';
import { WebView } from 'react-native-webview';

type MobileMapComponentProps = {
  selectedCategory: string | null;
  markerPosition?: { lat: number; lng: number };
};

const MobileMapComponent: React.FC<MobileMapComponentProps> = ({ 
  selectedCategory, 
  markerPosition 
}) => {
  const webViewRef = useRef<WebView>(null);

  // This HTML includes Leaflet CSS and JS directly
  const leafletHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
      <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
      <style>
        body { margin: 0; padding: 0; }
        #map { width: 100%; height: 100vh; }
      </style>
    </head>
    <body>
      <div id="map"></div>
      <script>
        // Initialize the map
        var map = L.map('map').setView([33.7490, -84.3880], 13);
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Function to handle messages from React Native
        window.addEventListener('message', function(event) {
          const data = JSON.parse(event.data);
          
          if (data.type === 'UPDATE_MARKER' && data.position) {
            // Clear existing markers
            map.eachLayer(function(layer) {
              if (layer instanceof L.Marker) {
                map.removeLayer(layer);
              }
            });
            
            // Add new marker
            L.marker([data.position.lat, data.position.lng]).addTo(map);
            map.setView([data.position.lat, data.position.lng], 15);
          }
          
          if (data.type === 'UPDATE_CATEGORY') {
            // Handle category filtering if needed
            console.log('Category updated:', data.category);
          }
        });
      </script>
    </body>
    </html>
  `;

  // Send updates to the WebView when props change
  useEffect(() => {
    if (webViewRef.current && markerPosition) {
      webViewRef.current.postMessage(JSON.stringify({
        type: 'UPDATE_MARKER',
        position: markerPosition
      }));
    }
  }, [markerPosition]);

  useEffect(() => {
    if (webViewRef.current && selectedCategory) {
      webViewRef.current.postMessage(JSON.stringify({
        type: 'UPDATE_CATEGORY',
        category: selectedCategory
      }));
    }
  }, [selectedCategory]);

  return (
    <View style={styles.container}>
      <WebView
        ref={webViewRef}
        originWhitelist={['*']}
        source={{ html: leafletHtml }}
        style={styles.webview}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  webview: {
    flex: 1,
  },
});

export default MobileMapComponent;