/**
 * <PERSON>ript to clear Metro bundler cache and watchman cache
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

console.log('Clearing Metro bundler cache...');

// Clear Metro cache
try {
  execSync('npx expo start --clear', { stdio: 'inherit' });
  console.log('Metro cache cleared successfully.');
} catch (error) {
  console.error('Error clearing Metro cache:', error.message);
}

// Clear Watchman cache if available
try {
  execSync('watchman watch-del-all', { stdio: 'inherit' });
  console.log('Watchman cache cleared successfully.');
} catch (error) {
  console.log('Watchman not available or error clearing cache.');
}

// Clear Babel cache
const babelCachePath = path.join(os.tmpdir(), 'metro-babel-cache');
if (fs.existsSync(babelCachePath)) {
  try {
    fs.rmdirSync(babelCachePath, { recursive: true });
    console.log('Babel cache cleared successfully.');
  } catch (error) {
    console.error('Error clearing Babel cache:', error.message);
  }
}

// Clear React Native cache
const reactNativeCachePath = path.join(os.tmpdir(), 'react-native-packager-cache');
if (fs.existsSync(reactNativeCachePath)) {
  try {
    fs.rmdirSync(reactNativeCachePath, { recursive: true });
    console.log('React Native cache cleared successfully.');
  } catch (error) {
    console.error('Error clearing React Native cache:', error.message);
  }
}

console.log('All caches cleared. Try rebuilding your app now.');
