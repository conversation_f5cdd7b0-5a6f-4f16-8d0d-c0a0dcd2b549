{"name": "townexpo", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "clear-cache": "npx expo start --clear --non-interactive --no-dev", "clear-metro": "node ./scripts/clear-metro-cache.js", "fix-web": "node ./scripts/fix-web-bundling.js", "web-clean": "npm run clear-cache && expo start --web", "web-reset": "npm run clear-metro && expo start --web --clear", "web-full-reset": "npm run clear-metro && npm run fix-web && rm -rf node_modules/.cache && expo start --web --clear", "analyze-bundle": "ANALYZE_BUNDLE=1 expo export -p web", "build-web": "expo export -p web", "test-performance": "npm run build-web && echo 'Bundle built. Check dist/ folder for output.'"}, "dependencies": {"@apollo/client": "^3.13.8", "@expo/vector-icons": "^14.1.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@shopify/flash-list": "^1.8.0", "@turf/turf": "^7.2.0", "ajv": "^8.12.0", "ajv-keywords": "^5.1.0", "apollo3-cache-persist": "^0.15.0", "expo": "~53.0.8", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.4", "expo-camera": "^16.1.6", "expo-constants": "~17.1.6", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.4", "expo-location": "^18.1.5", "expo-notifications": "^0.31.2", "expo-router": "~5.0.6", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "graphql": "^16.11.0", "graphql-ws": "^6.0.4", "leaflet": "^1.9.4", "leaflet-defaulticon-compatibility": "^0.1.2", "lodash": "^4.17.21", "react": "19.0.0", "react-dom": "19.0.0", "react-leaflet": "^5.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-pell-rich-editor": "^1.9.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "~0.20.0", "react-native-webview": "^13.13.5", "rrule": "^2.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-runtime": "^7.25.0", "@types/leaflet": "^1.9.17", "@types/lodash": "^4.17.16", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "react-native-svg-transformer": "^1.5.1", "typescript": "~5.8.3"}, "overrides": {"glob": "^10.3.10", "rimraf": "^5.0.5"}, "resolutions": {"glob": "^10.3.10", "rimraf": "^5.0.5"}, "private": true}