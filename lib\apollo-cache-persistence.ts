import { InMemoryCache } from '@apollo/client';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Helper function to safely access localStorage
const safeLocalStorage = {
  getItem: (key: string): string | null => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        return window.localStorage.getItem(key);
      }
      return null;
    } catch (error) {
      console.error('Error accessing localStorage.getItem:', error);
      return null;
    }
  },
  setItem: (key: string, value: string): void => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem(key, value);
      }
    } catch (error) {
      console.error('Error accessing localStorage.setItem:', error);
    }
  },
  removeItem: (key: string): void => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error accessing localStorage.removeItem:', error);
    }
  }
};

// Cache storage key
const APOLLO_CACHE_KEY = 'apollo_cache';

/**
 * Persists the Apollo cache to storage
 * @param cache The Apollo InMemoryCache instance
 */
export async function persistCache(cache: InMemoryCache): Promise<void> {
  try {
    const data = cache.extract();
    const serializedData = JSON.stringify(data);

    if (Platform.OS === 'web') {
      safeLocalStorage.setItem(APOLLO_CACHE_KEY, serializedData);
    } else {
      await SecureStore.setItemAsync(APOLLO_CACHE_KEY, serializedData);
    }

    console.log('Apollo cache persisted successfully');
  } catch (error) {
    console.error('Error persisting Apollo cache:', error);
  }
}

/**
 * Restores the Apollo cache from storage
 * @param cache The Apollo InMemoryCache instance
 */
export async function restoreCache(cache: InMemoryCache): Promise<void> {
  try {
    let serializedData: string | null = null;

    if (Platform.OS === 'web') {
      serializedData = safeLocalStorage.getItem(APOLLO_CACHE_KEY);
    } else {
      serializedData = await SecureStore.getItemAsync(APOLLO_CACHE_KEY);
    }

    if (serializedData) {
      const data = JSON.parse(serializedData);
      cache.restore(data);
      console.log('Apollo cache restored successfully');
    } else {
      console.log('No persisted Apollo cache found');
    }
  } catch (error) {
    console.error('Error restoring Apollo cache:', error);
  }
}

/**
 * Clears the persisted Apollo cache from storage
 */
export async function clearPersistedCache(): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      safeLocalStorage.removeItem(APOLLO_CACHE_KEY);
    } else {
      await SecureStore.deleteItemAsync(APOLLO_CACHE_KEY);
    }

    console.log('Persisted Apollo cache cleared successfully');
  } catch (error) {
    console.error('Error clearing persisted Apollo cache:', error);
  }
}

/**
 * Sets up automatic cache persistence
 * @param cache The Apollo InMemoryCache instance
 * @param debounceInterval Time in ms to wait before persisting cache after changes
 */
export function setupAutomaticCachePersistence(
  cache: InMemoryCache,
  debounceInterval = 1000
): () => void {
  let timeoutId: NodeJS.Timeout | null = null;

  // Function to persist cache with debounce
  const debouncedPersist = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      persistCache(cache);
      timeoutId = null;
    }, debounceInterval);
  };

  // Watch for cache changes
  const watcher = cache.watch({
    callback: debouncedPersist,
    optimistic: true,
  });

  // Return cleanup function
  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    watcher();
  };
}
