# Town App - Expo

This is the Expo version of the Town App, a location-based social platform that connects people through zones, places, events, and more.

## Features

- **Authentication**: Login, signup, and password reset
- **Zones**: Geographical areas that contain content and communities
- **Posts**: Share updates, photos, and thoughts within zones
- **Places**: Discover and interact with local businesses and landmarks
- **Happenings**: Find and join events in your area
- **Offers**: Discover and redeem special offers from local businesses
- **Messaging**: Private conversations with other users
- **Map**: Interactive map showing zones, places, and events
- **Profile**: Manage your profile and settings

## Tech Stack

- **Framework**: [Expo](https://expo.dev/) with [Expo Router](https://docs.expo.dev/router/introduction/)
- **Language**: [TypeScript](https://www.typescriptlang.org/)
- **State Management**: [Apollo Client](https://www.apollographql.com/docs/react/) for GraphQL
- **UI Components**: Custom components built with React Native
- **Maps**: [React Native Maps](https://github.com/react-native-maps/react-native-maps)
- **Location**: [Expo Location](https://docs.expo.dev/versions/latest/sdk/location/)
- **Storage**: [Expo SecureStore](https://docs.expo.dev/versions/latest/sdk/securestore/)
- **Image Picker**: [Expo ImagePicker](https://docs.expo.dev/versions/latest/sdk/imagepicker/)
- **Geospatial**: [Turf.js](https://turfjs.org/) for geospatial analysis

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (v14 or later)
- [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)
- [Expo CLI](https://docs.expo.dev/get-started/installation/)

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npx expo start
   ```

3. Run on a device or emulator:
   - Press `a` to run on Android emulator
   - Press `i` to run on iOS simulator
   - Scan the QR code with the Expo Go app on your device

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
EXPO_PUBLIC_GRAPHQL_URL=http://your-api-url/graphql
```

## Project Structure

```
townapp/
├── app/                    # Expo Router app directory
│   ├── (auth)/             # Authentication screens
│   ├── (tabs)/             # Main tab screens
│   ├── post/               # Post-related screens
│   ├── place/              # Place-related screens
│   ├── zone/               # Zone-related screens
│   ├── messages/           # Message-related screens
│   ├── _layout.tsx         # Root layout
│   └── index.tsx           # Entry point
├── assets/                 # Static assets
├── components/             # Reusable components
│   ├── ui/                 # UI components
│   ├── posts/              # Post-related components
│   ├── places/             # Place-related components
│   ├── happenings/         # Happening-related components
│   └── offers/             # Offer-related components
├── contexts/               # Context providers
├── hooks/                  # Custom hooks
├── styles/                 # Global styles
├── types/                  # TypeScript type definitions
├── utils/                  # Utility functions
├── app.json                # Expo configuration
└── package.json            # Dependencies
```

## Authentication

The app uses JWT-based authentication with the following flow:

1. User enters credentials (email/password)
2. App sends credentials to GraphQL API
3. API returns JWT token
4. Token is stored securely (SecureStore on mobile, localStorage on web)
5. Apollo client includes token in all GraphQL requests
6. Token is refreshed automatically when needed

## GraphQL API

The app connects to a GraphQL API for all data operations. The API is defined in the server repository.
