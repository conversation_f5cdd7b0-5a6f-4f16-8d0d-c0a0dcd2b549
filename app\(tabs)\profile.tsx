import { useQuery } from '@apollo/client';
import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, RefreshControl, ScrollView, StyleSheet } from 'react-native';
import { FriendRequestsList } from '../../components/social/FriendRequestsList';
import { FriendsList } from '../../components/social/FriendsList';
import { Avatar } from '../../components/ui/Avatar';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { GET_CURRENT_USER_WITH_FRIENDS } from '../../lib/graphql-operations';

export default function ProfileScreen() {
  const { user: authUser, logout } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  // Query current user data with friends
  const { data, loading, error, refetch } = useQuery(GET_CURRENT_USER_WITH_FRIENDS, {
    variables: {
      limit: 5, // Limit to 5 friends for the preview
    },
  });

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing profile:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  // Navigate to friends list
  const navigateToFriends = () => {
    router.push('/profile/friends');
  };

  // Navigate to friend requests
  const navigateToFriendRequests = () => {
    router.push('/profile/friend-requests');
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading profile...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading profile: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
      </ThemedView>
    );
  }

  const user = data?.currentUser || authUser;

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <ThemedView style={styles.header}>
        <Avatar
          source={user?.userProfile?.avatar}
          name={user?.userProfile ? 
            `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim() :
            user?.emails?.[0]?.address?.split('@')[0]
          }
          size="large"
          online={user?.isOnline}
        />
        <ThemedView style={styles.userInfo}>
          <ThemedText variant="title">
            {user?.userProfile ?
              `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim() :
              user?.emails?.[0]?.address?.split('@')[0]
            }
          </ThemedText>
          <ThemedText variant="caption">@{user?.emails?.[0]?.address?.split('@')[0]}</ThemedText>
        </ThemedView>
      </ThemedView>

      {user?.userProfile?.bio && (
        <Card style={styles.bioCard}>
          <ThemedText variant="subtitle">Bio</ThemedText>
          <ThemedText style={styles.bio}>{user.userProfile.bio}</ThemedText>
        </Card>
      )}

      {/* Friend Requests Section */}
      <Card style={styles.sectionCard}>
        <FriendRequestsList 
          showHeader={true}
          onViewAllPress={navigateToFriendRequests}
        />
      </Card>

      {/* Friends Section */}
      <Card style={styles.sectionCard}>
        <FriendsList 
          userId={user?.id}
          limit={5}
          showHeader={true}
          onViewAllPress={navigateToFriends}
        />
      </Card>

      <ThemedView style={styles.actions}>
        <Button
          title="Edit Profile"
          onPress={() => router.push('/profile/edit')}
          style={styles.actionButton}
        />
        <Button
          title="Settings"
          onPress={() => router.push('/settings')}
          variant="outline"
          style={styles.actionButton}
        />
        <Button
          title="Log Out"
          onPress={handleLogout}
          variant="text"
          style={styles.logoutButton}
        />
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  userInfo: {
    marginLeft: 16,
  },
  bioCard: {
    marginBottom: 16,
    padding: 16,
  },
  bio: {
    marginTop: 8,
  },
  sectionCard: {
    marginBottom: 16,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  viewAllText: {
    color: '#0a7ea4',
  },
  actions: {
    marginTop: 8,
  },
  actionButton: {
    marginBottom: 12,
  },
  logoutButton: {
    marginTop: 8,
  },
});
