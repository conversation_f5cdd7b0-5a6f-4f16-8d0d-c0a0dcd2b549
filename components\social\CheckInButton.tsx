import { useMutation } from '@apollo/client';
import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { CHECK_IN } from '../../lib/graphql-operations';
import { Button } from '../ui/Button';
import { CheckInModal } from './CheckInModal';

interface CheckInButtonProps {
  objectId: string;
  objectType: 'place' | 'happening' | 'offer';
  checkInCount?: number;
  checkType?: 'prox' | 'checkin' | 'completed';
  size?: 'small' | 'medium' | 'large';
  style?: any;
}

export function CheckInButton({
  objectId,
  objectType,
  checkInCount = 0,
  checkType = 'checkin',
  size = 'small',
  style
}: CheckInButtonProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const [checkIn, { loading }] = useMutation(CHECK_IN);
  const theme = useTheme();

  const handleCheckIn = async (comment?: string) => {
    try {
      await checkIn({
        variables: {
          linkedObjectId: objectId,
          objectType,
          comment,
          checkType
        },
        optimisticResponse: {
          __typename: 'Mutation',
          createCheck: {
            __typename: 'Check',
            id: 'temp-id-' + Date.now(),
            linkedObjectId: objectId,
            objectType,
            comment: comment || null,
            type: checkType || 'checkin',
            createdAt: new Date().toISOString(),
          },
        },
        update: (cache, { data }) => {
          // Update the cache to reflect the new check-in count
          // This is a simplified approach - in a real app, you'd want to update
          // the specific object that was checked into
          console.log('Check-in successful:', data);
        }
      });
    } catch (error) {
      console.error('Error checking in:', error);
    } finally {
      setModalVisible(false);
    }
  };

  // Get icon based on check type
  const getIcon = () => {

    switch (checkType) {
      case 'prox':
        return 'navigate';
      case 'completed':
        return 'checkmark-circle';
      case 'checkin':
      default:
        return 'location-outline';
    }
  };

  // Get button title based on check type and status
  const getTitle = () => {

    switch (checkType) {
      case 'prox':
        return `Nearby (${checkInCount})`;
      case 'completed':
        return `Complete (${checkInCount})`;
      case 'checkin':
      default:
        return `Check In (${checkInCount})`;
    }
  };

  return (
    <>
      <Button
        title={getTitle()}
        variant={'primary'}
        size={size}
        onPress={() => setModalVisible(true)}
        loading={loading}
        icon={getIcon()}
        style={style}
      />

      <CheckInModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onCheckIn={handleCheckIn}
        objectType={objectType}
      />
    </>
  );
}
