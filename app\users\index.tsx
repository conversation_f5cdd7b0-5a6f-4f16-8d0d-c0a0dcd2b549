'use client';

import { useZone } from '@/contexts/ZoneContext';
import { User } from '@/models/User';
import { useMutation, useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TextInput
} from 'react-native';
import { Card } from '../../components/ui/Card';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { BLOCK_USER, GET_USERS, SEND_FRIEND_REQUEST, UNBLOCK_USER } from '../../lib/graphql-operations';
import UserCard from './UserCard';



// Define interfaces for the criteria object
interface UserReference {
  _id?: string;
  email?: string;
}

interface UsersCriteria {
  zone: string;
  limit: number;
  visible?: boolean;
  userId?: string;
  user?: UserReference;
}

const Users: React.FC = () => {
  console.log('Users component rendering');

  const { currentZone, loading: zoneLoading } = useZone();
  const { user, loading: userLoading } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [limit, setLimit] = useState(10);

  // Determine if the current user is an admin
  const isAuthenticated = !!user;
  const isAdmin = user?.roles?.includes('admin');

  
  console.log('Using zone ID for queries:', currentZone?.id);
  console.log('Current user from UserContext:', user);
  console.log('Is admin:', isAdmin);
  console.log('Is authenticated:', isAuthenticated);
  console.log('User loading:', userLoading);

  // Create a simple criteria object for the users query
  // Let the server handle the visibility filtering based on the authenticated user
  const criteria: UsersCriteria = {
    zone: currentZone?.id || '',
    limit: limit
  };

  // Query for users with the simplified criteria
  const { loading, error, data } = useQuery(
    GET_USERS, {
      variables: {
        criteria: {
          zone: currentZone?.id || '',
        },
        limit: 20,
        skip: 0,
      },
      skip: !currentZone?.id,
    });

  const [sendFriendRequest] = useMutation(SEND_FRIEND_REQUEST, {
    onError: (error) => {
      console.error('Error sending friend request:', error);
    }
  });

  const [blockUser] = useMutation(BLOCK_USER, {
    onError: (error) => {
      console.error('Error blocking user:', error);
    },
    refetchQueries: [
      {
        query: GET_USERS,
        variables: { criteria }
      }
    ]
  });

  const [unblockUser] = useMutation(UNBLOCK_USER, {
    onError: (error) => {
      console.error('Error unblocking user:', error);
    },
    refetchQueries: [
      {
        query: GET_USERS,
        variables: { criteria }
      }
    ]
  });

  const handleSendFriendRequest = (userId: string) => {
    // Use the correct property name 'userId' as specified in the API documentation
    sendFriendRequest({
      variables: { userId }
    });
  };

  const handleBlockUser = (userId: string) => {
    blockUser({
      variables: { userId }
    });
  };

  const handleUnblockUser = (userId: string) => {
    unblockUser({
      variables: { userId }
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const loadMore = () => {
    setLimit(prev => prev + 10);
  };

  // Loading state
  if (zoneLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  if (error) {
    console.error('GraphQL error details:', error);
    return (
      <div className="red-text">
        <h4>Error loading users:</h4>
        <p>{error.message}</p>
        <p>Please check the browser console for more details.</p>
      </div>
    );
  }

  // Get the users from the query result
  const users: User[] = data?.users || [];

  console.log('Users data received:', users);

  // Filter users based on search term
  const filteredUsers = users.filter(user => {
    // Search term filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();

      // Try different user properties for matching
      let matchesName = false;

      // Check humanName method if available
      if (typeof user.humanName === 'function') {
        try {
          const name = user.humanName();
          if (name) matchesName = name.toLowerCase().includes(searchLower);
        } catch (e) {
          console.error('Error calling humanName method:', e);
        }
      }

      // Check profile name fields
      if (!matchesName) {
        // Check userProfile
        if (user.userProfile?.firstName || user.userProfile?.lastName) {
          const fullName = `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim();
          if (fullName) matchesName = fullName.toLowerCase().includes(searchLower);
        }

        // Check userProfile
        if (!matchesName && user.userProfile) {
          if (typeof user.userProfile.fullName === 'function') {
            try {
              const fullName = user.userProfile.fullName();
              if (fullName) matchesName = fullName.toLowerCase().includes(searchLower);
            } catch (e) {
              console.error('Error calling userProfile.fullName method:', e);
            }
          } else if (user.userProfile.firstName || user.userProfile.lastName) {
            const fullName = `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim();
            if (fullName) matchesName = fullName.toLowerCase().includes(searchLower);
          }
        }

        // Check profile directly
        if (!matchesName && user.userProfile) {
          if (typeof user.userProfile === 'object' && (user.userProfile.firstName || user.userProfile.lastName)) {
            const fullName = `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim();
            if (fullName) matchesName = fullName.toLowerCase().includes(searchLower);
          }
        }
      }

      // Check email addresses
      const matchesEmail = user.emails &&
        user.emails.some(email => email.address.toLowerCase().includes(searchLower));

      if (!matchesName && !matchesEmail) return false;
    }
    return true;
  });

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <Card style={styles.searchContainer}>
          <Ionicons name="search" size={20} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search offers..."
            value={searchTerm}
            onChangeText={setSearchTerm}
          />
        </Card>
      </ThemedView>

      {filteredUsers.length > 0 ? (
        <>
          {filteredUsers.map(user => (
            <div key={user.id} className="row user-card-container">
              <div className="col s12">
                <UserCard
                  user={user}
                  onSendRequest={handleSendFriendRequest}
                  onBlockUser={handleBlockUser}
                  onUnblockUser={handleUnblockUser}
                />
                {user.roles && user.roles.length > 0 && (
                  <div className="user-roles" style={{ marginTop: '8px' }}>
                    {user.roles.map((role, index) => (
                      <div key={index} className="chip blue white-text">
                        {role}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}

          {users.length >= limit && (
            <div className="center-align">
              <button
                className="btn waves-effect waves-light indigo"
                onClick={loadMore}
              >
                Load More
              </button>
            </div>
          )}
        </>
      ) : (
        <div className="center-align">
          <p>No users found</p>
        </div>
      )}
    </ThemedView>
  );
};

export default Users;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
        pointerEvents: 'auto',

  },
  header: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
  },
  addButton: {
    marginBottom: 16,
  },
  offersGrid: {
    paddingBottom: 16,
  },
  row: {
    flex: 1,
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  createButton: {
    marginTop: 8,
  },
});
