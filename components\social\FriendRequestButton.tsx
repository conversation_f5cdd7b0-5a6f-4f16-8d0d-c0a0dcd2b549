import { useMutation, useQuery } from '@apollo/client';
import React, { useState } from 'react';
import { ActivityIndicator, Alert, StyleSheet, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import {
  ACCEPT_FRIEND_REQUEST,
  GET_FRIEND_REQUESTS,
  REJECT_FRIEND_REQUEST,
  SEND_FRIEND_REQUEST,
} from '../../lib/graphql-operations';
import { Button } from '../ui/Button';
import { ThemedText } from '../ui/ThemedText';

interface FriendRequestButtonProps {
  userId: string;
  size?: 'small' | 'medium' | 'large';
  style?: any;
  onSuccess?: () => void;
}

export function FriendRequestButton({ userId, size = 'medium', style, onSuccess }: FriendRequestButtonProps) {
  const { currentUser } = useAuth();
  const theme = useTheme();
  const [loading, setLoading] = useState(false);

  // Get friend requests to determine the current state
  const { data, loading: queryLoading, refetch } = useQuery(GET_FRIEND_REQUESTS, {
    variables: {
      criteria: {
        $or: [
          { requesterId: currentUser?.id, linkedObjectId: userId },
          { requesterId: userId, linkedObjectId: currentUser?.id }
        ],
        objectType: "users",
        type: "friend"
      }
    },
    skip: !currentUser?.id || !userId,
  });

  // Send friend request mutation
  const [sendFriendRequest] = useMutation(SEND_FRIEND_REQUEST, {
    onCompleted: () => {
      setLoading(false);
      Alert.alert('Success', 'Friend request sent!');
      refetch();
      if (onSuccess) onSuccess();
    },
    onError: (error) => {
      setLoading(false);
      Alert.alert('Error', `Failed to send friend request: ${error.message}`);
    },
  });

  // Accept friend request mutation
  const [acceptFriendRequest] = useMutation(ACCEPT_FRIEND_REQUEST, {
    onCompleted: () => {
      setLoading(false);
      Alert.alert('Success', 'Friend request accepted!');
      refetch();
      if (onSuccess) onSuccess();
    },
    onError: (error) => {
      setLoading(false);
      Alert.alert('Error', `Failed to accept friend request: ${error.message}`);
    },
  });

  // Reject friend request mutation
  const [rejectFriendRequest] = useMutation(REJECT_FRIEND_REQUEST, {
    onCompleted: () => {
      setLoading(false);
      Alert.alert('Success', 'Friend request rejected.');
      refetch();
      if (onSuccess) onSuccess();
    },
    onError: (error) => {
      setLoading(false);
      Alert.alert('Error', `Failed to reject friend request: ${error.message}`);
    },
  });

  // Handle send friend request
  const handleSendRequest = () => {
    if (!currentUser?.id) {
      Alert.alert('Error', 'You must be logged in to send friend requests.');
      return;
    }

    setLoading(true);
    sendFriendRequest({
      variables: {
        targetUserId: userId,
      },
    });
  };

  // Handle accept friend request
  const handleAcceptRequest = (requestId: string) => {
    setLoading(true);
    acceptFriendRequest({
      variables: {
        requestId,
      },
    });
  };

  // Handle reject friend request
  const handleRejectRequest = (requestId: string) => {
    setLoading(true);
    rejectFriendRequest({
      variables: {
        requestId,
      },
    });
  };

  // Determine the current state of the friendship/request
  const friendRequests = data?.requests || [];
  const incomingRequest = friendRequests.find(
    (req: any) => req.requesterId === userId && req.linkedObjectId === currentUser?.id
  );
  const outgoingRequest = friendRequests.find(
    (req: any) => req.requesterId === currentUser?.id && req.linkedObjectId === userId
  );
  
  // Check if users are already friends
  const isFriend = currentUser?.friends?.some((friend: any) => friend.friendId === userId);

  if (queryLoading || loading) {
    return (
      <View style={[styles.container, style]}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  }

  // If users are already friends
  if (isFriend) {
    return (
      <View style={[styles.container, style]}>
        <Button
          title="Friends"
          variant="outline"
          size={size}
          style={styles.button}
          disabled
        />
      </View>
    );
  }

  // If there's an incoming friend request
  if (incomingRequest) {
    return (
      <View style={[styles.container, style]}>
        <ThemedText variant="caption" style={styles.requestText}>Friend Request</ThemedText>
        <View style={styles.buttonRow}>
          <Button
            title="Accept"
            variant="primary"
            size={size}
            style={styles.actionButton}
            onPress={() => handleAcceptRequest(incomingRequest.id)}
          />
          <Button
            title="Reject"
            variant="outline"
            size={size}
            style={styles.actionButton}
            onPress={() => handleRejectRequest(incomingRequest.id)}
          />
        </View>
      </View>
    );
  }

  // If there's an outgoing friend request
  if (outgoingRequest) {
    return (
      <View style={[styles.container, style]}>
        <Button
          title="Request Sent"
          variant="outline"
          size={size}
          style={styles.button}
          onPress={() => handleRejectRequest(outgoingRequest.id)}
        />
        <ThemedText variant="caption" style={styles.cancelText}>Tap to cancel</ThemedText>
      </View>
    );
  }

  // Default state - no relationship yet
  return (
    <View style={[styles.container, style]}>
      <Button
        title="Add Friend"
        variant="outline"
        size={size}
        style={styles.button}
        onPress={handleSendRequest}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
        pointerEvents: 'auto'

  },
  button: {
    minWidth: 120,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  requestText: {
    marginBottom: 8,
  },
  cancelText: {
    marginTop: 4,
    fontSize: 12,
    opacity: 0.7,
  },
});
