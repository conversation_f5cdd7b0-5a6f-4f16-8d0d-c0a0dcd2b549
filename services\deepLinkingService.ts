import * as Linking from 'expo-linking';

// Define the types of deep links we support
export type DeepLinkType = 'redeem';

// Define the structure of a deep link
export interface DeepLink {
  type: DeepLinkType;
  params?: Record<string, string>;
}

/**
 * Parse a URL into a DeepLink object
 * @param url The URL to parse
 * @returns A DeepLink object or null if the URL is not a valid deep link
 */
export function parseDeepLink(url: string): DeepLink | null {
  try {
    // Basic URL validation
    if (!url || typeof url !== 'string') {
      console.warn('Invalid URL provided to parseDeepLink:', url);
      return null;
    }

    // Extract the path and query parameters
    const [path, queryString] = url.split('?');
    const pathSegments = path.split('/').filter(Boolean);

    // Validate the path
    if (pathSegments.length !== 1 || pathSegments[0] !== 'redeem') {
      console.warn('Invalid deep link path:', path);
      return null;
    }

    // Parse query parameters
    const queryParams: Record<string, string> = {};
    if (queryString) {
      queryString.split('&').forEach(param => {
        const [key, value] = param.split('=');
        if (key && value) {
          queryParams[key] = decodeURIComponent(value);
        }
      });
    }

    // Return the deep link
    return { type: 'redeem', params: queryParams };
  } catch (error) {
    console.error('Error parsing deep link:', error);
    return null;
  }
}

/**
 * Handle a deep link
 * @param url The URL to handle
 * @returns True if the URL was handled, false otherwise
 */
export function handleDeepLink(url: string): boolean {
  // Parse the URL
  const deepLink = parseDeepLink(url);

  // If the URL is not a valid deep link, return false
  if (!deepLink) {
    return false;
  }

  // Handle the redeem link
  if (deepLink.type === 'redeem') {
    console.log('Handling redeem link with params:', deepLink.params);
    // Add logic to handle the redeem link here
    return true;
  }

  return false;
}

/**
 * Initialize deep linking
 * @returns A function to remove the event listeners
 */
export function initializeDeepLinking(): (() => void) | undefined {
  try {
    // Handle deep links that open the app
    const handleUrl = (event: { url: string }) => {
      console.log('Received deep link URL:', event.url); // Log the incoming URL
      try {
        handleDeepLink(event.url);
      } catch (error) {
        console.error('Error handling deep link:', error);
      }
    };

    // Add event listeners
    let subscription;
    try {
      subscription = Linking.addEventListener('url', handleUrl);
    } catch (error) {
      console.error('Error adding event listener:', error);
    }

    // Handle deep links that the app was opened with
    try {
      Linking.getInitialURL().then((url) => {
        if (url) {
          console.log('Initial deep link URL:', url); // Log the initial URL
          handleDeepLink(url);
        }
      }).catch(error => {
        console.error('Error getting initial URL:', error);
      });
    } catch (error) {
      console.error('Error setting up initial URL handling:', error);
    }

    // Return a function to remove the event listeners
    if (subscription) {
      return () => {
        try {
          subscription.remove();
        } catch (error) {
          console.error('Error removing event listener subscription:', error);
        }
      };
    }
  } catch (error) {
    console.error('Error initializing deep linking:', error);
  }

  return undefined;
}
