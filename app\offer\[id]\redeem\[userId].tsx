import { useMutation, useQuery } from '@apollo/client';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '../../../../components/ui/ThemedText';
import { ThemedView } from '../../../../components/ui/ThemedView';
import { ADD_REDEMPTION, GET_OFFER } from '../../../../lib/graphql-operations';

export default function RedeemOfferScreen() {
  const { id, userId } = useLocalSearchParams<{ id: string; userId: string }>();
  const router = useRouter();
  const [redemptionStatus, setRedemptionStatus] = useState<'success' | 'failure' | null>(null);

  // Query offer details
  const { data, loading, error } = useQuery(GET_OFFER, {
    variables: { id },
  });

  // Add redemption mutation
  const [addRedemption] = useMutation(ADD_REDEMPTION);

  useEffect(() => {
    if (!data?.offer) return;

    const offer = data.offer;
    const now = new Date();

    if (offer.expires && new Date(offer.expires) < now) {
      setRedemptionStatus('failure');
      return;
    }

    if (offer.redeems >= offer.maxRedeems) {
      setRedemptionStatus('failure');
      return;
    }

    const userRedemptions = offer.redeemers?.filter((redeemer: { id: string }) => redeemer.id === userId).length || 0;

    if (userRedemptions >= offer.maxRedeems) {
      setRedemptionStatus('failure');
      return;
    }

    // Add redemption
    addRedemption({
      variables: { input: { offerId: id, userId } },
      onCompleted: () => setRedemptionStatus('success'),
      onError: () => setRedemptionStatus('failure'),
    });
  }, [data, id, userId, addRedemption]);

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading...</ThemedText>
      </ThemedView>
    );
  }

  if (error || redemptionStatus === 'failure') {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>Redemption failed. Please try again.</ThemedText>
      </ThemedView>
    );
  }

  if (redemptionStatus === 'success') {
    return (
      <ThemedView style={styles.successContainer}>
        <ThemedText style={styles.successText}>Redemption successful! Thank you for using this offer.</ThemedText>
      </ThemedView>
    );
  }

  return null;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  successText: {
    color: 'green',
    textAlign: 'center',
  },
});
