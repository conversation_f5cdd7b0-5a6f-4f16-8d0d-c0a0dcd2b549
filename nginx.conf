# nginx.conf
worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen 8080; # Listen on port 8080 as expected by Fly.io
        listen [::]:8080; # Listen on IPv6

        # Set the root directory for serving files
        # This is where your static Expo files will be copied inside the container
        root /usr/share/nginx/html;

        # This is crucial for single-page applications (SPAs) like Expo Router.
        # It tries to find a file/directory first, then falls back to index.html
        location / {
            try_files $uri $uri/index.html $uri.html /index.html =404;
        }

        # Optional: Custom 404 page for clean SPA routing (redirects to index.html)
        error_page 404 /index.html;
    }
}