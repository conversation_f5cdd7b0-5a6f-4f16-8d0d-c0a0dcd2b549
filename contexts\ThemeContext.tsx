import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import { lightTheme, darkTheme, Theme } from '../styles/theme';

// Types
interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (mode: 'light' | 'dark') => void;
}

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Provider component
export function ThemeProvider({ children }: { children: ReactNode }) {
  const colorScheme = useColorScheme();
  const [isDark, setIsDark] = useState(colorScheme === 'dark');
  
  // Update theme based on system preference
  useEffect(() => {
    setIsDark(colorScheme === 'dark');
  }, [colorScheme]);
  
  // Toggle theme
  const toggleTheme = () => {
    setIsDark(!isDark);
  };
  
  // Set specific theme
  const setTheme = (mode: 'light' | 'dark') => {
    setIsDark(mode === 'dark');
  };
  
  // Get current theme
  const theme = isDark ? darkTheme : lightTheme;
  
  // Context value
  const value = {
    theme,
    isDark,
    toggleTheme,
    setTheme
  };
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// Hook for using the theme context
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context.theme;
}

// Hook for using theme functions
export function useThemeControls() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeControls must be used within a ThemeProvider');
  }
  return {
    isDark: context.isDark,
    toggleTheme: context.toggleTheme,
    setTheme: context.setTheme
  };
}
