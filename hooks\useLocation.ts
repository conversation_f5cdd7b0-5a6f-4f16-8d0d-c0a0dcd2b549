import * as turf from '@turf/turf';
import * as Location from 'expo-location';
import { useEffect, useState } from 'react';
import { useZone } from '../contexts/ZoneContext';
import { Zone } from '../types';

interface UseLocationOptions {
  watchPosition?: boolean;
  accuracy?: Location.Accuracy;
}

export function useLocation(options: UseLocationOptions = {}) {
  const {
    watchPosition = false,
    accuracy = Location.Accuracy.Balanced,
  } = options;

  const { zones, currentZone } = useZone();
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [permission, setPermission] = useState<boolean | null>(null);
  const [zonesNearby, setZonesNearby] = useState<Zone[]>([]);
  const [isInCurrentZone, setIsInCurrentZone] = useState<boolean>(false);

  // Request location permissions and get initial location
  useEffect(() => {
    let locationSubscription: Location.LocationSubscription | null = null;

    const setupLocation = async () => {
      try {
        setLoading(true);

        // Request permissions
        const { status } = await Location.requestForegroundPermissionsAsync();
        setPermission(status === 'granted');

        if (status !== 'granted') {
          setError('Permission to access location was denied');
          setLoading(false);
          return;
        }

        // Get initial location
        const initialLocation = await Location.getCurrentPositionAsync({
          accuracy,
        });

        setLocation(initialLocation);

        // Set up location watching if requested
        if (watchPosition) {
          locationSubscription = await Location.watchPositionAsync(
            {
              accuracy,
              distanceInterval: 10, // Update every 10 meters
              timeInterval: 5000, // Or every 5 seconds
            },
            (newLocation) => {
              setLocation(newLocation);
            }
          );
        }

        setLoading(false);
      } catch (err) {
        setError('Error getting location');
        setLoading(false);
        console.error('Error setting up location:', err);
      }
    };

    setupLocation();

    // Clean up subscription
    return () => {
      if (locationSubscription) {
        locationSubscription.remove();
      }
    };
  }, [watchPosition, accuracy]);

  // Check if user is in zones when location changes
  useEffect(() => {
    if (!location || !zones || zones.length === 0) {
      return;
    }

    // Create a point from user's location
    const point = turf.point([
      loc.lng,
      loc.lat,
    ]);

    // Find zones that contain the user's location
    const nearby = zones.filter((zone) => {
      if (!zone.boundary?.coordinates || zone.boundary.coordinates.length === 0) {
        return false;
      }

      try {
        const polygon = turf.polygon(zone.boundary.coordinates);
        return turf.booleanPointInPolygon(point, polygon);
      } catch (error) {
        console.error('Error checking if point is in polygon:', error);
        return false;
      }
    });

    setZonesNearby(nearby);

    // Check if user is in current zone
    if (currentZone && currentZone.boundary?.coordinates) {
      try {
        const currentZonePolygon = turf.polygon(currentZone.boundary.coordinates);
        setIsInCurrentZone(turf.booleanPointInPolygon(point, currentZonePolygon));
      } catch (error) {
        console.error('Error checking if point is in current zone:', error);
        setIsInCurrentZone(false);
      }
    } else {
      setIsInCurrentZone(false);
    }
  }, [location, zones, currentZone]);

  // Get distance between two points in meters
  const getDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number => {
    if (!lat1 || !lon1 || !lat2 || !lon2) return 0;

    const from = turf.point([lon1, lat1]);
    const to = turf.point([lon2, lat2]);
    const options = { units: 'meters' as turf.Units };

    return turf.distance(from, to, options);
  };

  // Get distance to a place
  const getDistanceToPlace = (placeLat: number, placeLon: number): number => {
    if (!location) return 0;

    return getDistance(
      loc.lat,
      loc.lng,
      placeLat,
      placeLon
    );
  };

  // Format distance
  const formatDistance = (distance: number): string => {
    if (distance < 1000) {
      return `${Math.round(distance)}m`;
    } else {
      return `${(distance / 1000).toFixed(1)}km`;
    }
  };

  return {
    location,
    loading,
    error,
    permission,
    zonesNearby,
    isInCurrentZone,
    getDistanceToPlace,
    formatDistance,
  };
}
