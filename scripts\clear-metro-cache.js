/**
 * <PERSON><PERSON>t to clear Metro bundler cache and other caches that might cause bundling issues
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🧹 Starting deep cache cleaning process...');

// Project root directory
const rootDir = path.resolve(__dirname, '..');

// Paths to clear
const pathsToClear = [
  // Metro bundler cache
  path.join(rootDir, 'node_modules', '.cache', 'metro'),
  path.join(rootDir, 'node_modules', '.cache', 'babel-loader'),
  // Expo web cache
  path.join(rootDir, '.expo'),
  path.join(rootDir, 'web-build'),
  // Metro cache
  path.join(rootDir, '.metro'),
  // Temporary files
  path.join(rootDir, '.tmp'),
  // Babel cache
  path.join(os.tmpdir(), 'metro-babel-cache'),
  // React Native cache
  path.join(os.tmpdir(), 'react-native-packager-cache'),
  // Metro cache in temp directory
  path.join(os.tmpdir(), 'metro-cache'),
  // Haste map cache
  path.join(rootDir, 'node_modules', '.cache', 'haste-map'),
];

// Clear each path
pathsToClear.forEach(cachePath => {
  if (fs.existsSync(cachePath)) {
    try {
      console.log(`🗑️  Removing: ${cachePath}`);
      fs.rmSync(cachePath, { recursive: true, force: true });
      console.log(`✅ Successfully removed: ${cachePath}`);
    } catch (error) {
      console.error(`❌ Error removing ${cachePath}:`, error.message);
    }
  } else {
    console.log(`ℹ️  Path does not exist, skipping: ${cachePath}`);
  }
});

// Clear Metro cache using Expo CLI
console.log('\n🧹 Clearing Metro cache using Expo CLI...');
try {
  execSync('npx expo start --clear --non-interactive --no-dev', { stdio: 'inherit' });
  console.log('✅ Metro cache cleared via Expo CLI');
} catch (error) {
  console.log('❌ Error clearing Metro cache via Expo CLI, continuing anyway...');
}

// Clear Watchman cache if available
console.log('\n🧹 Clearing Watchman cache...');
try {
  execSync('watchman watch-del-all', { stdio: 'inherit' });
  console.log('✅ Watchman cache cleared');
} catch (error) {
  console.log('ℹ️  Watchman not available or error clearing cache.');
}

console.log('\n🎉 All caches cleared! Try running the app now with:');
console.log('npm run web-reset');
console.log('\nIf you still encounter issues, try:');
console.log('npm run web-full-reset');
