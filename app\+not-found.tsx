import { Stack, router } from 'expo-router';
import { StyleSheet } from 'react-native';
import { Button } from '../components/ui/Button';
import { ThemedText } from '../components/ui/ThemedText';
import { ThemedView } from '../components/ui/ThemedView';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <ThemedView style={styles.container}>
        <ThemedText variant="title" style={styles.title}>Page Not Found</ThemedText>
        <ThemedText style={styles.message}>
          The page you're looking for doesn't exist or has been moved.
        </ThemedText>
        <Button
          title="Go to Home"
          onPress={() => router.replace('/')}
          style={styles.button}
        />
      </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    marginBottom: 16,
  },
  message: {
    textAlign: 'center',
    marginBottom: 24,
  },
  button: {
    minWidth: 150,
  },
});
