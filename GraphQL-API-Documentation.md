# GraphQL API Documentation

This document provides comprehensive documentation for the GraphQL API used in the Town application. It covers all available queries, mutations, and data types to help you build client-side applications.

## Table of Contents

- [Authentication](#authentication)
- [GraphQL Basics](#graphql-basics)
- [Queries](#queries)
- [Mutations](#mutations)
- [Subscriptions](#subscriptions)
- [Data Types](#data-types)
- [Common Patterns](#common-patterns)
- [Erro<PERSON>ling](#error-handling)

## Authentication

The API uses JWT (JSON Web Token) authentication.

### Getting a Token

To authenticate, you need to obtain a JWT token by either:

1. **Creating a new user**:
   ```graphql
   mutation CreateUser {
     createUser(email: "<EMAIL>", password: "password", zone: "zone-id") {
       access_token
       user {
         id
         emails {
           address
           verified
         }
       }
     }
   }
   ```

2. **Logging in** (via REST endpoint):
   ```
   POST /auth/login
   Content-Type: application/json

   {
     "email": "<EMAIL>",
     "password": "password"
   }
   ```

### JWT Token Structure

The JWT token payload should include the following fields:

- `sub`: The user's ID (same as `_id` in the database)
- `_id`: Alternative field for the user's ID (for backward compatibility)
- `email`: The user's email address
- `roles`: Array of user roles
- `zone`: The user's home zone ID
- `stripeCustomerId`: Optional Stripe customer ID
- `iat`: Issued at timestamp
- `exp`: Expiration timestamp

Example token payload:
```json
{
  "sub": "user-123",
  "_id": "user-123",
  "email": "<EMAIL>",
  "roles": ["user"],
  "zone": "zone-456",
  "stripeCustomerId": null,
  "iat": 1620000000,
  "exp": 1620604800
}
```

If the `sub` or `_id` field is missing, the server will attempt to find the user by email address.

### JWT Lifecycle and Authentication Flow

The JWT authentication flow works as follows:

1. **Token Generation**:
   - When a user logs in or registers, the backend server generates a JWT token
   - The token is signed with a secret key (JWT_SECRET) and includes an expiration time (typically 7 days)
   - The token is returned to the client

2. **Token Storage**:
   - The client (browser or mobile app) stores the token, typically in:
     - Browser localStorage or cookies for web applications
     - Secure storage for mobile applications
   - The NextJS client stores the token in a cookie named `auth_token`

3. **Token Usage**:
   - The client includes the token in the `Authorization` header for all authenticated requests
   - Format: `Authorization: Bearer <token>`
   - The client should also include the zone ID in the `x-zone-id` header

4. **Token Verification**:
   - When the backend receives a request with a token, it:
     - Extracts the token from the `Authorization` header
     - Verifies the token signature using the JWT_SECRET
     - Checks that the token hasn't expired
     - Extracts the user information from the token payload
     - Attempts to find the user in the database:
       - First by ID (`sub` or `_id` field)
       - If ID is missing or user not found, falls back to email lookup
     - Sets the user context for the request
     - Uses the user's zone as the current zone (if no zone is specified)

5. **Token Expiration**:
   - When a token expires, the client should obtain a new token by:
     - Redirecting the user to the login page
     - Using a refresh token flow (if implemented)
   - The backend will reject requests with expired tokens

6. **Authentication Responsibilities**:
   - **Backend Server (NestJS)**:
     - Handles user authentication (login, register)
     - Generates and validates JWT tokens
     - Provides user information through the GraphQL API
     - Enforces access control for protected resources

   - **Frontend Server (NextJS)**:
     - Stores and manages the JWT token
     - Includes the token in requests to the backend
     - Handles UI for login, registration, and authentication errors
     - Manages user sessions and redirects unauthenticated users
     - May proxy authentication requests to the backend

This flow ensures that even if the token is missing some fields, the server can still authenticate the user as long as the email is valid.

### Using the Token

Include the token in the `Authorization` header for authenticated requests:

```
Authorization: Bearer your-jwt-token
```

### Authentication Rules

- **Queries**: Most queries don't require authentication
- **Mutations**: Most mutations require authentication
- Specific operations like user profile updates always require authentication

### Authentication APIs

The following REST endpoints are available for authentication:

#### Login
```
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

Response:
```json
{
  "access_token": "jwt-token",
  "user": {
    "_id": "user-id",
    "emails": [
      {
        "address": "<EMAIL>",
        "verified": false
      }
    ],
    "roles": ["user"]
  }
}
```

#### Register
```
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password",
  "zone": "zone-id"
}
```

Response:
```json
{
  "access_token": "jwt-token",
  "user": {
    "_id": "user-id",
    "emails": [
      {
        "address": "<EMAIL>",
        "verified": false
      }
    ],
    "roles": ["user"]
  }
}
```

#### Change Password
```
POST /auth/change-password
Authorization: Bearer jwt-token
Content-Type: application/json

{
  "oldPassword": "current-password",
  "newPassword": "new-password"
}
```

Response:
```json
{
  "success": true
}
```

#### Verify Email
```
POST /auth/verify-email
Content-Type: application/json

{
  "token": "verification-token"
}
```

#### Reset Password
```
POST /auth/reset-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### Role Management (Admin Only)
```
POST /auth/add-role
Authorization: Bearer jwt-token
Content-Type: application/json

{
  "userId": "user-id",
  "role": "role-name"
}
```

```
POST /auth/remove-role
Authorization: Bearer jwt-token
Content-Type: application/json

{
  "userId": "user-id",
  "role": "role-name"
}
```

#### Get User Profile
```
GET /auth/profile
Authorization: Bearer jwt-token
```

Response:
```json
{
  "_id": "user-id",
  "emails": [
    {
      "address": "<EMAIL>",
      "verified": false
    }
  ],
  "roles": ["user"],
  "zone": "zone-id",
  "userProfile": {
    "_id": "profile-id", // Different from user-id
    "userId": "user-id", // References the user
    "firstName": "John",
    "lastName": "Doe",
    "avatar": "avatar-url",
    "visible": true
  }
}
```

> **Note**: Profiles now have their own unique `_id` and use a `userId` field to reference the user. This change was made to accommodate MongoDB's limitations with using the same ID across collections.

## GraphQL Basics

### Making Requests

All GraphQL requests are made to a single endpoint:

```
POST /graphql
```

The request body should contain:

```json
{
  "query": "...",
  "variables": { ... }
}
```

### Using Variables

Variables make queries reusable and safer:

```graphql
query GetPlace($id: String!) {
  place(id: $id) {
    id
    name
    desc
  }
}
```

With variables:

```json
{
  "id": "place-123"
}
```

## Queries

The API supports the following query patterns:

### Single Entity Queries

Each entity type has a query to fetch a single item by ID:

```graphql
query GetPlace {
  place(id: "place-id") {
    id
    name
    desc
    zone
  }
}
```

### Collection Queries

Each entity type has a collection query that supports filtering, pagination, and sorting:

```graphql
query GetPlaces {
  places(
    criteria: { zone: "zone-id" },
    limit: 10,
    skip: 0,
    sort: { createdAt: -1 }
  ) {
    id
    name
    desc
  }
}
```

#### Common Query Parameters

- `criteria`: JSON object for filtering (supports MongoDB query syntax)
- `limit`: Maximum number of results to return
- `skip`: Number of results to skip (for pagination)
- `sort`: JSON object for sorting (e.g., `{ createdAt: -1 }` for newest first)

### Special Queries

- `placesInPolygon`: Find places within a geographic polygon
  ```graphql
  query PlacesInPolygon {
    placesInPolygon(
      coordinates: [[[lng1, lat1], [lng2, lat2], [lng3, lat3], [lng1, lat1]]],
      limit: 10
    ) {
      id
      name
      loc {
        lat
        lng
      }
    }
  }
  ```

## Mutations

### Creating Entities

Each entity type has a create mutation that accepts an input object:

```graphql
mutation CreatePlace {
  createPlace(
    input: {
      name: "New Place",
      desc: "Description",
      zone: "zone-id",
      loc: { lat: 37.7749, lng: -122.4194 }
    }
  ) {
    id
    name
  }
}
```

### Updating Entities

Each entity type has an update mutation:

```graphql
mutation UpdatePlace {
  updatePlace(
    id: "place-id",
    input: {
      name: "Updated Place Name",
      desc: "Updated description"
    }
  ) {
    id
    name
    desc
  }
}
```

### Deleting Entities

Each entity type has a delete mutation:

```graphql
mutation DeletePlace {
  deletePlace(id: "place-id")
}
```

Delete mutations return a boolean indicating success.

### User Registration

```graphql
mutation CreateUser {
  createUser(
    email: "<EMAIL>",
    password: "password",
    zone: "zone-id"
  ) {
    access_token
    user {
      id
      emails {
        address
      }
    }
  }
}
```

## Subscriptions

The API supports real-time updates through GraphQL subscriptions over WebSockets. Subscriptions allow clients to receive live updates when data changes on the server.

### WebSocket Connection

To use subscriptions, clients need to establish a WebSocket connection to the server. The WebSocket endpoint is derived from the GraphQL HTTP endpoint by replacing `http://` with `ws://` or `https://` with `wss://`.

For example, if your GraphQL endpoint is:
```
http://localhost:3003/graphql
```

Then your WebSocket endpoint would be:
```
ws://localhost:3003/graphql
```

### Authentication with WebSockets

When establishing a WebSocket connection, you need to include authentication information in the connection parameters:

```javascript
const wsLink = new GraphQLWsLink(
  createClient({
    url: 'ws://localhost:3003/graphql',
    connectionParams: {
      authorization: `Bearer ${token}`,
      'x-zone-id': zoneId,
    },
  })
);
```

### Available Subscriptions

The API provides the following subscription endpoints:

#### Zone Updates

Subscribe to updates for a specific zone:

```graphql
subscription ZoneUpdates($zoneId: String) {
  zoneUpdates(zoneId: $zoneId)
}
```

Parameters:
- `zoneId` (optional): The ID of the zone to subscribe to. If not provided, you'll receive updates for all zones.

The subscription returns a JSON string containing:
- `zoneId`: The ID of the zone that was updated
- `data`: The updated zone data
- `timestamp`: When the update occurred

#### New Content

Subscribe to new content created in a zone:

```graphql
subscription NewContent($zoneId: String, $contentType: String) {
  newContent(zoneId: $zoneId, contentType: $contentType)
}
```

Parameters:
- `zoneId` (optional): The ID of the zone to subscribe to. If not provided, you'll receive updates for all zones.
- `contentType` (optional): The type of content to subscribe to (e.g., "post", "happening", "offer"). If not provided, you'll receive updates for all content types.

The subscription returns a JSON string containing:
- `zoneId`: The ID of the zone where the content was created
- `contentType`: The type of content that was created
- `data`: The content data
- `timestamp`: When the content was created

### Client Implementation

Here's an example of how to implement subscriptions in a client application using Apollo Client:

```javascript
import { ApolloClient, ApolloLink, HttpLink, InMemoryCache, split } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { getMainDefinition } from '@apollo/client/utilities';
import { createClient } from 'graphql-ws';

// HTTP link for queries and mutations
const httpLink = new HttpLink({
  uri: 'http://localhost:3003/graphql',
});

// WebSocket link for subscriptions
const wsLink = new GraphQLWsLink(
  createClient({
    url: 'ws://localhost:3003/graphql',
    connectionParams: async () => {
      const token = localStorage.getItem('auth_token');
      const zoneId = localStorage.getItem('current_zone_id');

      return {
        authorization: token ? `Bearer ${token}` : '',
        'x-zone-id': zoneId || '',
      };
    },
  })
);

// Auth link to add the token to HTTP requests
const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('auth_token');
  const zoneId = localStorage.getItem('current_zone_id');

  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
      'x-zone-id': zoneId || '',
    },
  };
});

// Split link based on operation type
const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return (
      definition.kind === 'OperationDefinition' &&
      definition.operation === 'subscription'
    );
  },
  wsLink,
  authLink.concat(httpLink)
);

// Create Apollo Client
const client = new ApolloClient({
  link: splitLink,
  cache: new InMemoryCache(),
});
```

### Using Subscriptions in React

Here's how to use subscriptions in a React component:

```javascript
import { gql, useSubscription } from '@apollo/client';

const ZONE_UPDATES_SUBSCRIPTION = gql`
  subscription ZoneUpdates($zoneId: String) {
    zoneUpdates(zoneId: $zoneId)
  }
`;

function ZoneUpdatesComponent({ zoneId }) {
  const { data, loading, error } = useSubscription(ZONE_UPDATES_SUBSCRIPTION, {
    variables: { zoneId },
  });

  if (loading) return <p>Waiting for updates...</p>;
  if (error) return <p>Error: {error.message}</p>;

  // Parse the JSON string into an object
  const update = data ? JSON.parse(data.zoneUpdates) : null;

  return (
    <div>
      <h3>Zone Updates</h3>
      {update && (
        <div>
          <p>Zone ID: {update.zoneId}</p>
          <p>Updated at: {new Date(update.timestamp).toLocaleString()}</p>
          <pre>{JSON.stringify(update.data, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
```

### Best Practices for Subscriptions

1. **Connection Management**:
   - Handle WebSocket connection errors gracefully
   - Implement reconnection logic for dropped connections
   - Close subscriptions when components unmount to prevent memory leaks

2. **Performance Considerations**:
   - Use specific filters (zoneId, contentType) to limit the number of updates
   - Only subscribe to the data you need
   - Consider debouncing or throttling updates on the client side for high-frequency events

3. **Security**:
   - Always include authentication tokens in connection parameters
   - Be aware that subscriptions are subject to the same authorization rules as queries and mutations
   - Sensitive data should not be included in subscription payloads

4. **Testing**:
   - Test subscription behavior with mock WebSocket servers
   - Verify that subscriptions receive updates when the corresponding data changes
   - Ensure that subscriptions are properly cleaned up when no longer needed

## Data Types

The API includes the following main entity types:

### User

```graphql
type User {
  id: String!
  emails: [Email!]
  registered_emails: [Email!]
  status: String
  zone: String!
  userProfile: Profile
  lastOnline: DateTime
  visible: Boolean
  categories: [String!]
  createdAt: String
  updatedAt: String
  roles: [String!]
  loc: Location
  stripe: StripePayments
  # Relationships
  ownedPlaces: [Place!]
  ownedPosts: [Post!]
  ownedHappenings: [Happening!]
  ownedOffers: [Offer!]
  placeMembership: [String!]
  placeMembershipDetails: [Place!]
  friends: [User!]
  # ... other owned entities
}
```

> **Note**: The `id` field in the GraphQL schema corresponds to the MongoDB `_id` field. In the database, user records have an `_id` field (not a `userId` field). Other collections use `userId` to reference users.

### Place

```graphql
type Place {
  id: String!
  name: String!
  desc: String
  type: String!
  zone: String
  active: Boolean!
  loc: Location
  geometry: Geometry
  properties: JSON
  avatar: String
  userId: String
  createdAt: String
  updatedAt: String
  # Relationships
  owner: User
  members: [User!]
  comments: [Comment!]
  likes: [Like!]
  happenings: [Happening!]
  offers: [Offer!]
  faves: [Fave!]
  visits: [Visit!]
  posts: [Post!]
  proximityads: [ProximityAd!]
  checks: [Check!]
  categoryDocs: [Category!]
  categories: [String!]
}
```

### Zone

```graphql
type Zone {
  id: String!
  title: String!
  center: Center
  createdAt: String
  updatedAt: String
  appName: String
  temp: String
  skytext: String
  weatherUpdatedAt: String
  subscriptionsEnabled: Boolean
  useGiftCards: Boolean
  giftcardHoldback: Float
}
```

### Post, Happening, Offer, and Other Content Types

These content types share similar structures:

```graphql
type Post {
  id: String!
  title: String!
  desc: String
  zone: String!
  avatar: String
  userId: String!
  createdAt: String
  updatedAt: String
  categories: [String!]
  # Relationships
  owner: User
  likes: [Like!]
  comments: [Comment!]
  place: Place
  categoryDocs: [Category!]
}
```

### Interaction Types

```graphql
type Like {
  id: String!
  userId: String!
  linkedObjectId: String!
  objectType: String!
  createdAt: String
  updatedAt: String
  # Relationships
  owner: User
  # Dynamic relationships based on objectType
  place: Place
  post: Post
  happening: Happening
  offer: Offer
  comment: Comment
  page: Page
  fave: Fave
  proximityAd: ProximityAd
  user: User
}

type Comment {
  id: String!
  userId: String!
  body: String!
  linkedObjectId: String!
  objectType: String!
  createdAt: String
  updatedAt: String
  # Relationships
  owner: User
  childComments: [Comment!]
  likes: [Like!]
  # Dynamic relationships based on objectType
  place: Place
  post: Post
  happening: Happening
  offer: Offer
  page: Page
  fave: Fave
  parentComment: Comment
}
```

### Common Input Types

Most entity types have corresponding input types for creation and updates:

```graphql
input CreatePlaceInput {
  name: String!
  desc: String
  zone: String!
  loc: LocationInput
  # ... other fields
}

input UpdatePlaceInput {
  name: String
  desc: String
  # ... other fields (all optional)
}
```

## Common Patterns

### Entity Relationships

Entities are related through reference fields and resolver functions:

1. **Direct References**: Using ID fields like `userId` or `placeId`
2. **Generic References**: Using `linkedObjectId` and `objectType` fields
3. **Resolved References**: Fields like `owner`, `place`, `comments` that resolve to full objects

We follow a standardized approach to relationships:

1. **Consistent Naming**: We use the same field name for the relationship in both the entity and GraphQL schema
   - For example, we use `comments` not `commentDetails`
   - For linked objects, we use the object type name (e.g., `place`, `post`) not `placeDetails` or `postDetails`

2. **Relationship Types**:
   - **One-to-Many**: One entity has many related entities (e.g., a Post has many Comments)
   - **Many-to-One**: Many entities relate to one entity (e.g., many Comments relate to one Post)
   - **Many-to-Many**: Many entities relate to many entities (e.g., Posts have many Categories, and Categories can be used by many Posts)

3. **Implementation**:
   - Relationships are defined using the `@Relationship` decorator
   - All relationship fields are nullable with `{ nullable: true }`
   - Input types use string arrays for simplicity, which are converted to relationship objects in resolvers
   - Relationship fields are resolved through dedicated resolver methods that handle the relationship logic

4. **Available Relationship Resolvers**:
   - **User**: `userProfile` - Resolves to the user's profile
   - **Profile**: `user` - Resolves to the profile's user
   - **Post**: `placeDetails`, `owner`, `categoryDocs`, `likes`, `comments` - Resolves to related entities
   - **Category**: `places`, `happenings`, `offers`, `posts` - Resolves to entities using this category
   - **Offer**: `placeDetails`, `owner`, `categoryDocs`, `likes`, `comments`, `checks` - Resolves to related entities
   - **Happening**: `placeDetails`, `owner`, `categoryDocs`, `likes`, `comments` - Resolves to related entities
   - **ProximityAd**: `placeDetails`, `owner`, `likes`, `checks` - Resolves to related entities
   - **Place**: `owner`, `members`, `comments`, `likes`, `happenings`, `offers`, `posts`, `proximityads`, `checks`, `categoryDocs` - Resolves to related entities
   - **Comment**: Various entity resolvers based on `objectType` - Resolves to the commented entity
   - **Like**: Various entity resolvers based on `objectType` - Resolves to the liked entity

5. **Example Relationship Queries**:

   **Query a User with their Profile**:
   ```graphql
   query GetUserWithProfile {
     user(id: "user-id") {
       id
       emails {
         address
       }
       userProfile {
         id
         firstName
         lastName
         avatar
         bio
       }
     }
   }
   ```

   **Query a Profile with its User**:
   ```graphql
   query GetProfileWithUser {
     profile(id: "profile-id") {
       id
       firstName
       lastName
       user {
         id
         emails {
           address
         }
         roles
       }
     }
   }
   ```

   **Query a Post with Related Entities**:
   ```graphql
   query GetPostWithRelationships {
     post(id: "post-id") {
       id
       title
       desc
       placeDetails {
         id
         name
       }
       owner {
         id
         emails {
           address
         }
       }
       categoryDocs {
         id
         title
         class
       }
       likes {
         id
         owner {
           id
         }
       }
       comments {
         id
         body
         owner {
           id
         }
       }
     }
   }
   ```

   **Query a Category with Related Entities**:
   ```graphql
   query GetCategoryWithRelatedEntities {
     category(id: "category-id") {
       id
       title
       class
       places {
         id
         name
       }
       happenings {
         id
         title
       }
       offers {
         id
         title
       }
       posts {
         id
         title
       }
     }
   }
   ```

### User Relationships and Roles

#### User Identification

- User records in MongoDB have an `_id` field (not a `userId` field)
- Other collections use `userId` to reference users
- In GraphQL responses, the MongoDB `_id` field is mapped to `id`
- For backward compatibility, the JWT token payload includes both `_id` and `userId` with the same value

#### User-Profile Relationship

The User-Profile relationship is implemented as a one-to-one relationship with separate collections:

1. **Profile Creation**: Profiles are created on-demand when first accessed, not automatically when a user is created
   - When `getUserProfile` is called for a user without a profile, a new profile is automatically created
   - This happens in the `userProfile` resolver and the `getUserProfile` service method
   - The new profile is created with default values including `visible: true`

2. **Profile Structure**:
   - Profiles have their own unique `_id` field that is different from the user's ID
   - Profiles reference the user through a `userId` field that matches the user's `_id`
   - The relationship is bidirectional with:
     - User has a `userProfile` field that resolves to the Profile
     - Profile has a `user` field that resolves to the User

3. **Querying Profiles**:
   - To get a profile by user ID, use the `profileByUserId` query:
   ```graphql
   query {
     profileByUserId(userId: "user-id") {
       id
       firstName
       lastName
       avatar
     }
   }
   ```

#### User Roles

Users can have multiple roles stored in two places:

1. **User Document**: The `roles` array in the user document contains role names:
   - `"user"`: Default role assigned to all registered users
   - `"admin"`: Administrative users with elevated permissions
   - `"placeOwner"`: Users who own one or more places

2. **Role-Assignment Collection**: Roles are also stored in a separate `role-assignment` collection:
   - Each document contains a role ID, user ID reference, and inheritedRoles array
   - This allows for more complex role hierarchies and inheritance
   - The system synchronizes roles between the user document and role-assignment collection

Roles determine what actions a user can perform:
- Admin users can see all users in their zone, including those with `visible: false`
- Admin users can modify any entity in their zone
- Regular users can only modify their own entities
- Place owners can manage their own places and related content

#### Place Membership and Ownership

Places can have both owners and members:

1. **Place Ownership**:
   - Each place has a single owner, referenced by the `userId` field in the Place document
   - The owner has full control over the place and its content
   - Users with places they own are automatically assigned the `"placeOwner"` role
   - A user's owned places can be accessed through the `ownedPlaces` field on the User type

2. **Place Membership**:
   - Places can have multiple members (employees) who have access to offer redemption logic
   - Members are stored in the `placeMembership` array in the User document as place IDs
   - The `placeMembershipDetails` field on the User type resolves to the actual Place objects
   - Place membership allows users to manage certain aspects of a place without being the owner

Example query for a user's place memberships:

```graphql
query GetUserPlaceMemberships {
  user(id: "user-id") {
    id
    placeMembershipDetails {
      id
      name
      desc
      owner {
        id
        emails {
          address
        }
      }
    }
  }
}
```

#### Friend Relationships

Friend relationships are stored in the `friends` collection with reciprocal entries:

```graphql
type Friend {
  id: String!
  userId: String!
  friendId: String!
  createdAt: String
  updatedAt: String
}
```

Each friendship has two records with swapped `userId` and `friendId` fields. This bidirectional relationship ensures that friendship is mutual and can be queried from either user's perspective.

#### Querying User Friends

To get a user's friends, use the `friends` field on the User type:

```graphql
query GetUserWithFriends {
  user(id: "user-id") {
    id
    emails {
      address
    }
    friends {
      id
      emails {
        address
      }
      userProfile {
        firstName
        lastName
        avatar
      }
    }
  }
}
```

You can also get the current user's friends:

```graphql
query GetCurrentUserWithFriends {
  currentUser {
    id
    friends {
      id
      emails {
        address
      }
      userProfile {
        firstName
        lastName
        avatar
      }
    }
  }
}
```

The `friends` field supports pagination:

```graphql
query GetUserWithPaginatedFriends {
  user(id: "user-id") {
    id
    friends(limit: 10, skip: 0) {
      id
      emails {
        address
      }
    }
  }
}
```

#### Friend Requests

Friend requests are stored in the `requests` collection:

```graphql
type Request {
  id: String!
  linkedObjectId: String!  # Target user ID
  objectType: String!      # Always "users"
  type: String!           # Always "friend"
  requesterId: String!    # Requesting user ID
  createdAt: String
  updatedAt: String
}
```

##### Querying Friend Requests

You can query friend requests using the `requests` query with appropriate filtering criteria:

```graphql
# Get all friend requests
query GetFriendRequests {
  requests(
    criteria: {
      objectType: "users",
      type: "friend"
    }
  ) {
    id
    linkedObjectId
    objectType
    type
    requesterId
    createdAt
  }
}

# Get friend requests for a specific user (where they are the target)
query GetFriendRequestsForUser {
  requests(
    criteria: {
      linkedObjectId: "user-id",
      objectType: "users",
      type: "friend"
    }
  ) {
    id
    linkedObjectId
    objectType
    type
    requesterId
    createdAt
  }
}

# Get friend requests sent by a specific user
query GetFriendRequestsSentByUser {
  requests(
    criteria: {
      requesterId: "user-id",
      objectType: "users",
      type: "friend"
    }
  ) {
    id
    linkedObjectId
    objectType
    type
    requesterId
    createdAt
  }
}
```

You can also retrieve a single request by its ID:

```graphql
query GetSingleRequest {
  request(id: "request-id") {
    id
    linkedObjectId
    objectType
    type
    requesterId
    createdAt
  }
}
```

##### Creating and Managing Friend Requests

To create a new friend request:

```graphql
mutation CreateFriendRequest {
  createRequest(
    input: {
      linkedObjectId: "target-user-id",  # The user receiving the request
      objectType: "users",
      type: "friend",
      requesterId: "requesting-user-id"  # The user sending the request
    }
  ) {
    id
    linkedObjectId
    requesterId
    createdAt
  }
}
```

To update a friend request (rarely needed):

```graphql
mutation UpdateFriendRequest {
  updateRequest(
    id: "request-id",
    input: {
      # Updated fields
    }
  ) {
    id
    updatedAt
  }
}
```

To delete a friend request (e.g., when rejecting it):

```graphql
mutation DeleteFriendRequest {
  deleteRequest(id: "request-id")
}
```

### Working with Likes and Comments

Likes and comments use a generic pattern to reference any entity type:

```graphql
mutation CreateLike {
  createLike(
    input: {
      linkedObjectId: "entity-id",
      objectType: "places" # or "posts", "happenings", etc.
    }
  ) {
    id
  }
}
```

#### Standardized Relationship Approach

We follow a standardized approach to relationships across all entities:

1. **Consistent Naming**:
   - Relationship fields use the same name as the entity type (e.g., `place`, `post`, `comments`, `likes`)
   - We avoid suffixes like "Details" (e.g., we use `place` not `placeDetails`)

2. **Dynamic Relationships**:
   - For entities like `Like` and `Comment` that can reference different object types, we provide type-specific fields
   - These fields are resolved based on the `objectType` field
   - For example, a `Like` with `objectType: "posts"` will have its `post` field populated

3. **Querying Relationships**:
   - You can query relationships directly:

```graphql
query GetPostWithRelationships {
  post(id: "post-id") {
    id
    title
    # Get the related place
    place {
      id
      name
    }
    # Get comments on this post
    comments {
      id
      body
      owner {
        id
        emails {
          address
        }
      }
    }
    # Get likes on this post
    likes {
      id
      owner {
        id
        emails {
          address
        }
      }
    }
  }
}
```

Valid `objectType` values include:
- `"places"`
- `"posts"`
- `"happenings"`
- `"offers"`
- `"comments"`
- `"pages"`
- `"faves"`
- `"proximityads"`
- `"users"`

### Zone-Based Data

Most entities are associated with a zone. The API automatically filters queries based on the current zone.

#### Zone Filtering Architecture

The API implements automatic zone filtering through a two-component system:

1. **ZoneContextMiddleware**: Determines the current zone for each request
2. **ZoneFilteringPlugin**: Automatically applies zone filtering to GraphQL queries

This architecture ensures that data is properly segregated by zone without requiring frontend developers to manually add zone filtering to every query.

#### Automatic Zone Filtering

The API automatically applies zone filtering to the following queries:
- posts
- offers
- happenings
- pages
- campaigns
- challenges
- places
- proximityAds
- faves
- users

You don't need to explicitly include zone filtering in your queries, as it's handled by the backend. For example, instead of:

```graphql
query {
  posts(criteria: { zone: "zone-123" }) {
    id
    title
  }
}
```

You can simply write:

```graphql
query {
  posts {
    id
    title
  }
}
```

The zone filter will be automatically added based on the current zone context.

#### Getting the Current User

To get information about the currently authenticated user, use the `currentUser` query:

```graphql
query GetCurrentUser {
  currentUser {
    id
    emails {
      address
      verified
    }
    zone
    userProfile {
      id
      userId
      firstName
      lastName
      avatar
    }
    roles
    blockedByMe
    friends {
      id
      emails {
        address
      }
      userProfile {
        id
        userId
        firstName
        lastName
        avatar
      }
    }
  }
}
```

#### Getting a Profile by User ID

To get a user's profile using their user ID, use the `profileByUserId` query:

```graphql
query GetProfileByUserId {
  profileByUserId(userId: "user-id") {
    id
    userId
    firstName
    lastName
    bio
    avatar
    visible
  }
}
```

This is useful when you have a user ID and need to find the associated profile. Note that profiles now have their own unique `id` that is different from the `userId` field that references the user.

This query is accessible to all clients and will return `null` if no user is authenticated. It provides a convenient way to check authentication status and get user information in a single request.

The `currentUser` query:
- Does not require authentication (but returns null for unauthenticated users)
- Does not accept any parameters (no need to pass email or id)
- Extracts the user from the JWT token in the Authorization header
- Falls back to email lookup if the user ID is missing from the token
- Returns the full user object with all relationships

The `blockedByMe` field indicates whether the current user has blocked the user being queried. This field will always return `false` if there is no authenticated user. This is useful for UI elements that need to show block status.

#### User Blocking Functionality

The system supports blocking other users through the Block entity:

1. **Block Structure**:
   ```graphql
   type Block {
     id: String!
     userId: String!        # The user who created the block
     blockedUserId: String! # The user who is blocked
     createdAt: DateTime
     updatedAt: DateTime
   }
   ```

2. **Creating Blocks**:
   ```graphql
   mutation CreateBlock {
     createBlock(
       input: {
         blockedUserId: "user-to-block-id"
       }
     ) {
       id
       blockedUserId
       createdAt
     }
   }
   ```

3. **Removing Blocks**:
   ```graphql
   mutation DeleteBlock {
     deleteBlock(id: "block-id")
   }
   ```

   Or by user ID:
   ```graphql
   mutation DeleteBlockByUserId {
     deleteBlockByUserId(blockedUserId: "user-to-unblock-id")
   }
   ```

4. **Querying Blocks**:
   ```graphql
   query GetMyBlocks {
     blocks {
       id
       blockedUserId
       createdAt
     }
   }
   ```

5. **Effects of Blocking**:
   - Blocked users will not appear in the blocker's user queries
   - Blocked users cannot see the blocker's profile
   - Blocked users cannot send friend requests to the blocker
   - Existing friend relationships are not automatically removed when blocking

#### Special Handling for Users Query

The `users` query has special role-based filtering:

1. **No Authentication**:
   - Returns an empty result set (for security)

2. **Regular Users**:
   - Returns only users from the current zone
   - Filters out users with `profile.visible = false` unless they are friends
   - Filters out users who have blocked the current user
   - This ensures users can only see other users who have chosen to be visible or are their friends

3. **Admin Users**:
   - Returns all users from the current zone
   - Can see users regardless of their visibility setting
   - Still filters out users who have blocked the current user

Example query with the `blockedByMe` field:

```graphql
query GetUsers {
  users(criteria: { zone: "my-zone" }) {
    id
    emails {
      address
      verified
    }
    userProfile {
      firstName
      lastName
      avatar
    }
    blockedByMe
  }
}
```

The `blockedByMe` field indicates whether the current user has blocked the user in the result set. This field will always return `false` if there is no authenticated user. This is useful for UI elements that need to show block status.

This role-based filtering is also applied to the single user query (`user(id: String!)`), which will return `null` if:
- No user is authenticated
- The requested user is in a different zone
- The requested user has `profile.visible = false` and the current user is not an admin and not a friend

#### Specifying Current User in Criteria

For clients that don't have authentication tokens (like public-facing websites), you can specify the current user in the query criteria using one of these fields (in order of priority):

1. **user**: Highest priority
   ```graphql
   query {
     users(
       criteria: {
         user: "<EMAIL>"  # Email address of the current user
       }
     ) {
       id
       username
     }
   }
   ```

2. **email**: Second priority
   ```graphql
   query {
     users(
       criteria: {
         email: "<EMAIL>"  # Email address of the current user
       }
     ) {
       id
       username
     }
   }
   ```

3. **currentUser**: Lowest priority
   ```graphql
   query {
     users(
       criteria: {
         currentUser: "<EMAIL>"  # Email address of the current user
       }
     ) {
       id
       username
     }
   }
   ```

The system will:
1. Check for `user`, then `email`, then `currentUser` in the criteria
2. Find the user with the specified email address
3. Use that user's permissions and zone for filtering
4. Remove the user identification fields from the criteria to avoid filtering by them

#### Setting the Current Zone

The current zone is determined in the following order of precedence:

1. The `x-zone-id` request header
2. The `zone` query parameter
3. The authenticated user's home zone (if no zone is specified)
4. A default zone from configuration (if no other zone is available)

Example with header:
```
GET /graphql
x-zone-id: zone-123
```

Example with query parameter:
```
GET /graphql?zone=zone-123
```

#### How Zone Filtering Works

Behind the scenes, the zone filtering system:

1. Extracts the current zone from the request context
2. Analyzes each GraphQL query operation
3. Modifies query arguments to include zone filtering
4. Handles both variable-based and inline criteria objects
5. Processes nested selections to ensure all relevant queries are filtered

This approach ensures consistent zone-based data segregation across the entire API.

#### Client-Side Considerations

When building client applications that interact with the API:

1. **Zone Selection**: Implement a zone selector in your UI if users need to switch between zones
2. **Zone Header**: Set the `x-zone-id` header in your API client for all requests
3. **Caching**: Consider zone-specific caching strategies in your client-side cache
4. **Error Handling**: Be prepared to handle empty results when no zone is available

#### Zone Filtering Best Practices

1. **Don't Include Zone in Criteria**: Avoid manually adding zone filters to your queries, as this can conflict with the automatic filtering
2. **Testing**: When testing, explicitly set the zone header to ensure consistent results
3. **Debugging**: If you're not seeing expected data, check which zone is being used in the request
4. **Performance**: The automatic zone filtering adds minimal overhead to queries

#### Creating Entities

Always include the zone ID when creating entities:

```graphql
mutation CreatePost {
  createPost(
    input: {
      title: "New Post",
      desc: "Post content",
      zone: "zone-id"
    }
  ) {
    id
  }
}
```

### Pagination

For collection queries, use `limit` and `skip` parameters:

```graphql
query GetPosts {
  posts(
    limit: 10,
    skip: 20, # Skip first 20 results
    sort: { createdAt: -1 }
  ) {
    id
    title
  }
}
```

Note: You don't need to include `zone` in the criteria as it's automatically added by the backend.

## Error Handling

GraphQL errors are returned in the `errors` array of the response:

```json
{
  "errors": [
    {
      "message": "Error message",
      "locations": [...],
      "path": [...]
    }
  ]
}
```

Common error scenarios:

1. **Authentication Errors**:
   - "Authentication required for this operation"
   - "Invalid credentials"

2. **Permission Errors**:
   - "You don't have permission to perform this action"

3. **Validation Errors**:
   - Field-specific validation errors

4. **Not Found Errors**:
   - "Entity not found"

## Best Practices

1. **Request Only What You Need**:
   GraphQL allows you to specify exactly which fields you need. This reduces payload size and improves performance.

2. **Use Fragments for Reusable Field Sets**:
   ```graphql
   fragment PlaceFields on Place {
     id
     name
     desc
     loc {
       lat
       lng
     }
   }

   query GetPlace {
     place(id: "place-id") {
       ...PlaceFields
     }
   }
   ```

3. **Handle Authentication Properly**:
   - Store JWT tokens securely
   - Refresh tokens when needed
   - Handle expired tokens gracefully

4. **Implement Optimistic UI Updates**:
   Update the UI immediately before the server responds, then reconcile with the actual response.

5. **Use Batching for Multiple Queries**:
   Combine multiple queries into a single request when possible.

## Client Implementation Examples

### React with Apollo Client

```javascript
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// Create HTTP link
const httpLink = createHttpLink({
  uri: 'http://your-api-url/graphql',
});

// Add auth header
const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('token');
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  };
});

// Create client
const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache()
});
```

### Query Example

```javascript
import { gql, useQuery } from '@apollo/client';

const GET_PLACES = gql`
  query GetPlaces($limit: Int, $skip: Int) {
    places(
      limit: $limit,
      skip: $skip,
      sort: { createdAt: -1 }
    ) {
      id
      name
      desc
      loc {
        lat
        lng
      }
    }
  }
`;

function PlacesList() {
  const { loading, error, data } = useQuery(GET_PLACES, {
    variables: { limit: 10, skip: 0 },
  });

  if (loading) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;

  return (
    <div>
      {data.places.map(place => (
        <div key={place.id}>
          <h3>{place.name}</h3>
          <p>{place.desc}</p>
        </div>
      ))}
    </div>
  );
}
```

### Mutation Example

```javascript
import { gql, useMutation } from '@apollo/client';

const CREATE_PLACE = gql`
  mutation CreatePlace($input: CreatePlaceInput!) {
    createPlace(input: $input) {
      id
      name
    }
  }
`;

function CreatePlaceForm() {
  const [createPlace, { loading, error }] = useMutation(CREATE_PLACE);

  const handleSubmit = (e) => {
    e.preventDefault();
    createPlace({
      variables: {
        input: {
          name: "New Place",
          desc: "Description",
          zone: "zone-id",
          loc: { lat: 37.7749, lng: -122.4194 }
        }
      }
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button type="submit" disabled={loading}>Create Place</button>
      {error && <p>Error: {error.message}</p>}
    </form>
  );
}
```

## Custom Scalars

The API uses several custom scalar types:

### DateTime

Used for date and time values. Represented as ISO 8601 strings in queries and responses.

```graphql
query GetHappenings {
  happenings {
    id
    title
    when # DateTime scalar
  }
}
```

When sending DateTime values in mutations, use ISO 8601 format:

```graphql
mutation CreateHappening {
  createHappening(
    input: {
      title: "Event",
      zone: "zone-id",
      when: "2023-12-31T23:59:59Z"
    }
  ) {
    id
  }
}
```

### JSON

Used for arbitrary JSON data. Can contain any valid JSON value.

```graphql
query GetPlace {
  place(id: "place-id") {
    id
    properties # JSON scalar
  }
}
```

When sending JSON values in mutations, use a JSON object:

```graphql
mutation UpdatePlace {
  updatePlace(
    id: "place-id",
    input: {
      properties: {
        customField: "value",
        nestedObject: {
          key: "value"
        }
      }
    }
  ) {
    id
  }
}
```

## Filtering with the Criteria Parameter

Most collection queries support a `criteria` parameter that accepts MongoDB-style query operators:

```graphql
query FilteredPlaces {
  places(
    criteria: {
      active: true,
      "loc.lat": { $gt: 37.0, $lt: 38.0 },
      "loc.lng": { $gt: -123.0, $lt: -122.0 },
      categories: { $in: ["restaurant", "cafe"] },
      createdAt: { $gt: "2023-01-01T00:00:00Z" }
    },
    limit: 20
  ) {
    id
    name
  }
}
```

Note: You don't need to include `zone` in the criteria as it's automatically added by the backend.

Common operators:
- `$eq`: Equal to
- `$gt`, `$gte`: Greater than, greater than or equal
- `$lt`, `$lte`: Less than, less than or equal
- `$in`: In an array
- `$nin`: Not in an array
- `$exists`: Field exists
- `$regex`: Regular expression match

## Conclusion

This documentation covers the core functionality of the Town application's GraphQL API. By following these patterns and examples, you can build robust client applications that interact with the server efficiently.

### Key Points to Remember

1. **Authentication**:
   - Use the appropriate authentication mechanism for your requests
   - Remember that some mutations require authentication
   - JWT tokens contain both `_id` and `userId` fields (with the same value) for backward compatibility
   - For WebSocket connections, include authentication in the connection parameters

2. **User Identification**:
   - User records in MongoDB have an `_id` field (not a `userId` field)
   - Other collections use `userId` to reference users
   - In GraphQL responses, the MongoDB `_id` field is mapped to `id`

3. **User-Profile Relationship**:
   - Profiles are created on-demand when first accessed, not automatically when a user is created
   - Profiles have their own unique `_id` field that is different from the user's ID
   - Profiles reference the user through a `userId` field that matches the user's `_id`
   - Use the `profileByUserId` query to get a profile by user ID

4. **Place Membership and Ownership**:
   - Places have a single owner but can have multiple members
   - Members are stored in the `placeMembership` array in the User document
   - The `placeMembershipDetails` field resolves to the actual Place objects
   - Place membership allows users to manage certain aspects of a place without being the owner

5. **User Roles and Permissions**:
   - Roles are stored both in the user document and in a separate role-assignment collection
   - Admin users can see all users in their zone, including those with `visible: false`
   - Regular users can only see users with `visible: true` or their friends
   - Place owners can manage their own places and related content

6. **User Blocking**:
   - The system supports blocking other users through the Block entity
   - Blocked users will not appear in the blocker's user queries
   - The `blockedByMe` field indicates whether the current user has blocked a user
   - Existing friend relationships are not automatically removed when blocking

7. **Query Optimization**:
   - Structure your queries to request only the data you need
   - Use fragments for reusable field sets
   - Take advantage of the relationship resolvers to fetch related data

8. **Zone-Based Data Model**:
   - All content in the system is segregated by zones
   - Zone filtering is automatically applied to queries
   - Set the zone context via headers or query parameters
   - Always include zone IDs when creating entities

9. **Filtering and Pagination**:
   - Use the criteria parameter for efficient filtering
   - Leverage MongoDB query operators for complex filters
   - Implement pagination with limit and skip parameters

10. **User Relationships**:
    - Friend relationships are stored with reciprocal entries
    - Friend requests use the generic request system
    - User visibility settings affect who can see a user in queries

11. **Real-time Updates with Subscriptions**:
    - Use WebSocket connections for real-time data updates
    - Available subscriptions include zone updates and new content notifications
    - Include authentication and zone information in WebSocket connection parameters
    - Implement proper connection management and error handling for WebSockets

### Getting Help

For additional help or specific use cases not covered here:
- Refer to the GraphQL playground available in development mode at `/graphql`
- Examine the network requests in your browser's developer tools
- Check the server logs for zone filtering debug information
- Contact the development team for advanced use cases
