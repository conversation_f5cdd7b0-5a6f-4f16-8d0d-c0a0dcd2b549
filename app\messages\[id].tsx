import { useMutation, useQuery } from '@apollo/client';
import { useLocalSearchParams } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, KeyboardAvoidingView, Platform, StyleSheet } from 'react-native';
import RealtimeMessages from '../../components/messages/RealtimeMessages';
import TypingIndicator from '../../components/messages/TypingIndicator';
import { Avatar } from '../../components/ui/Avatar';
import { Button } from '../../components/ui/Button';
import { TextInput } from '../../components/ui/TextInput';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import useTypingStatus from '../../hooks/useTypingStatus';
import useUnreadMessages from '../../hooks/useUnreadMessages';
import { GET_CONVERSATION_BY_ID, SEND_CONVERSATION_MESSAGE } from '../../lib/graphql-operations';
import { Message } from '../../types';

export default function ConversationScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const theme = useTheme();
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [optimisticMessageHandler, setOptimisticMessageHandler] = useState<((message: Message) => void) | null>(null);

  // Initialize typing status
  const {
    isOtherUserTyping,
    handleTextChange,
    setOtherUserInfo
  } = useTypingStatus(id as string, user?.id || '');

  // Use unread messages hook to mark messages as read
  const { markConversationAsRead } = useUnreadMessages();

  // Query conversation
  const { data, loading, error, refetch } = useQuery(GET_CONVERSATION_BY_ID, {
    variables: { id },
    // No need for polling as we're using subscriptions
  });

  // Set other user info for typing indicator and mark messages as read
  useEffect(() => {
    if (data?.conversation?.participant) {
      // Set typing indicator info
      setOtherUserInfo({
        userId: data.conversation.participant.id,
        username: data.conversation.participant.userProfile ?
          `${data.conversation.participant.userProfile.firstName || ''} ${data.conversation.participant.userProfile.lastName || ''}`.trim() :
          data.conversation.participant.emails?.[0]?.address?.split('@')[0] || '',
      });

      // Mark conversation as read when it's loaded
      markConversationAsRead(id as string);
    }
  }, [data?.conversation?.participant, setOtherUserInfo, markConversationAsRead, id]);

  // Send message mutation
  const [sendMessage, { loading: sendLoading }] = useMutation(SEND_CONVERSATION_MESSAGE, {
    onCompleted: () => {
      setMessage('');
    },
    onError: (error) => {
      console.error('Error sending message:', error);
    },
    optimisticResponse: (variables) => {
      // Create an optimistic response for immediate UI update
      return {
        sendMessage: {
          __typename: 'Message',
          id: `temp-${Date.now()}`, // Temporary ID that will be replaced by the real one
          content: variables.input.content,
          createdAt: new Date().toISOString(),
          sender: {
            __typename: 'User',
            id: user?.id || '',
            emails: user?.emails || [],
            userProfile: {
              __typename: 'Profile',
              id: user?.userProfile?.id || '',
              firstName: user?.userProfile?.firstName || '',
              lastName: user?.userProfile?.lastName || '',
              avatar: user?.userProfile?.avatar || null,
            },
          },
          read: false,
        },
      };
    },
    update: (cache, { data: mutationData }) => {
      // Update the cache with the new message
      try {
        const newMessage = mutationData?.sendMessage;
        if (!newMessage) return;

        // Read the current messages from the cache
        const existingData: any = cache.readQuery({
          query: GET_CONVERSATION_BY_ID,
          variables: { id },
        });

        if (existingData && existingData.conversation) {
          // Update the cache with the new message
          cache.writeQuery({
            query: GET_CONVERSATION_BY_ID,
            variables: { id },
            data: {
              conversation: {
                ...existingData.conversation,
                messages: [...existingData.conversation.messages, newMessage],
              },
            },
          });
        }
      } catch (error) {
        console.error('Error updating cache:', error);
      }
    },
  });

  // Handle optimistic UI updates for messages
  const handleOptimisticMessage = useCallback((callback: (message: Message) => void) => {
    setOptimisticMessageHandler(() => callback);
  }, []);

  // Handle text input changes for typing indicator
  const handleMessageChange = (text: string) => {
    setMessage(text);
    handleTextChange(text);
  };

  // Handle send message
  const handleSendMessage = () => {
    if (!message.trim() || !user?.id || !id) return;

    // Create an optimistic message for immediate UI update
    const optimisticMessage: Message = {
      id: `temp-${Date.now()}`,
      content: message.trim(),
      createdAt: new Date().toISOString(),
      senderId: user?.id || '',
      recipientId: data?.conversation?.participant?.id || '',
      sender: user,
      recipient: data?.conversation?.participant || { id: '' },
      read: false,
    };

    // Add the optimistic message to the UI
    if (optimisticMessageHandler) {
      optimisticMessageHandler(optimisticMessage);
    }

    // Send the actual message
    sendMessage({
      variables: {
        input: {
          conversationId: id,
          content: message.trim(),
        },
      },
    });

    // Clear the input and typing status
    setMessage('');
    handleTextChange('');
  };

  // Format date
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading conversation...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading conversation: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={() => refetch()} style={styles.retryButton} />
      </ThemedView>
    );
  }

  const conversation = data?.conversation;

  if (!conversation) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>Conversation not found</ThemedText>
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <ThemedView style={styles.header}>
        <Avatar
          user={conversation.participant}
          online={conversation.participant.isOnline}
          size="small"
        />
        <ThemedView style={styles.headerInfo}>
          <ThemedText variant="subtitle">
            {conversation.participant.userProfile ?
              `${conversation.participant.userProfile.firstName || ''} ${conversation.participant.userProfile.lastName || ''}`.trim() :
              conversation.participant.emails?.[0]?.address?.split('@')[0] || ''
            }
          </ThemedText>
          <ThemedText variant="caption">
            {conversation.participant.isOnline
              ? 'Online'
              : conversation.participant.lastOnline
              ? `Last seen ${formatTime(conversation.participant.lastOnline)}`
              : conversation.participant.emails?.[0]?.address ? '@' + conversation.participant.emails[0].address.split('@')[0] : ''
            }
          </ThemedText>
        </ThemedView>
      </ThemedView>

      <ThemedView style={styles.messagesWrapper}>
        <RealtimeMessages
          conversationId={id}
          onOptimisticMessage={handleOptimisticMessage}
        />
      </ThemedView>

      <TypingIndicator
        isVisible={isOtherUserTyping}
        username={conversation.participant.userProfile ?
          `${conversation.participant.userProfile.firstName || ''} ${conversation.participant.userProfile.lastName || ''}`.trim() :
          conversation.participant.emails?.[0]?.address?.split('@')[0] || ''
        }
      />

      <ThemedView style={styles.inputContainer}>
        <TextInput
          value={message}
          onChangeText={handleMessageChange}
          placeholder="Type a message..."
          style={styles.input}
          containerStyle={styles.inputWrapper}
          returnKeyType="send"
          onSubmitEditing={handleSendMessage}
        />
        <Button
          title="Send"
          onPress={handleSendMessage}
          disabled={!message.trim() || sendLoading}
          loading={sendLoading}
          size="small"
          style={styles.sendButton}
        />
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  headerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  messagesWrapper: {
    flex: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#e1e1e1',
  },
  inputWrapper: {
    flex: 1,
    marginBottom: 0,
  },
  input: {
    height: 40,
  },
  sendButton: {
    marginLeft: 8,
    alignSelf: 'center',
  },
});
