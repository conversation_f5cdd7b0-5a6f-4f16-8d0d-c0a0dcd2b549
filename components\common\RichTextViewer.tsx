import { router } from 'expo-router';
import React, { useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemedText } from '../ui/ThemedText';

interface RichTextViewerProps {
  content: string;
  containerStyle?: any;
  textStyle?: any;
  onLinkPress?: (url: string) => void;
  fallbackComponent?: React.ReactNode;
}

export function RichTextViewer({
  content,
  containerStyle,
  textStyle,
  onLinkPress,
  fallbackComponent,
}: RichTextViewerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const theme = useTheme();

  // Handle empty content
  if (!content) {
    return fallbackComponent || null;
  }

  // Create HTML content with proper styling
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
        <style>
          body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
            font-size: 16px;
            line-height: 1.5;
            color: ${theme.colors.text};
            background-color: ${theme.colors.background};
            margin: 0;
            padding: 0;
          }
          a {
            color: ${theme.colors.primary};
            text-decoration: none;
          }
          img {
            max-width: 100%;
            height: auto;
          }
          p {
            margin: 0 0 16px 0;
          }
          h1, h2, h3, h4, h5, h6 {
            margin: 16px 0 8px 0;
            font-weight: bold;
          }
          ul, ol {
            margin: 0 0 16px 0;
            padding-left: 24px;
          }
          blockquote {
            margin: 16px 0;
            padding: 8px 16px;
            border-left: 4px solid ${theme.colors.border};
            background-color: ${theme.colors.card};
          }
          code {
            font-family: monospace;
            background-color: ${theme.colors.card};
            padding: 2px 4px;
            border-radius: 4px;
          }
          pre {
            background-color: ${theme.colors.card};
            padding: 16px;
            border-radius: 4px;
            overflow-x: auto;
          }
          pre code {
            background-color: transparent;
            padding: 0;
          }
          table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
          }
          th, td {
            border: 1px solid ${theme.colors.border};
            padding: 8px;
            text-align: left;
          }
          th {
            background-color: ${theme.colors.card};
          }
        </style>
      </head>
      <body>
        ${content}
      </body>
    </html>
  `;

  // Handle link press
  const handleNavigationStateChange = (event: any) => {
    if (event.url !== 'about:blank') {
      // Prevent default navigation
      event.preventDefault();

      // Extract path from URL
      const url = event.url;
      
      if (onLinkPress) {
        onLinkPress(url);
        return;
      }

      // Handle internal links
      if (url.startsWith('/')) {
        router.push(url as any);
      } else if (url.includes('townapp://')) {
        // Handle deep links
        const deepLink = url.replace('townapp://', '/');
        router.push(deepLink as any);
      } else {
        // Handle external links
        Linking.openURL(url);
      }
    }
  };

  // Handle WebView load
  const handleLoad = () => {
    setLoading(false);
  };

  // Handle WebView error
  const handleError = () => {
    setLoading(false);
    setError('Failed to load content');
  };

  // Inject JavaScript to handle link clicks
  const injectedJavaScript = `
    (function() {
      function handleLinkClick(event) {
        if (event.target.tagName === 'A') {
          event.preventDefault();
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'linkPress',
            url: event.target.href
          }));
        }
      }
      
      document.addEventListener('click', handleLinkClick);
      
      // Calculate content height
      function updateHeight() {
        const height = document.body.scrollHeight;
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'contentHeight',
          height: height
        }));
      }
      
      // Update height when content changes
      const observer = new MutationObserver(updateHeight);
      observer.observe(document.body, { 
        childList: true, 
        subtree: true, 
        attributes: true, 
        characterData: true 
      });
      
      // Initial height calculation
      updateHeight();
      
      true;
    })();
  `;

  // Handle messages from WebView
  const [webViewHeight, setWebViewHeight] = useState(100);
  
  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      
      if (data.type === 'linkPress') {
        if (onLinkPress) {
          onLinkPress(data.url);
        } else if (data.url.startsWith('/')) {
          router.push(data.url as any);
        } else if (data.url.includes('townapp://')) {
          const deepLink = data.url.replace('townapp://', '/');
          router.push(deepLink as any);
        } else {
          Linking.openURL(data.url);
        }
      } else if (data.type === 'contentHeight') {
        setWebViewHeight(data.height);
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
        </View>
      )}
      
      {error ? (
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      ) : (
        <WebView
          originWhitelist={['*']}
          source={{ html: htmlContent }}
          style={[styles.webView, { height: webViewHeight }]}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onLoad={handleLoad}
          onError={handleError}
          injectedJavaScript={injectedJavaScript}
          onMessage={handleMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
        pointerEvents: 'auto'

  },
  webView: {
    backgroundColor: 'transparent',
    width: '100%',
  },
  loadingContainer: {
    padding: 10,
    alignItems: 'center',
  },
  errorText: {
    padding: 10,
    color: 'red',
  },
});
