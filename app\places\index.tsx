import { useApolloClient } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TextInput,
  useWindowDimensions
} from 'react-native';

import { PlaceCard } from '../../components/places/PlaceCard';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useZone } from '../../contexts/ZoneContext';
import { GET_CATEGORIES, GET_PLACES } from '../../lib/graphql-operations';
import { createCategories, createPlaces } from '../../models';
import { createUser } from '../../models/User';
import { Place } from '../../types';


export default function PlacesScreen() {
  // Context and client
  const { currentZone, loading: zoneLoading } = useZone();
  const { user } = useAuth();
  const client = useApolloClient();
  const zoneId = currentZone?.id || '';
  const router = useRouter();
  const { cat: routeCategory } = useLocalSearchParams();

  // Component state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [places, setPlaces] = useState<Place[]>([]);
  const [categories, setCategories] = useState<{ id: string | null; title: string }[]>([{ id: null, title: 'All' }]);
  const [isLoadingPlaces, setIsLoadingPlaces] = useState(false);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const { width } = useWindowDimensions();

  // Refs to track state without triggering effects
  const currentPage = useRef(0);
  const hasMorePages = useRef(true);
  const isMounted = useRef(true);
  const PAGE_SIZE = 20;

  // Permission check
  const userCanAddPlace = user && (createUser(user).isPlaceOwner() || createUser(user).isAdmin());

  // Number of columns for responsive grid
  const numColumns = width >= 1024 ? 3 : width >= 768 ? 2 : 1;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // MANUAL FETCH FUNCTIONS - Complete control over when network requests happen

  // Fetch places function - only called explicitly when needed
  const fetchPlaces = useCallback(async (reset = false) => {
    if (!zoneId || isLoadingPlaces) return;

    try {
      setIsLoadingPlaces(true);

      // Reset pagination if needed
      if (reset) {
        currentPage.current = 0;
        hasMorePages.current = true;
      }

      // Build query variables
      const variables = {
        criteria: {
          zone: zoneId,
          ...(selectedCategory ? { categories: selectedCategory } : {})
        },
        limit: PAGE_SIZE,
        skip: reset ? 0 : currentPage.current * PAGE_SIZE
      };

      // Execute query manually
      const result = await client.query({
        query: GET_PLACES,
        variables,
        fetchPolicy: reset ? 'network-only' : 'cache-first'
      });

      // Process results
      if (isMounted.current) {
        const newPlaces = result?.data?.places || [];
        const createdPlaces = createPlaces(newPlaces);
        if (reset) {
          // Replace all places
          setPlaces(createdPlaces);
        } else {
          // Append to existing places
          setPlaces(prevPlaces => [
            ...prevPlaces,
            ...createdPlaces.filter(
              // Filter out duplicates
              newPlace => !prevPlaces.some(p => p.id === newPlace.id)
            )
          ]);
        }

        // Update pagination state
        hasMorePages.current = newPlaces.length === PAGE_SIZE;
        if (newPlaces.length > 0) {
          currentPage.current = reset ? 1 : currentPage.current + 1;
        }
      }
    } catch (error) {
      console.error('Error fetching places:', error);
    } finally {
      if (isMounted.current) {
        setIsLoadingPlaces(false);
        setIsLoadingMore(false);
        setRefreshing(false);
      }
    }
    // fetchPlaces dependencies are handled by useCallback
  }, [client, zoneId, selectedCategory]);

  // Fetch categories function - only called explicitly when needed
  const fetchCategories = useCallback(async () => {
    if (!zoneId || isLoadingCategories) return;

    try {
      setIsLoadingCategories(true);

      // Execute query manually
      const result = await client.query({
        query: GET_CATEGORIES,
        variables: {
          criteria: {
            zone: zoneId,
            ctype: { $in: ['place', 'all'] }
          }
        },
        fetchPolicy: 'cache-first'
      });

      // Process results
      if (isMounted.current && result?.data?.categories) {
        const fetchedCategories = createCategories(result.data.categories);
        setCategories([
          { id: null, title: 'All' },
          ...fetchedCategories
        ]);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      if (isMounted.current) {
        setIsLoadingCategories(false);
      }
    }
    // fetchCategories dependencies are handled by useCallback
  }, [client, zoneId]);

  // Initial data load - only runs when component mounts and zone is available
  useEffect(() => {
    if (zoneId && isMounted.current) {
      fetchPlaces(true);
      fetchCategories();
    }
    // Removed fetchCategories from dependencies as it's a useCallback
  }, [zoneId, fetchPlaces]);

  useEffect(() => {
    if (routeCategory) {
      const category = Array.isArray(routeCategory) ? routeCategory[0] : routeCategory;
      setSelectedCategory(category);
      fetchPlaces(true);
    }
  }, [routeCategory, fetchPlaces]);

  // EVENT HANDLERS

  // Handle category selection
  const handleCategorySelect = useCallback(
    (categoryId: string | null) => {
      setSelectedCategory(categoryId);
      setIsLoadingPlaces(true);
      fetchPlaces(!!categoryId); // Convert `categoryId` to a boolean
    },
    [fetchPlaces, setSelectedCategory]
  );

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchPlaces(true);
  }, [fetchPlaces]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (isLoadingPlaces || isLoadingMore || refreshing || !hasMorePages.current) return;

    setIsLoadingMore(true);
    fetchPlaces(false);
  }, [isLoadingPlaces, isLoadingMore, refreshing, hasMorePages, fetchPlaces]);


  // Filter places by search term (client-side filtering)
  const filteredPlaces = places.filter(place => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = place.name.toLowerCase().includes(searchLower) ||
      (place.desc && place.desc.toLowerCase().includes(searchLower));
    const matchesCategory = !selectedCategory || place.categories?.includes(selectedCategory);
    return matchesSearch && matchesCategory;
  });


  // Loading state for initial zone load
  if (zoneLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={[styles.container]}>
      <ThemedView style={styles.header}>
        {/* Search input */}
        <Card style={styles.searchContainer}>
          <Ionicons name="search" size={20} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search places..."
            value={searchTerm}
            onChangeText={setSearchTerm}
          />
        </Card>

        {/* Category filters */}
        {categories.length > 1 && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoryScrollContainer}
            contentContainerStyle={styles.categoryContainer}
          >
            {isLoadingCategories ? (
              <ActivityIndicator size="small" />
            ) : (
              categories.map((category) => (
                <Button
                  key={category.id || 'all'}
                  title={category.title || 'All'}
                  onPress={() => handleCategorySelect(category.id)}
                  variant={selectedCategory === category.id ? 'primary' : 'secondary'}
                  size="small"
                  style={styles.categoryButton}
                />
              ))
            )}
          </ScrollView>
        )}

        {/* Add new place button */}
        {userCanAddPlace && (
          <Button
            title="Add New Place"
            onPress={() => router.push('/place/new')}
            style={styles.addButton}
          />
        )}
      </ThemedView>

      {/* Places grid */}
      {/* Conditionally render FlatList based on loading and data presence */}
      {isLoadingPlaces && filteredPlaces.length === 0 ? (
         <ThemedView style={styles.emptyContainer}>
          <ActivityIndicator size="large" />
          <ThemedText style={styles.loadingText}>Loading places...</ThemedText>
        </ThemedView>
      ) : filteredPlaces.length > 0 ? (
<FlatList
  data={filteredPlaces}
  renderItem={({ item }) => <PlaceCard place={item} numColumns={numColumns} />}
  keyExtractor={(item) => item.id}
  numColumns={numColumns}
  key={`places-list-${numColumns}`} 
  contentContainerStyle={[styles.placesGrid, { flexGrow: 1 }]}
  columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
  refreshControl={
    <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
  }
  onEndReached={handleLoadMore}
  onEndReachedThreshold={0.5}
  ListFooterComponent={isLoadingMore ? <ActivityIndicator size="large" /> : null}
  keyboardShouldPersistTaps="handled"
  scrollEnabled={true}
/>
      ) : (
        // Show "No places found" only when not loading and no places are found
        <ThemedView style={styles.emptyContainer}>
          <ThemedText>No places found</ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
  },
  categoryScrollContainer: {
    marginBottom: 16,
  },
  categoryContainer: {
    flexDirection: 'row',
    paddingRight: 16,
  },
  categoryButton: {
    marginRight: 8,
    minWidth: 80,
    paddingHorizontal: 12,
  },
  addButton: {
    marginBottom: 16,
  },
  placesGrid: {
    paddingBottom: 16,
    pointerEvents: 'auto',
  },
  row: {
    flex: 1,
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
});