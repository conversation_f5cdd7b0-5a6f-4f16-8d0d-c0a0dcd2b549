import { View, ViewProps } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface ThemedViewProps extends ViewProps {
  variant?: 'primary' | 'secondary' | 'card';
}

export function ThemedView({ style, variant = 'primary', ...props }: ThemedViewProps) {
  const theme = useTheme();
  
  const variantStyles = {
    primary: {
      backgroundColor: 'transparent',//theme.colors.background,
    },
    secondary: {
      backgroundColor: theme.colors.backgroundSecondary,
    },
    card: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.medium,
      padding: theme.spacing.medium,
      ...theme.shadows.medium,
    },
  };
  
  return (
    <View 
      style={[variantStyles[variant], style]} 
      {...props} 
    />
  );
}
