import React, { createContext, useContext, useState, ReactNode } from 'react';

interface MapContextType {
  showMap: boolean;
  setShowMap: (show: boolean) => void;
  selectedCategory: string | null;
  setSelectedCategory: (category: string | null) => void;
}

const MapContext = createContext<MapContextType | undefined>(undefined);

export function MapProvider({ children }: { children: ReactNode }) {
  const [showMap, setShowMap] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  return (
    <MapContext.Provider
      value={{
        showMap,
        setShowMap,
        selectedCategory,
        setSelectedCategory,
      }}
    >
      {children}
    </MapContext.Provider>
  );
}

export function useMap() {
  const context = useContext(MapContext);
  if (context === undefined) {
    throw new Error('useMap must be used within a MapProvider');
  }
  return context;
}
