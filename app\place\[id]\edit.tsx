import { useMutation, useQuery } from '@apollo/client';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useState } from 'react';
import { ScrollView, StyleSheet, TextInput } from 'react-native';
import { Button } from '../../../components/ui/Button';
import { ThemedText } from '../../../components/ui/ThemedText';
import { ThemedView } from '../../../components/ui/ThemedView';
import { GET_PLACE, UPDATE_PLACE } from '../../../lib/graphql-operations';

export default function EditPlaceScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { data, loading, error } = useQuery(GET_PLACE, { variables: { id } });
  const [updatePlace] = useMutation(UPDATE_PLACE);

  const [form, setForm] = useState({
    name: '',
    desc: '',
    avatar: '',
    fullAddress: '',
    phone: '',
    url: '',
    offerCards: '',
    cardBackground: '',
    cats: '',
    wakeWords: '',
    alexaDesc: '',
    loc: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    try {
      await updatePlace({ variables: { id, input: form } });
      router.push(`/place/${id}`);
    } catch (err) {
      console.error('Error updating place:', err);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>Loading...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText>Error loading place details.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <ThemedText>Name</ThemedText>
      <TextInput
        style={styles.input}
        value={form.name}
        onChangeText={(text) => handleInputChange('name', text)}
      />

      <ThemedText>Description</ThemedText>
      <TextInput
        style={styles.input}
        value={form.desc}
        onChangeText={(text) => handleInputChange('desc', text)}
      />

      <ThemedText>Avatar</ThemedText>
      <TextInput
        style={styles.input}
        value={form.avatar}
        onChangeText={(text) => handleInputChange('avatar', text)}
      />

      <ThemedText>Full Address</ThemedText>
      <TextInput
        style={styles.input}
        value={form.fullAddress}
        onChangeText={(text) => handleInputChange('fullAddress', text)}
      />

      <ThemedText>Phone</ThemedText>
      <TextInput
        style={styles.input}
        value={form.phone}
        onChangeText={(text) => handleInputChange('phone', text)}
      />

      <ThemedText>URL</ThemedText>
      <TextInput
        style={styles.input}
        value={form.url}
        onChangeText={(text) => handleInputChange('url', text)}
      />

      <ThemedText>Offer Cards</ThemedText>
      <TextInput
        style={styles.input}
        value={form.offerCards}
        onChangeText={(text) => handleInputChange('offerCards', text)}
      />

      <ThemedText>Card Background</ThemedText>
      <TextInput
        style={styles.input}
        value={form.cardBackground}
        onChangeText={(text) => handleInputChange('cardBackground', text)}
      />

      <ThemedText>Categories</ThemedText>
      <TextInput
        style={styles.input}
        value={form.cats}
        onChangeText={(text) => handleInputChange('cats', text)}
      />

      <ThemedText>Wake Words</ThemedText>
      <TextInput
        style={styles.input}
        value={form.wakeWords}
        onChangeText={(text) => handleInputChange('wakeWords', text)}
      />

      <ThemedText>Alexa Description</ThemedText>
      <TextInput
        style={styles.input}
        value={form.alexaDesc}
        onChangeText={(text) => handleInputChange('alexaDesc', text)}
      />

      <ThemedText>Location</ThemedText>
      <TextInput
        style={styles.input}
        value={form.loc}
        onChangeText={(text) => handleInputChange('loc', text)}
      />

      <Button title="Save" onPress={handleSubmit} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
