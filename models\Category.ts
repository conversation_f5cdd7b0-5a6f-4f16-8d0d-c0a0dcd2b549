import { Category as CategoryInterface } from '@/types/category';

/**
 * Category class that implements the CategoryInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Category implements CategoryInterface {
  id: string;
  title: string;
  ctype: string;
  zone: string;
  iconName?: string;
  count?: number;
  createdAt?: string;
  updatedAt?: string;

  constructor(categoryData: CategoryInterface) {
    Object.assign(this, categoryData);
  }

  /**
   * Get the icon name for this category
   * @returns The name of the icon to use for this category
   */
  getIconName(): string {
    return this.iconName || 'faTag';
  }

  /**
   * Get the URL for this category's list page
   * @returns The URL for the category's list page
   */
  linkToList(): string {
    switch (this.ctype) {
      case 'place':
        return `/places?category=${this.id}`;
      case 'offer':
        return `/offers?category=${this.id}`;
      case 'event':
        return `/happenings?category=${this.id}`;
      case 'page':
        return `/pages?category=${this.id}`;
      case 'user':
        return `/people?category=${this.id}`;
      case 'all':
      default:
        return `/places?category=${this.id}`;
    }
  }

  /**
   * Get the color for this category
   * @returns A CSS color string
   */
  getColor(): string {
    // This would typically be based on the category type or other properties
    // For now, we'll return colors based on the ctype
    switch (this.ctype) {
      case 'place':
        return '#3B82F6'; // Blue
      case 'offer':
        return '#10B981'; // Green
      case 'event':
        return '#F59E0B'; // Amber
      case 'page':
        return '#8B5CF6'; // Purple
      case 'user':
        return '#EC4899'; // Pink
      case 'all':
      default:
        return '#6B7280'; // Gray
    }
  }
}

/**
 * Factory function to create a Category instance from a plain object
 * @param data The category data
 * @returns A new Category instance
 */
export function createCategory(data: CategoryInterface): Category {
  return new Category(data);
}

/**
 * Factory function to create multiple Category instances from an array
 * @param dataArray Array of category data
 * @returns Array of Category instances
 */
export function createCategories(dataArray: CategoryInterface[]): Category[] {
  return dataArray.map(data => createCategory(data));
}
