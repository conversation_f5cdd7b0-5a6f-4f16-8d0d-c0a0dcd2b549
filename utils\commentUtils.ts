import { ApolloCache, MutationFunctionOptions } from '@apollo/client';

/**
 * Shared function to handle adding a comment.
 * @param addComment - The mutation function for adding a comment.
 * @param input - The input variables for the mutation.
 * @param cacheUpdate - A function to update the Apollo cache.
 */
export async function handleAddComment(
  addComment: (options?: MutationFunctionOptions) => Promise<any>,
  input: { linkedObjectId: string; objectType: string; body: string },
  cacheUpdate: (cache: ApolloCache<any>, data: any) => void
): Promise<void> {
  if (!input.body.trim()) return;

  try {
    await addComment({
      variables: { input },
      update: (cache, { data }) => {
        if (data?.createComment) {
          cacheUpdate(cache, data.createComment);
        }
      },
    });
  } catch (error) {
    console.error('Error adding comment:', error);
  }
}
