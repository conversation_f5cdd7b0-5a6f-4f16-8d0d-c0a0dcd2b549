import { gql, useMutation, useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, KeyboardAvoidingView, Platform, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { Avatar } from '../../components/ui/Avatar';
import { Button } from '../../components/ui/Button';
import { TextInput } from '../../components/ui/TextInput';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

// GraphQL query for current user
const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    me {
      id
      emails {
        address
        verified
      }
      userProfile {
        id
        firstName
        lastName
        bio
        avatar
      }
    }
  }
`;

// GraphQL mutation for updating profile
const UPDATE_PROFILE = gql`
  mutation UpdateProfile($input: UpdateProfileInput!) {
    updateProfile(input: $input) {
      id
      userProfile {
        id
        firstName
        lastName
        bio
        avatar
      }
    }
  }
`;

export default function EditProfileScreen() {
  const theme = useTheme();
  const { user: authUser } = useAuth();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [bio, setBio] = useState('');
  const [avatar, setAvatar] = useState('');

  // Query current user data
  const { data, loading: queryLoading, error: queryError } = useQuery(GET_CURRENT_USER);

  // Update profile mutation
  const [updateProfile, { loading: mutationLoading }] = useMutation(UPDATE_PROFILE, {
    onCompleted: () => {
      Alert.alert('Success', 'Profile updated successfully!');
      router.back();
    },
    onError: (error) => {
      Alert.alert('Error', `Failed to update profile: ${error.message}`);
    },
  });

  // Set initial values when data is loaded
  useEffect(() => {
    if (data?.me) {
      setFirstName(data.me.userProfile?.firstName || '');
      setLastName(data.me.userProfile?.lastName || '');
      setBio(data.me.userProfile?.bio || '');
      setAvatar(data.me.userProfile?.userProfile.avatar || '');
    } else if (authUser) {
      setFirstName(authUser.userProfile?.firstName || '');
      setLastName(authUser.userProfile?.lastName || '');
      setBio(authUser.userProfile?.bio || '');
      setAvatar(authUser.userProfile?.userProfile.avatar || '');
    }
  }, [data, authUser]);

  // Handle submit
  const handleSubmit = () => {
    if (!firstName.trim()) {
      Alert.alert('Error', 'First name is required');
      return;
    }

    updateProfile({
      variables: {
        input: {
          userProfile: {
            firstName: firstName.trim(),
            lastName: lastName.trim(),
            bio: bio.trim() || null,
            avatar: avatar.trim() || null,
          }
        },
      },
    });
  };

  // Handle avatar selection
  const handleSelectAvatar = () => {
    // In a real app, this would open an image picker
    Alert.alert(
      'Select Avatar',
      'This would open an image picker in a real app',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Use Sample Avatar',
          onPress: () => setAvatar('https://randomuser.me/api/portraits/lego/1.jpg'),
        },
      ]
    );
  };

  // Loading state
  if (queryLoading && !data) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading profile...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (queryError) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading profile: {queryError.message}
        </ThemedText>
        <Button title="Go Back" onPress={() => router.back()} style={styles.backButton} />
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
      >
        <ThemedText variant="title" style={styles.title}>Edit Profile</ThemedText>

        <ThemedView style={styles.userProfile.avatarContainer}>
          <TouchableOpacity onPress={handleSelectAvatar}>
            <Avatar
              source={avatar}
              size="large"
              name={`${firstName} ${lastName}`}
            />
            <ThemedView style={styles.editAvatarButton}>
              <Ionicons name="camera" size={20} color={theme.colors.white} />
            </ThemedView>
          </TouchableOpacity>
        </ThemedView>

        <TextInput
          label="First Name"
          value={firstName}
          onChangeText={setFirstName}
          placeholder="Enter your first name"
          style={styles.input}
        />

        <TextInput
          label="Last Name"
          value={lastName}
          onChangeText={setLastName}
          placeholder="Enter your last name"
          style={styles.input}
        />

        <TextInput
          label="Bio"
          value={bio}
          onChangeText={setBio}
          placeholder="Tell us about yourself"
          multiline
          numberOfLines={4}
          style={styles.bioInput}
        />

        <TextInput
          label="Avatar URL"
          value={avatar}
          onChangeText={setAvatar}
          placeholder="Enter URL for your avatar image"
          style={styles.input}
        />

        <ThemedView style={styles.buttonContainer}>
          <Button
            title="Cancel"
            onPress={() => router.back()}
            variant="outline"
            style={styles.cancelButton}
          />
          <Button
            title={mutationLoading ? 'Saving...' : 'Save Changes'}
            onPress={handleSubmit}
            disabled={mutationLoading || !firstName.trim()}
            loading={mutationLoading}
            style={styles.submitButton}
          />
        </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  backButton: {
    marginTop: 8,
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  avatarContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#0a7ea4',
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  input: {
    marginBottom: 16,
  },
  bioInput: {
    marginBottom: 16,
    height: 100,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  submitButton: {
    flex: 1,
    marginLeft: 8,
  },
});
