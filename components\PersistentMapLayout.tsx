import React, { ReactNode } from 'react';
import { StyleSheet, View } from 'react-native';
import { useMap } from '../contexts/MapContext';
import WebMapComponent from './map/WebMapComponent';

interface PersistentMapLayoutProps {
  children: ReactNode;
}

export default function PersistentMapLayout({ children }: PersistentMapLayoutProps) {
  const { selectedCategory } = useMap();
  
  return (
    <View style={styles.container}>
      <WebMapComponent 
        selectedCategory={selectedCategory} 
      />
      <View style={styles.content} >
        {children}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    backgroundColor: 'red',
  },
  map: {
    position: 'absolute',
    top: 0,
    left: 0,
    //width: Dimensions.get('window').width,
    //height: Dimensions.get('window').height,
    zIndex: 0,
    elevation: -1,
    // Remove pointerEvents from here - we'll set it directly on the component
  },
  content: {
    flex: 1,
    position: 'relative',
    zIndex: 1,
    pointerEvents:"box-none",
  },
});