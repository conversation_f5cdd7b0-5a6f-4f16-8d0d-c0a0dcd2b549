import React, { createContext, useCallback, useContext, useState } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';

// Define the navigation context type
type NavigationContextType = {
  navigate: (routeName: string) => void;
  currentRoute: string;
};

// Create the navigation context
const NavigationContext = createContext<NavigationContextType>({
  navigate: () => {},
  currentRoute: '/',
});

// Custom hook to use navigation
export const useNavigation = () => useContext(NavigationContext);

// Navigation button component
export const NavButton = ({ 
  to, 
  children,
  style,
  textStyle,
}: { 
  to: string; 
  children: React.ReactNode;
  style?: any;
  textStyle?: any;
}) => {
  const { navigate } = useNavigation();
  
  return (
    <Pressable 
      style={[styles.button, style]} 
      onPress={() => {
        console.log(`NavButton navigating to: ${to}`);
        navigate(to);
      }}
    >
      {typeof children === 'string' ? (
        <Text style={[styles.buttonText, textStyle]}>{children}</Text>
      ) : (
        children
      )}
    </Pressable>
  );
};

// Navigation wrapper component
export const NavigationProvider = ({ 
  children,
  initialRoute = '/',
  routes,
}: { 
  children: React.ReactNode;
  initialRoute?: string;
  routes: Record<string, React.ComponentType<any>>;
}) => {
  const [currentRoute, setCurrentRoute] = useState(initialRoute);
  
  const navigate = useCallback((routeName: string) => {
    console.log(`NavigationProvider navigating to: ${routeName}`);
    if (routes[routeName]) {
      setCurrentRoute(routeName);
    } else {
      console.error(`Route not found: ${routeName}`);
    }
  }, [routes]);
  
  const CurrentComponent = routes[currentRoute] || routes[initialRoute];
  
  return (
    <NavigationContext.Provider value={{ navigate, currentRoute }}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerText}>Navigation: {currentRoute}</Text>
        </View>
        <CurrentComponent />
      </View>
    </NavigationContext.Provider>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  header: {
    backgroundColor: '#2196F3',
    padding: 15,
    alignItems: 'center',
  },
  headerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
    width: '100%',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
