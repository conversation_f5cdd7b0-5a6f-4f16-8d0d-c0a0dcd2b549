import { useMutation } from '@apollo/client';
import { useDebugValue, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { UPDATE_USER_PRESENCE } from '../lib/graphql-operations';

const STATUS = {
  ONLINE: 'online',
  IDLE: 'idle',
  OFFLINE: 'offline',
};

export function useUserPresence() {
  const { user } = useAuth(); // Get the current user

  useDebugValue(user ? `User: ${user.id}` : 'No user'); // Debugging aid to show user state

  const [updateUserPresence] = useMutation(UPDATE_USER_PRESENCE);

  useEffect(() => {
    if (!user) {
      console.debug('useUserPresence: No user detected, skipping presence updates.');
      return; // Do nothing if the user is not logged in
    }

    console.debug('useUserPresence: User detected, initializing presence updates.');

    const handleOnline = () => {
      console.debug('useUserPresence: Setting status to ONLINE.');
      updateUserPresence({ variables: { status: STATUS.ONLINE } }).catch((error) => {
        console.error('useUserPresence: Failed to update status to ONLINE.', error);
      });
    };
    const handleIdle = () => {
      console.debug('useUserPresence: Setting status to IDLE.');
      updateUserPresence({ variables: { status: STATUS.IDLE } }).catch((error) => {
        console.error('useUserPresence: Failed to update status to IDLE.', error);
      });
    };
    const handleOffline = () => {
      console.debug('useUserPresence: Setting status to OFFLINE.');
      updateUserPresence({ variables: { status: STATUS.OFFLINE } }).catch((error) => {
        console.error('useUserPresence: Failed to update status to OFFLINE.', error);
      });
    };

    // Set initial status to online
    handleOnline();

    // Listen for visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        handleIdle();
      } else {
        handleOnline();
      }
    });

    // Listen for beforeunload to set offline status
    window.addEventListener('beforeunload', handleOffline);

    // Cleanup event listeners on unmount
    return () => {
      console.debug('useUserPresence: Cleaning up event listeners.');
      document.removeEventListener('visibilitychange', handleIdle);
      window.removeEventListener('beforeunload', handleOffline);
    };
  }, [updateUserPresence, user]);

  return null;
}
