const createExpoWebpackConfigAsync = require('@expo/webpack-config');
const path = require('path');
const webpack = require('webpack');
const fs = require('fs');

// Get the project root directory
const projectRoot = fs.realpathSync(process.cwd());

// Bundle analyzer for development (optional dependency)
let BundleAnalyzerPlugin = null;
try {
  if (process.env.ANALYZE_BUNDLE) {
    BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
  }
} catch (e) {
  // webpack-bundle-analyzer not installed, skip
}

module.exports = async function (env, argv) {
  // Create the default Expo web configuration
  const config = await createExpoWebpackConfigAsync(env, argv);

  // Fix for node:internal/process/task_queues error
  config.resolve.fallback = {
    ...config.resolve.fallback,
    'node:internal/process/task_queues': path.resolve(projectRoot, './node-internal-process-task_queues.js'),
    'process': require.resolve('process/browser'),
    'stream': require.resolve('stream-browserify'),
    'zlib': require.resolve('browserify-zlib'),
    'util': require.resolve('util'),
    'buffer': require.resolve('buffer'),
    'asset': require.resolve('assert'),
  };

  // Fix for missing-asset-registry-path error
  config.resolve.alias = {
    ...config.resolve.alias,
    'missing-asset-registry-path': path.resolve(projectRoot, './missing-asset-registry-path.js'),
  };

  // Fix for serializer format issues
  if (config.output) {
    // Ensure the output format is compatible with Metro
    config.output.globalObject = 'this';
  }

  // Add polyfills
  config.plugins = [
    ...config.plugins,
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer'],
    }),
  ];

  // Add a resolver plugin to handle ESM/CJS mismatches for @babel/runtime
  config.resolve.plugins = [
    ...(config.resolve.plugins || []),
    new webpack.NormalModuleReplacementPlugin(
      /^@babel\/runtime\/helpers\/esm\//,
      resource => {
        resource.request = resource.request.replace(/^@babel\/runtime\/helpers\/esm\//, '@babel/runtime/helpers/');
      }
    )
  ];

  // Add bundle analyzer if requested
  if (BundleAnalyzerPlugin && process.env.ANALYZE_BUNDLE) {
    config.plugins.push(
      new BundleAnalyzerPlugin({
        analyzerMode: 'server',
        openAnalyzer: true,
        generateStatsFile: true,
        statsFilename: 'bundle-stats.json',
      })
    );
  }

  // Performance optimizations
  if (env.production) {
    // Enable tree shaking
    config.optimization = {
      ...config.optimization,
      usedExports: true,
      sideEffects: false,
    };
  }

  return config;
};
