import { useQuery } from '@apollo/client';
import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { Message, User } from '../../types';

export default function MessagesScreen() {
  const { currentUser, isAuthenticated } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  // Query messages for the current user
  const { data, loading, error, refetch } = useQuery(GET_USER_MESSAGES, {
    variables: {
      userId: currentUser?.id || '',
    },
    skip: !currentUser?.id,
  });

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing messages:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // If less than 24 hours, show time
    if (diff < 24 * 60 * 60 * 1000) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // If less than 7 days, show day of week
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      return date.toLocaleDateString([], { weekday: 'short' });
    }

    // Otherwise show date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  // Get the other user in a conversation
  const getOtherUser = (message: Message, currentUserId: string): User => {
    return message.sender.id === currentUserId ? message.recipient : message.sender;
  };

  // Group messages by conversation
  const groupMessagesByConversation = (messages: Message[], currentUserId: string) => {
    const conversations: { [key: string]: Message[] } = {};

    messages.forEach(message => {
      const otherUser = getOtherUser(message, currentUserId);
      const conversationId = otherUser.id;

      if (!conversations[conversationId]) {
        conversations[conversationId] = [];
      }

      conversations[conversationId].push(message);
    });

    // Get the latest message from each conversation
    return Object.values(conversations).map(msgs => {
      // Sort by date descending
      msgs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      return msgs[0]; // Return the latest message
    });
  };

  // Render message item
  const renderMessageItem = ({ item }: { item: Message }) => {
    const otherUser = getOtherUser(item, currentUser?.id || '');
    const isFromMe = item.sender.id === currentUser?.id;

    return (
      <Card
        style={styles.messageCard}
        onPress={() => router.push(`/messages/${otherUser.id}`)}
      >
        <View style={styles.messageHeader}>
          <ThemedText variant="subtitle">{otherUser.userProfile ?
              `${otherUser.userProfile.firstName || ''} ${otherUser.userProfile.lastName || ''}`.trim() :
              otherUser?.email?.split('@')[0]
            }</ThemedText>
          <ThemedText variant="caption">{formatDate(item.createdAt)}</ThemedText>
        </View>
        <View style={styles.messageContent}>
          {isFromMe && <ThemedText style={styles.sentPrefix}>You: </ThemedText>}
          <ThemedText
            style={[styles.messageText, !item.read && !isFromMe && styles.unreadMessage]}
            numberOfLines={1}
          >
            {item.content}
          </ThemedText>
        </View>
      </Card>
    );
  };

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText variant="title" style={styles.title}>Messages</ThemedText>
        <ThemedText style={styles.subtitle}>
          Please sign in to view your messages
        </ThemedText>
        <Button
          title="Sign In"
          onPress={() => router.push('/login')}
          style={styles.signInButton}
        />
      </ThemedView>
    );
  }

  // Group messages by conversation
  const conversations = data?.messages
    ? groupMessagesByConversation(data.messages, currentUser?.id || '')
    : [];

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={conversations}
        renderItem={renderMessageItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        keyboardShouldPersistTaps="handled"
        scrollEnabled={true}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListHeaderComponent={
          <View>
            <ThemedText variant="title" style={styles.title}>Messages</ThemedText>
            <ThemedText style={styles.subtitle}>
              Your conversations
            </ThemedText>

            <Button
              title="New Message"
              onPress={() => router.push('/messages/new')}
              style={styles.newButton}
            />
          </View>
        }
        ListEmptyComponent={
          loading ? (
            <ThemedView style={styles.emptyContainer}>
              <ActivityIndicator size="large" />
              <ThemedText style={styles.emptyText}>Loading messages...</ThemedText>
            </ThemedView>
          ) : error ? (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.errorText}>
                Error loading messages: {error.message}
              </ThemedText>
              <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
            </ThemedView>
          ) : (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.emptyText}>
                No messages yet. Start a conversation!
              </ThemedText>
              <Button
                title="New Message"
                onPress={() => router.push('/messages/new')}
                style={styles.createButton}
              />
            </ThemedView>
          )
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  listContent: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 16,
  },
  newButton: {
    marginBottom: 16,
  },
  messageCard: {
    marginBottom: 12,
    padding: 16,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  messageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sentPrefix: {
    fontWeight: '500',
    marginRight: 4,
  },
  messageText: {
    flex: 1,
  },
  unreadMessage: {
    fontWeight: 'bold',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  createButton: {
    marginTop: 8,
  },
  signInButton: {
    marginTop: 16,
  },
});
