import { Zone as ZoneInterface, Center } from '@/types/zone';

/**
 * Zone class that implements the ZoneInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Zone implements ZoneInterface {
  id: string;
  title: string;
  center: Center;
  signUpText: string;
  temp?: string;
  skytext?: string;
  weatherUpdatedAt?: string;
  rootPage?: string;
  welcomePage?: string;
  createdAt: string;
  updatedAt?: string;
  appName: string;
  createdBy: string;
  giftcardHoldback: number;
  maxBusinesses: number;
  ownersAddPlaces: boolean;
  pagesMenuLabel: string;
  showSubscribeBtn: boolean;
  subscriptionCode: string;
  subscriptionsEnabled: boolean;
  useGiftCards: boolean;

  constructor(zoneData: ZoneInterface) {
    Object.assign(this, zoneData);
  }

  /**
   * Get the URL for this zone's home page
   * @returns The URL for the zone's home page
   */
  homeUrl(): string {
    return `/zone/${this.id}`;
  }

  /**
   * Get the URL for this zone's welcome page
   * @returns The URL for the zone's welcome page
   */
  welcomeUrl(): string {
    if (this.welcomePage) {
      return `/page/${this.welcomePage}`;
    }
    return this.homeUrl();
  }

  /**
   * Get the URL for this zone's root page
   * @returns The URL for the zone's root page
   */
  rootUrl(): string {
    if (this.rootPage) {
      return `/page/${this.rootPage}`;
    }
    return this.homeUrl();
  }

  /**
   * Get the formatted weather information
   * @returns A formatted string with the weather information
   */
  weatherInfo(): string {
    if (this.temp && this.skytext) {
      return `${this.temp}° - ${this.skytext}`;
    }
    if (this.temp) {
      return `${this.temp}°`;
    }
    if (this.skytext) {
      return this.skytext;
    }
    return '';
  }

  /**
   * Check if the weather information is current
   * @returns True if the weather information is current, false otherwise
   */
  hasCurrentWeather(): boolean {
    if (!this.weatherUpdatedAt) {
      return false;
    }
    
    const now = new Date();
    const updated = new Date(this.weatherUpdatedAt);
    
    // Weather is considered current if it was updated in the last 3 hours
    const threeHoursInMs = 3 * 60 * 60 * 1000;
    return (now.getTime() - updated.getTime()) < threeHoursInMs;
  }
}

/**
 * Factory function to create a Zone instance from a plain object
 * @param data The zone data
 * @returns A new Zone instance
 */
export function createZone(data: ZoneInterface): Zone {
  return new Zone(data);
}

/**
 * Factory function to create multiple Zone instances from an array
 * @param dataArray Array of zone data
 * @returns Array of Zone instances
 */
export function createZones(dataArray: ZoneInterface[]): Zone[] {
  return dataArray.map(data => createZone(data));
}
