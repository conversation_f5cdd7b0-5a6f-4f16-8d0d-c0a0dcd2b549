import React from 'react';

/**
 * Performance monitoring utilities for TownExpo
 *
 * This module provides tools for measuring and monitoring app performance
 * during development and testing phases.
 */

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = __DEV__;

  /**
   * Start measuring a performance metric
   */
  start(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata,
    };

    this.metrics.set(name, metric);
  }

  /**
   * End measuring a performance metric
   */
  end(name: string): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" was not started`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    // Log the metric in development
    if (__DEV__) {
      console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`, metric.metadata || '');
    }

    return duration;
  }

  /**
   * Measure a function execution time
   */
  async measure<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
    this.start(name, metadata);
    try {
      const result = await fn();
      this.end(name);
      return result;
    } catch (error) {
      this.end(name);
      throw error;
    }
  }

  /**
   * Measure a synchronous function execution time
   */
  measureSync<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    this.start(name, metadata);
    try {
      const result = fn();
      this.end(name);
      return result;
    } catch (error) {
      this.end(name);
      throw error;
    }
  }

  /**
   * Get all recorded metrics
   */
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values()).filter(metric => metric.duration !== undefined);
  }

  /**
   * Get metrics by name pattern
   */
  getMetricsByPattern(pattern: RegExp): PerformanceMetric[] {
    return this.getMetrics().filter(metric => pattern.test(metric.name));
  }

  /**
   * Get average duration for metrics matching a pattern
   */
  getAverageDuration(pattern: RegExp): number {
    const metrics = this.getMetricsByPattern(pattern);
    if (metrics.length === 0) return 0;

    const totalDuration = metrics.reduce((sum, metric) => sum + (metric.duration || 0), 0);
    return totalDuration / metrics.length;
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
  }

  /**
   * Enable or disable performance monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Generate a performance report
   */
  generateReport(): string {
    const metrics = this.getMetrics();
    if (metrics.length === 0) {
      return 'No performance metrics recorded';
    }

    let report = '📊 Performance Report\n';
    report += '='.repeat(50) + '\n';

    // Group metrics by category
    const categories = new Map<string, PerformanceMetric[]>();
    metrics.forEach(metric => {
      const category = metric.name.split(':')[0] || 'General';
      if (!categories.has(category)) {
        categories.set(category, []);
      }
      categories.get(category)!.push(metric);
    });

    categories.forEach((categoryMetrics, category) => {
      report += `\n${category}:\n`;
      report += '-'.repeat(category.length + 1) + '\n';

      categoryMetrics.forEach(metric => {
        report += `  ${metric.name}: ${metric.duration?.toFixed(2)}ms\n`;
      });

      const avgDuration = categoryMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / categoryMetrics.length;
      report += `  Average: ${avgDuration.toFixed(2)}ms\n`;
    });

    return report;
  }
}

// Create a singleton instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator for measuring method execution time
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const metricName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      return performanceMonitor.measureSync(metricName, () => originalMethod.apply(this, args));
    };

    return descriptor;
  };
}

/**
 * Hook for measuring React component render time
 */
export function usePerformanceMonitor(componentName: string) {
  const startTime = performance.now();

  React.useEffect(() => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (__DEV__) {
      console.log(`🎨 ${componentName} render: ${duration.toFixed(2)}ms`);
    }
  });
}

/**
 * Measure GraphQL query performance
 */
export function measureGraphQLQuery(operationName: string) {
  return {
    onQueryStart: () => {
      performanceMonitor.start(`GraphQL:${operationName}`);
    },
    onQueryEnd: () => {
      performanceMonitor.end(`GraphQL:${operationName}`);
    },
  };
}

/**
 * Measure navigation performance
 */
export function measureNavigation(routeName: string) {
  performanceMonitor.start(`Navigation:${routeName}`);
  
  return () => {
    performanceMonitor.end(`Navigation:${routeName}`);
  };
}

/**
 * Memory usage monitoring
 */
export function logMemoryUsage(label: string = 'Memory Usage') {
  if (!__DEV__) return;

  // For React Native, we can use performance.memory if available
  if (typeof performance !== 'undefined' && (performance as any).memory) {
    const memory = (performance as any).memory;
    console.log(`💾 ${label}:`, {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
    });
  }
}

/**
 * Bundle size analysis helper
 */
export function analyzeBundleSize() {
  if (!__DEV__) return;

  // This is a placeholder for bundle analysis
  // In a real implementation, you would integrate with tools like
  // @expo/webpack-config or Metro bundler to get actual bundle sizes
  console.log('📦 Bundle analysis would be performed here');
}
