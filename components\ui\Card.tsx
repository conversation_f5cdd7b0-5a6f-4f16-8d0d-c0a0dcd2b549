import { StyleSheet, ViewProps } from 'react-native';
import { ThemedView } from './ThemedView';
import { useTheme } from '../../contexts/ThemeContext';

interface CardProps extends ViewProps {
  elevation?: 'none' | 'small' | 'medium' | 'large';
}

export function Card({ style, elevation = 'medium', ...props }: CardProps) {
  const theme = useTheme();
  
  const elevationStyles = {
    none: {},
    small: theme.shadows.small,
    medium: theme.shadows.medium,
    large: theme.shadows.large,
  };
  
  return (
    <ThemedView
      variant="card"
      style={[
        styles.card,
        elevationStyles[elevation],
        style,
      ]}
      {...props}
    />
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 8,
    overflow: 'hidden',
  },
});
