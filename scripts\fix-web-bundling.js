/**
 * <PERSON><PERSON><PERSON> to fix web bundling issues by updating Expo web configuration
 */
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing web bundling configuration...');

// Project root directory
const rootDir = path.resolve(__dirname, '..');

// Create a webpack.config.js file to customize web bundling
const webpackConfigPath = path.join(rootDir, 'webpack.config.js');
const webpackConfigContent = `
const createExpoWebpackConfigAsync = require('@expo/webpack-config');
const path = require('path');

module.exports = async function (env, argv) {
  // Create the default Expo web configuration
  const config = await createExpoWebpackConfigAsync(env, argv);

  // Fix for node:internal/process/task_queues error
  config.resolve.fallback = {
    ...config.resolve.fallback,
    'node:internal/process/task_queues': path.resolve(__dirname, './node-internal-process-task_queues.js'),
  };

  // Fix for missing-asset-registry-path error
  config.resolve.alias = {
    ...config.resolve.alias,
    'missing-asset-registry-path': path.resolve(__dirname, './missing-asset-registry-path.js'),
  };

  // Fix for serializer format issues
  if (config.output) {
    // Ensure the output format is compatible with Metro
    config.output.globalObject = 'this';
  }

  return config;
};
`;

// Write the webpack config file
try {
  fs.writeFileSync(webpackConfigPath, webpackConfigContent);
  console.log(`✅ Created/updated webpack.config.js at ${webpackConfigPath}`);
} catch (error) {
  console.error(`❌ Error creating webpack.config.js:`, error.message);
}

console.log('\n🎉 Web bundling configuration updated!');
console.log('Try running the app now with:');
console.log('npm run web-reset');
