import { useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams } from 'expo-router';
import React, { useMemo, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  TextInput,
  useWindowDimensions
} from 'react-native';

import { OfferCard } from '../../components/offers/OfferCard';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useZone } from '../../contexts/ZoneContext';
import { GET_CATEGORIES, GET_OFFERS } from '../../lib/graphql-operations';
import { createCategories, createOffers } from '../../models';


export default function OffersScreen() {
  const { currentZone, loading: zoneLoading } = useZone();
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const { width } = useWindowDimensions();
  const { cat: routeCategory } = useLocalSearchParams();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Query offers for the current zone
  const { data, loading, error, refetch, fetchMore } = useQuery(GET_OFFERS, {
    variables: {
      criteria: {
        zone: currentZone?.id || '',
        ...(selectedCategory ? { categories: selectedCategory } : {})
      },
      limit: 20,
      skip: 0,
    },
    skip: !currentZone?.id,
  });

  // Query categories for offers
  const { data: categoriesData, loading: categoriesLoading } = useQuery(GET_CATEGORIES, {
    variables: {
      criteria: {
        zone: currentZone?.id || '',
        ctype: { $in: ['offer', 'all'] }
      }
    },
    skip: !currentZone?.id,
  });

  // Process categories
  const categories = useMemo(() => {
    const allCategories = categoriesData?.categories
      ? createCategories(categoriesData.categories)
      : [];

    // Add "All" category at the beginning
    return [
      { id: null, title: 'All' },
      ...allCategories
    ];
  }, [categoriesData]);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing offers:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle load more
  const handleLoadMore = () => {
    if (loading || !data?.offers?.length) return;

    fetchMore({
      variables: {
        skip: data.offers.length,
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          offers: [...prev.offers, ...fetchMoreResult.offers],
        };
      },
    });
  };

  // Convert offers to model instances
  const offers = data?.offers ? createOffers(data.offers) : [];

  // Filter offers by search term
  const filteredOffers = offers.filter(offer => {
    const matchesSearch = searchTerm === '' ||
      offer.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (offer.desc && offer.desc.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = !selectedCategory || offer.categories?.includes(selectedCategory);
    return matchesSearch && matchesCategory;
  });

  // Determine number of columns based on screen width
  const numColumns = width >= 1024 ? 3 : width >= 768 ? 2 : 1;



  // Loading state
  if (zoneLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        {/* Search input */}
        <Card style={styles.searchContainer}>
          <Ionicons name="search" size={20} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search offers..."
            value={searchTerm}
            onChangeText={setSearchTerm}
          />
        </Card>

        {/* Category filters */}
        <ThemedView style={styles.categoryContainer}>
          {categoriesLoading ? (
            <ActivityIndicator size="small" />
          ) : (
            categories.map((category) => (
              <Button
                key={category.id || 'all'}
                title={category.title}
                onPress={() => setSelectedCategory(category.id)}
                variant={selectedCategory === category.id ? 'primary' : 'secondary'}
                size="small"
                style={styles.categoryButton}
              />
            ))
          )}
        </ThemedView>
      </ThemedView>

      {/* Offers grid */}
      {filteredOffers.length > 0 ? (
        <FlatList
          data={filteredOffers}
          renderItem={({ item }) => <OfferCard offer={item} numColumns={numColumns} />}
          keyExtractor={(item) => item.id}
          numColumns={numColumns}
          key={`offers-list-${numColumns}`} // Force re-render when columns change
          contentContainerStyle={styles.offersGrid}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}
        columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={loading && !refreshing ? <ActivityIndicator size="large" /> : null}
        />
      ) : loading ? (
        <ThemedView style={styles.emptyContainer}>
          <ActivityIndicator size="large" />
          <ThemedText style={styles.emptyText}>Loading offers...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.errorText}>
            Error loading offers: {error.message}
          </ThemedText>
          <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
        </ThemedView>
      ) : (
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.emptyText}>
            No offers found. 
          </ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
        pointerEvents: 'auto'

  },
  header: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
    backgroundColor: 'transparent'

  },
  categoryButton: {
    marginRight: 8,
    marginBottom: 8,
  },
  addButton: {
    marginBottom: 16,
  },
  offersGrid: {
    paddingBottom: 16,
  },
  row: {
    flex: 1,
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  createButton: {
    marginTop: 8,
  },
});
