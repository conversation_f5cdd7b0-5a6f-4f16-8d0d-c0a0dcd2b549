import { createUser } from '@/models/User';
import { useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, RefreshControl, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { FriendRequestButton } from '../../components/social/FriendRequestButton';
import { FriendsList } from '../../components/social/FriendsList';
import { Avatar } from '../../components/ui/Avatar';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { GET_USER_FRIENDS } from '../../lib/graphql-operations';

export default function UserProfileScreen() {
  const { currentUser } = useAuth();
  const theme = useTheme();
  const params = useLocalSearchParams();
  const userId = params.id as string;
  const [refreshing, setRefreshing] = useState(false);

  // Query user data with friends
  const { data, loading, error, refetch } = useQuery(GET_USER_FRIENDS, {
    variables: {
      userId,
      limit: 5, // Limit to 5 friends for the preview
    },
    skip: !userId,
  });

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing profile:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Navigate to friends list
  const navigateToFriends = () => {
    router.push(`/profile/friends?userId=${userId}`);
  };

  // Navigate to message conversation
  const navigateToMessages = () => {
    // Create a new conversation or navigate to existing one
    router.push(`/messages/new?userId=${userId}`);
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading profile...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading profile: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
      </ThemedView>
    );
  }

  const user = createUser(data?.user);

  // If user not found
  if (!user) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>User not found</ThemedText>
        <Button title="Go Back" onPress={() => router.back()} style={styles.retryButton} />
      </ThemedView>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <ThemedText variant="title">Profile</ThemedText>
      </ThemedView>

      <ThemedView style={styles.profileHeader}>
        <Avatar
          source={user.userProfile?.avatar}
          name={user.userProfile ? 
            `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim() :
            user.emails?.[0]?.address?.split('@')[0]
          }
          size="large"
          online={user.isOnline}
        />
        <ThemedView style={styles.userInfo}>
          <ThemedText variant="title">
            {user.userProfile ?
              `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim() :
              user.emails?.[0]?.address?.split('@')[0]
            }
          </ThemedText>
          <ThemedText variant="caption">@{user.emails?.[0]?.address?.split('@')[0]}</ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Action Buttons */}
      {userId !== currentUser?.id && (
        <ThemedView style={styles.actionButtons}>
          <FriendRequestButton
            userId={userId}
            style={styles.actionButton}
          />
          <Button
            title="Message"
            variant="outline"
            onPress={navigateToMessages}
            style={styles.actionButton}
          />
        </ThemedView>
      )}

      {user.userProfile?.bio && (
        <Card style={styles.bioCard}>
          <ThemedText variant="subtitle">Bio</ThemedText>
          <ThemedText style={styles.bio}>{user.userProfile.bio}</ThemedText>
        </Card>
      )}

      {/* Friends Section */}
      <Card style={styles.sectionCard}>
        <FriendsList 
          userId={userId}
          limit={5}
          showHeader={true}
          onViewAllPress={navigateToFriends}
        />
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    pointerEvents: 'auto',
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    marginRight: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  userInfo: {
    marginLeft: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  bioCard: {
    marginBottom: 16,
    padding: 16,
  },
  bio: {
    marginTop: 8,
  },
  sectionCard: {
    marginBottom: 16,
    padding: 16,
  },
});
