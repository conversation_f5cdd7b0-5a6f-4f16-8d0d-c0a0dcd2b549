import { useQuery } from '@apollo/client';
import React from 'react';
import { ActivityIndicator, FlatList, StyleSheet } from 'react-native';
import { GET_LIKES } from '../../lib/graphql-operations';
import { formatDate } from '../../utils/date';
import { Avatar } from '../ui/Avatar';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface LikesListProps {
  objectId: string;
  objectType: 'post' | 'place' | 'happening' | 'offer';
}

export function LikesList({ objectId, objectType }: LikesListProps) {
  const { data, loading, error } = useQuery(GET_LIKES, {
    variables: {
      objectId,
      objectType,
    },
  });

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="small" />
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText>Error loading likes: {error.message}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <FlatList
      data={data?.likes || []}
      keyExtractor={(item) => item.id}
                keyboardShouldPersistTaps="handled"
          scrollEnabled={true}

      renderItem={({ item }) => (
        <ThemedView style={styles.likeItem}>
          <Avatar
            source={item.user?.userProfile.userProfile.avatar}
            name={item.user?.userProfile ?
              `${item.user.userProfile.firstName || ''} ${item.user.userProfile.lastName || ''}`.trim() :
              item.user?.email?.split('@')[0]
            }
            size="small"
          />
          <ThemedView style={styles.likeInfo}>
            <ThemedText>{item.user?.userProfile ?
              `${item.user.userProfile.firstName || ''} ${item.user.userProfile.lastName || ''}`.trim() :
              item.user?.email?.split('@')[0]
            }</ThemedText>
            <ThemedText variant="caption">{formatDate(item.createdAt)}</ThemedText>
          </ThemedView>
        </ThemedView>
      )}
      ListEmptyComponent={
        <ThemedView style={styles.emptyContainer}>
          <ThemedText>No likes yet</ThemedText>
        </ThemedView>
      }
    />
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
        pointerEvents: 'auto'

  },
  errorContainer: {
    padding: 16,
        pointerEvents: 'auto'

  },
  likeItem: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  likeInfo: {
    marginLeft: 12,
    justifyContent: 'center',
  },
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
  },
});
