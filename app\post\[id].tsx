import { Post } from '@/models';
import { useMutation, useQuery } from '@apollo/client';
import { useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Dimensions, Image, RefreshControl, ScrollView } from 'react-native';
import RenderHtml from 'react-native-render-html';
import { CommentsList } from '../../components/common/CommentsList';
import { LikeButton } from '../../components/social/LikeButton';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { ADD_COMMENT, GET_POST } from '../../lib/graphql-operations';
import { sharedStyles } from '../../styles/sharedStyles';
import { handleAddComment } from '../../utils/commentUtils';
import { formatDate } from '../../utils/date';
import { getShareableUrl, shareContent } from '../../utils/sharing';

const { width } = Dimensions.get('window');


export default function PostDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [comment, setComment] = useState('');

  // Query post details
  const { data, loading, error, refetch } = useQuery<{ post: Post }>(GET_POST, {
    variables: { id },
  });

  // Add comment mutation
  const [addComment, { loading: commentLoading }] = useMutation(ADD_COMMENT);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing post:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle adding a comment
  const handleAddCommentWrapper = async () => {
    await handleAddComment(addComment, {
      linkedObjectId: id,
      objectType: 'post',
      body: comment,
    }, (cache, newComment) => {
      const existingPost = cache.readQuery({ query: GET_POST, variables: { id } });
      if (existingPost?.post) {
        cache.writeQuery({
          query: GET_POST,
          variables: { id },
          data: {
            post: {
              ...existingPost.post,
              comments: [...(existingPost.post.comments || []), newComment],
            },
          },
        });
      }
    });
  };

  // Handle sharing
  const handleShare = async () => {
    if (!post) return;

    const shareableUrl = getShareableUrl('post', post.id);
    await shareContent(
      post.title || 'Post',
      `Check out this post: ${post.title || 'Post'}`,
      shareableUrl
    );
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={sharedStyles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={sharedStyles.loadingText}>Loading post...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={sharedStyles.errorContainer}>
        <ThemedText style={sharedStyles.errorText}>
          Error loading post: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={onRefresh} style={sharedStyles.retryButton} />
      </ThemedView>
    );
  }

  const post: Post | undefined = data?.post;

  if (!post) {
    return (
      <ThemedView style={sharedStyles.errorContainer}>
        <ThemedText style={sharedStyles.errorText}>Post not found</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ScrollView
      style={sharedStyles.container}
      contentContainerStyle={sharedStyles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <ThemedView style={sharedStyles.header}>
        <ThemedText variant="title">{post.title || 'Untitled Post'}</ThemedText>
        <ThemedText variant="caption" style={sharedStyles.date}>
          {formatDate(post.createdAt)}
          {post.updatedAt !== post.createdAt && ' (edited)'}
        </ThemedText>
      </ThemedView>

      {post.avatar && (
        <Image
          source={{ uri: post.avatarUrl() }}
          style={sharedStyles.avatar}
          resizeMode="cover"
        />
      )}

      {post.desc && (
        <Card style={sharedStyles.section}>
          <RenderHtml
            contentWidth={width}
            source={{ html: post.desc }}
          />
        </Card>
      )}

      <ThemedView style={sharedStyles.actionsContainer}>
        <LikeButton
          objectId={post?.id || ''}
          objectType="post"
          isLiked={false} // or derive from user/likes if available
          likeCount={Array.isArray(post?.likes) ? post.likes.length : 0}
          size="small"
          style={{ flex: 1, marginHorizontal: 4 }}
        />
        <Button
          title="Comment"
          variant="outline"
          size="small"
          style={sharedStyles.actionButton}
          onPress={() => {
            // Scroll to comments section
            // This is a placeholder - in a real app, you would implement scrolling to the comments section
          }}
        />
        <Button
          title="Share"
          variant="outline"
          size="small"
          style={sharedStyles.actionButton}
          onPress={handleShare}
        />
      </ThemedView>

      <Card style={sharedStyles.section}>
        <ThemedText variant="subtitle">Comments ({post.comments?.length || 0})</ThemedText>
        <CommentsList
          comments={post.comments || []}
          onAddComment={handleAddCommentWrapper}
          comment={comment}
          setComment={setComment}
        />
      </Card>
    </ScrollView>
  );
}
