import { useQuery } from '@apollo/client';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Dimensions, StyleSheet, Text, useWindowDimensions, View } from 'react-native';
import { useZone } from '../../contexts/ZoneContext';
import { GET_PLACES, GET_PLACES_IN_POLYGON } from '../../lib/graphql-operations';
import { createPlace } from '../../models/Place';

// We'll check if we're in a browser environment before importing Leaflet
// This allows the component to be imported during SSR without errors
let L: any;
let LeafletComponents: any = null;

// Safe check for browser environment
const isBrowser = typeof window !== 'undefined' && window.document;

if (isBrowser) {
  // Dynamic imports won't work in regular React, so we do a regular import with a try/catch
  try {
    // Import Leaflet and its CSS
    L = require('leaflet');
    require('leaflet/dist/leaflet.css');
    
    // Import React-Leaflet components
    LeafletComponents = require('react-leaflet');
  } catch (e) {
    console.error('Error importing Leaflet:', e);
  }
}

// Helper function to create polygon from bounds
const createPolygonFromBounds = (bounds: any) => {
  const latlngs = [
    bounds.getSouthWest(),
    bounds.getNorthWest(),
    bounds.getNorthEast(),
    bounds.getSouthEast(),
    bounds.getSouthWest(), // Close the polygon by repeating the first point
  ];
  // Convert Leaflet LatLng objects to GeoJSON-compatible arrays
  const coordinates = latlngs.map((latlng) => [latlng.lng, latlng.lat]);
  return {
    type: 'Polygon',
    coordinates: [coordinates],
  };
};


// Component to update map view and bounds - only defined if we're in browser
const MapUpdater = isBrowser ? ({ center, zoom, onBoundsChange }: { 
  center: [number, number]; 
  zoom: number; 
  onBoundsChange: (bounds: any) => void 
}) => {
  const map = LeafletComponents.useMap();

  useEffect(() => {
    map.setView(center, zoom);
  }, [center, zoom, map]);

  useEffect(() => {
    let debounceTimer: ReturnType<typeof setTimeout> | null = null;
    let lastBounds: any | null = null;

    const boundsAreEqual = (b1: any, b2: any) => {
      const sw1 = b1.getSouthWest();
      const ne1 = b1.getNorthEast();
      const sw2 = b2.getSouthWest();
      const ne2 = b2.getNorthEast();
      const threshold = 0.001;
      return (
        Math.abs(sw1.lat - sw2.lat) < threshold &&
        Math.abs(sw1.lng - sw2.lng) < threshold &&
        Math.abs(ne1.lat - ne2.lat) < threshold &&
        Math.abs(ne1.lng - ne2.lng) < threshold
      );
    };

    const updateBounds = () => {
      if (debounceTimer) clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        const bounds = map.getBounds();
        if (!lastBounds || !boundsAreEqual(bounds, lastBounds)) {
          lastBounds = bounds;
          onBoundsChange(bounds);
        }
      }, 1000);
    };

    const bounds = map.getBounds();
    lastBounds = bounds;
    onBoundsChange(bounds);

    map.on('moveend', updateBounds);
    map.on('zoomend', updateBounds);

    return () => {
      if (debounceTimer) clearTimeout(debounceTimer);
      map.off('moveend', updateBounds);
      map.off('zoomend', updateBounds);
    };
  }, [map, onBoundsChange]);

  return null;
} : () => null;

export default function WebMapComponent({ selectedCategory }: { selectedCategory: string | null }) {
  const { currentZone } = useZone();
  const { width, height } = useWindowDimensions();

  const [mapCenter, setMapCenter] = useState<[number, number]>([51.505, -0.09]);
  const [mapZoom, setMapZoom] = useState(13);
  const [mapBounds, setMapBounds] = useState<any | null>(null);
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [isMapReady, setIsMapReady] = useState(false);

  const lastQueryTimeRef = useRef<number>(0);
  const queryMinInterval = 2000;

  // Set map ready state based on browser environment and leaflet import
  useEffect(() => {
    setIsMapReady(isBrowser && !!L && !!LeafletComponents);
  }, []);

  // Query places within polygon bounds
  const { data: visibleData, loading: visibleLoading } = useQuery(GET_PLACES_IN_POLYGON, {
    variables: {
      coordinates: mapBounds ? createPolygonFromBounds(mapBounds) : [],
      ...(selectedCategory && { categories: selectedCategory }),
      zone: currentZone?.id || '',
    },
    fetchPolicy: 'cache-first',
    skip: !mapBounds || (Date.now() - lastQueryTimeRef.current < queryMinInterval),
    onCompleted: () => {
      lastQueryTimeRef.current = Date.now();
    }
  });

  // Fallback query for all places in zone
  const { data: allPlacesData, loading: allPlacesLoading } = useQuery(GET_PLACES, {
    variables: {
      criteria: {
        zone: currentZone?.id || ''
      }
    },
    fetchPolicy: 'cache-first',
    skip: !currentZone || (visibleData?.placesInPolygon && visibleData.placesInPolygon.length > 0)
  });

  const isLoading = visibleLoading || allPlacesLoading;

  const placesToShow = useMemo(() => {
    if (visibleData?.placesInPolygon?.length > 0) {
      return visibleData.placesInPolygon.map(createPlace);
    }
    if (allPlacesData?.places) {
      return allPlacesData.places.map(createPlace);
    }
    return [];
  }, [visibleData, allPlacesData]);

  const filteredPlaces = useMemo(() => {
    return placesToShow.filter((place: { categories?: string[] }) =>
      selectedCategory ? place.categories?.includes(selectedCategory) : true
    );
  }, [placesToShow, selectedCategory]);

  // Handle bounds change
  const onBoundsChange = useCallback((bounds: any) => {
    setMapBounds(bounds);
  }, []);

  // Handle user location - only executed in browser environment
  useEffect(() => {
    if (isBrowser && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        position => {
          const { latitude, longitude } = position.coords;
          setUserLocation([latitude, longitude]);
        },
        error => {
          console.warn('Geolocation error:', error);
        }
      );
    }
  }, []);

  // Leaflet icon fix - only executed in browser environment
  useEffect(() => {
    if (isBrowser && L) {
      delete (L.Icon.Default.prototype as any)._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconRetinaUrl:
          'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
        iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
        shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
      });
    }
  }, [isMapReady]);

  // If the map isn't ready (not in browser or leaflet not loaded), render a placeholder
  if (!isMapReady) {
    return (
      <View style={[styles.mapContainer, { height, width }]}>
        <Text>Map is loading...</Text>
      </View>
    );
  }

  // Destructure components from LeafletComponents
  const { MapContainer, Marker, Popup, TileLayer, ZoomControl } = LeafletComponents;

  // Customize the user's marker icon to be green
  const userIcon = isBrowser && L ? new L.Icon({
    iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png',
    shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41]
  }) : null;

  // Render markers for places
  const placeMarkers = filteredPlaces.map(place => {
    if (!place.loc) return null;
    return (
      <Marker
        key={place.id}
        position={[place.loc.lat, place.loc.lng]}
        title={place.name}
      >
        <Popup>
          <div style={{ minWidth: '150px' }}>
            <a href={`place/${place.id}`}>
            <h3 style={{ margin: '0', fontWeight: 'bold' }}>{place.name}</h3>
            </a>
          </div>
        </Popup>
      </Marker>
    );
  });

  // Update the user marker to use the green icon
  const userMarker = userLocation ? (
    <Marker position={userLocation} title="Your Location" icon={userIcon}>
      <Popup>
        <div style={{ minWidth: '150px' }}>
          <h3 style={{ margin: '0', fontWeight: 'bold' }}>Your Location</h3>
          <p style={{ margin: '0' }}>Lat: {userLocation[0].toFixed(6)}</p>
          <p style={{ margin: '0' }}>Lng: {userLocation[1].toFixed(6)}</p>
        </div>
      </Popup>
    </Marker>

  ) : null;

  return (
    <View style={styles.mapContainer}>
      <MapContainer
        center={mapCenter}
        zoom={mapZoom}
        style={styles.leafletMap}
        zoomControl={false}
      >
        <TileLayer
          attribution='&copy; OpenStreetMap contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <ZoomControl position="bottomright" />
        <MapUpdater center={mapCenter} zoom={mapZoom} onBoundsChange={onBoundsChange} />
        {placeMarkers}
        {userMarker}
      </MapContainer>
    </View>
  );
}

const styles = StyleSheet.create({
  mapContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0, // Ensure the map is at the correct z-index
    elevation: 0, // For Android
    pointerEvents: 'auto', // Allow pointer events on the map
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  },
  leafletMap: {
    width: '100%',
    height: '100%',
  },
  // Add a style for any potential overlapping layers
  overlayLayer: {
    pointerEvents: 'none', // Prevent this layer from intercepting clicks
  },
});