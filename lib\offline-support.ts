import { DocumentNode } from '@apollo/client';
import * as SecureStore from 'expo-secure-store';
import NetInfo from '@react-native-community/netinfo';
import { Platform } from 'react-native';

// Safe localStorage wrapper for web
const safeLocalStorage = {
  getItem: (key: string): string | null => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        return window.localStorage.getItem(key);
      }
    } catch (error) {
      console.error('Error accessing localStorage.getItem:', error);
    }
    return null;
  },
  setItem: (key: string, value: string): void => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem(key, value);
      }
    } catch (error) {
      console.error('Error accessing localStorage.setItem:', error);
    }
  },
  removeItem: (key: string): void => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error accessing localStorage.removeItem:', error);
    }
  }
};

// Types for offline operations
export interface QueuedMutation {
  id: string;
  mutation: DocumentNode;
  variables: any;
  timestamp: number;
  retryCount: number;
  optimisticResponse?: any;
}

export interface NetworkStatus {
  isConnected: boolean;
  isInternetReachable: boolean | null;
  type: string;
}

// Storage keys
const MUTATION_QUEUE_KEY = 'offline_mutation_queue';
const NETWORK_STATUS_KEY = 'last_network_status';

/**
 * Offline Support Service
 * Handles mutation queuing, network monitoring, and background sync
 */
export class OfflineSupport {
  private mutationQueue: QueuedMutation[] = [];
  private networkStatus: NetworkStatus = {
    isConnected: false,
    isInternetReachable: null,
    type: 'unknown'
  };
  private listeners: Array<(status: NetworkStatus) => void> = [];
  private syncInProgress = false;

  constructor() {
    this.initializeNetworkMonitoring();
    this.loadMutationQueue();
  }

  /**
   * Initialize network status monitoring
   */
  private async initializeNetworkMonitoring() {
    try {
      // Get initial network state
      const state = await NetInfo.fetch();
      this.updateNetworkStatus({
        isConnected: state.isConnected ?? false,
        isInternetReachable: state.isInternetReachable,
        type: state.type
      });

      // Subscribe to network state changes
      NetInfo.addEventListener(state => {
        const newStatus = {
          isConnected: state.isConnected ?? false,
          isInternetReachable: state.isInternetReachable,
          type: state.type
        };
        
        this.updateNetworkStatus(newStatus);
        
        // Trigger sync when coming back online
        if (newStatus.isConnected && !this.networkStatus.isConnected) {
          this.syncQueuedMutations();
        }
      });
    } catch (error) {
      console.error('Error initializing network monitoring:', error);
    }
  }

  /**
   * Update network status and notify listeners
   */
  private updateNetworkStatus(status: NetworkStatus) {
    const wasOffline = !this.networkStatus.isConnected;
    this.networkStatus = status;
    
    // Persist network status
    this.persistNetworkStatus(status);
    
    // Notify listeners
    this.listeners.forEach(listener => listener(status));
    
    console.log('📶 Network status updated:', status);
    
    // Auto-sync when coming back online
    if (status.isConnected && wasOffline && this.mutationQueue.length > 0) {
      console.log('🔄 Coming back online, syncing queued mutations...');
      this.syncQueuedMutations();
    }
  }

  /**
   * Add a listener for network status changes
   */
  public addNetworkListener(listener: (status: NetworkStatus) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Get current network status
   */
  public getNetworkStatus(): NetworkStatus {
    return this.networkStatus;
  }

  /**
   * Check if device is online
   */
  public isOnline(): boolean {
    return this.networkStatus.isConnected && this.networkStatus.isInternetReachable !== false;
  }

  /**
   * Queue a mutation for offline execution
   */
  public async queueMutation(
    mutation: DocumentNode,
    variables: any,
    optimisticResponse?: any
  ): Promise<string> {
    const queuedMutation: QueuedMutation = {
      id: `mutation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      mutation,
      variables,
      timestamp: Date.now(),
      retryCount: 0,
      optimisticResponse
    };

    this.mutationQueue.push(queuedMutation);
    await this.persistMutationQueue();
    
    console.log('📝 Mutation queued for offline execution:', queuedMutation.id);
    
    // Try to sync immediately if online
    if (this.isOnline()) {
      this.syncQueuedMutations();
    }
    
    return queuedMutation.id;
  }

  /**
   * Get the current mutation queue
   */
  public getMutationQueue(): QueuedMutation[] {
    return [...this.mutationQueue];
  }

  /**
   * Clear the mutation queue
   */
  public async clearMutationQueue(): Promise<void> {
    this.mutationQueue = [];
    await this.persistMutationQueue();
  }

  /**
   * Sync queued mutations when online
   */
  public async syncQueuedMutations(): Promise<void> {
    if (this.syncInProgress || !this.isOnline() || this.mutationQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log('🔄 Starting sync of queued mutations...');

    const mutationsToSync = [...this.mutationQueue];
    const successfulMutations: string[] = [];

    for (const queuedMutation of mutationsToSync) {
      try {
        // This would be implemented with actual Apollo Client mutation execution
        // For now, we'll simulate success
        console.log('✅ Synced mutation:', queuedMutation.id);
        successfulMutations.push(queuedMutation.id);
      } catch (error) {
        console.error('❌ Failed to sync mutation:', queuedMutation.id, error);
        
        // Increment retry count
        queuedMutation.retryCount++;
        
        // Remove from queue if max retries exceeded
        if (queuedMutation.retryCount >= 3) {
          console.warn('🚫 Max retries exceeded for mutation:', queuedMutation.id);
          successfulMutations.push(queuedMutation.id); // Remove from queue
        }
      }
    }

    // Remove successful mutations from queue
    this.mutationQueue = this.mutationQueue.filter(
      mutation => !successfulMutations.includes(mutation.id)
    );

    await this.persistMutationQueue();
    this.syncInProgress = false;
    
    console.log(`🎉 Sync completed. ${successfulMutations.length} mutations processed.`);
  }

  /**
   * Load mutation queue from storage
   */
  private async loadMutationQueue(): Promise<void> {
    try {
      let queueData: string | null = null;
      
      if (Platform.OS === 'web') {
        queueData = safeLocalStorage.getItem(MUTATION_QUEUE_KEY);
      } else {
        queueData = await SecureStore.getItemAsync(MUTATION_QUEUE_KEY);
      }

      if (queueData) {
        this.mutationQueue = JSON.parse(queueData);
        console.log(`📂 Loaded ${this.mutationQueue.length} queued mutations from storage`);
      }
    } catch (error) {
      console.error('Error loading mutation queue:', error);
      this.mutationQueue = [];
    }
  }

  /**
   * Persist mutation queue to storage
   */
  private async persistMutationQueue(): Promise<void> {
    try {
      const queueData = JSON.stringify(this.mutationQueue);
      
      if (Platform.OS === 'web') {
        safeLocalStorage.setItem(MUTATION_QUEUE_KEY, queueData);
      } else {
        await SecureStore.setItemAsync(MUTATION_QUEUE_KEY, queueData);
      }
    } catch (error) {
      console.error('Error persisting mutation queue:', error);
    }
  }

  /**
   * Persist network status to storage
   */
  private async persistNetworkStatus(status: NetworkStatus): Promise<void> {
    try {
      const statusData = JSON.stringify(status);
      
      if (Platform.OS === 'web') {
        safeLocalStorage.setItem(NETWORK_STATUS_KEY, statusData);
      } else {
        await SecureStore.setItemAsync(NETWORK_STATUS_KEY, statusData);
      }
    } catch (error) {
      console.error('Error persisting network status:', error);
    }
  }
}

// Create singleton instance
export const offlineSupport = new OfflineSupport();
