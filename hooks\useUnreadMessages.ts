import { useQuery } from '@apollo/client';
import { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { GET_CONVERSATIONS, MESSAGE_RECEIVED } from '../lib/graphql-operations';
import useReliableSubscription from './useSubscription';

/**
 * Hook to manage unread message counts
 * @returns An object containing unread message counts and related functions
 */
export function useUnreadMessages() {
  const { user } = useAuth();
  const [totalUnreadCount, setTotalUnreadCount] = useState(0);
  const [conversationUnreadCounts, setConversationUnreadCounts] = useState<Record<string, number>>({});

  // Query to get initial conversation data with unread counts
  const { data, loading, error, refetch } = useQuery(GET_CONVERSATIONS, {
    fetchPolicy: 'cache-and-network',
    skip: !user,
  });

  // Subscribe to new messages
  const { data: subscriptionData } = useReliableSubscription(
    MESSAGE_RECEIVED,
    {},
    (newData) => {
      if (newData?.messageReceived) {
        handleNewMessage(newData.messageReceived);
      }
    }
  );

  // Update unread counts when conversation data changes
  useEffect(() => {
    if (data?.conversations) {
      const counts: Record<string, number> = {};
      let total = 0;

      data.conversations.forEach((conversation: any) => {
        counts[conversation.id] = conversation.unreadCount || 0;
        total += conversation.unreadCount || 0;
      });

      setConversationUnreadCounts(counts);
      setTotalUnreadCount(total);
    }
  }, [data]);

  // Handle new message from subscription
  const handleNewMessage = (message: any) => {
    // Only update counts if the message is not from the current user
    if (message.sender.id !== user?.id) {
      // Find the conversation ID from the data if not directly provided
      // This is needed because the messageReceived subscription doesn't include conversationId directly
      const findConversationId = () => {
        // If conversationId is directly available, use it
        if (message.conversationId) {
          return message.conversationId;
        }

        // Otherwise, try to find the conversation from our existing data
        if (data?.conversations) {
          const conversation = data.conversations.find((conv: any) =>
            conv.lastMessage && conv.lastMessage.id === message.id
          );
          return conversation?.id;
        }

        return null;
      };

      const conversationId = findConversationId();

      if (conversationId) {
        // Update the specific conversation count
        setConversationUnreadCounts((prev) => {
          const newCounts = { ...prev };
          newCounts[conversationId] = (newCounts[conversationId] || 0) + 1;
          return newCounts;
        });

        // Update total count
        setTotalUnreadCount((prev) => prev + 1);
      }
    }
  };

  // Mark a conversation as read
  const markConversationAsRead = (conversationId: string) => {
    const prevCount = conversationUnreadCounts[conversationId] || 0;

    // Update the specific conversation count
    setConversationUnreadCounts((prev) => {
      const newCounts = { ...prev };
      newCounts[conversationId] = 0;
      return newCounts;
    });

    // Update total count
    setTotalUnreadCount((prev) => Math.max(0, prev - prevCount));
  };

  return {
    totalUnreadCount,
    conversationUnreadCounts,
    loading,
    error,
    refetch,
    markConversationAsRead,
  };
}

export default useUnreadMessages;
