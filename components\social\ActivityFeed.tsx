import { useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { createInfiniteScroll } from '../../hooks/useInfiniteScroll';
import { GET_USER_ACTIVITY } from '../../lib/graphql-operations';
import { formatRelativeTime } from '../../utils/date';
import { VirtualizedList } from '../common/VirtualizedList';
import { Avatar } from '../ui/Avatar';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

// Define activity types
export type ActivityType = 'like' | 'comment' | 'checkin' | 'post' | 'friend' | 'offer' | 'happening';

// Define activity item interface
export interface ActivityItem {
  id: string;
  type: ActivityType;
  createdAt: string;
  user: {
    id: string;
    userProfile?: {
      firstName?: string;
      lastName?: string;
      avatar?: string;
    };
    emails?: { address: string }[];
  };
  targetId: string;
  targetType: string;
  targetTitle?: string;
  targetImage?: string;
}

interface ActivityFeedProps {
  userId: string;
  limit?: number;
}

export function ActivityFeed({ userId, limit = 20 }: ActivityFeedProps) {
  const theme = useTheme();

  // Query user activity
  const queryResult = useQuery(GET_USER_ACTIVITY, {
    variables: {
      userId,
      limit,
      offset: 0,
    },
    skip: !userId,
  });

  // Create infinite scroll
  const {
    items: activities,
    loading,
    error,
    refreshing,
    onRefresh,
    handleEndReached,
    hasMore,
  } = createInfiniteScroll<ActivityItem>(queryResult, {
    getItems: (data) => data?.userActivity || [],
    getVariables: (offset, limit) => ({
      userId,
      limit,
      offset,
    }),
    pageSize: limit,
    estimatedItemSize: 80,
    renderItem: ({ item }) => renderActivityItem(item),
    keyExtractor: (item) => item.id,
  });

  // Get activity icon
  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case 'like':
        return 'heart';
      case 'comment':
        return 'chatbubble';
      case 'checkin':
        return 'location';
      case 'post':
        return 'document-text';
      case 'friend':
        return 'people';
      case 'offer':
        return 'pricetag';
      case 'happening':
        return 'calendar';
      default:
        return 'ellipsis-horizontal';
    }
  };

  // Get activity text
  const getActivityText = (item: ActivityItem) => {
    const userName = item.user?.userProfile
      ? `${item.user.userProfile.firstName || ''} ${item.user.userProfile.lastName || ''}`.trim()
      : item.user?.emails?.[0]?.address?.split('@')[0] || 'Unknown User';

    switch (item.type) {
      case 'like':
        return `${userName} liked a ${item.targetType}`;
      case 'comment':
        return `${userName} commented on a ${item.targetType}`;
      case 'checkin':
        return `${userName} checked in at ${item.targetTitle || 'a place'}`;
      case 'post':
        return `${userName} created a new post`;
      case 'friend':
        return `${userName} became friends with someone`;
      case 'offer':
        return `${userName} redeemed an offer`;
      case 'happening':
        return `${userName} is attending an event`;
      default:
        return `${userName} did something`;
    }
  };

  // Handle activity press
  const handleActivityPress = (item: ActivityItem) => {
    if (!item.targetId || !item.targetType) return;

    // Navigate based on target type
    switch (item.targetType.toLowerCase()) {
      case 'post':
        router.push(`/post/${item.targetId}`);
        break;
      case 'place':
        router.push(`/place/${item.targetId}`);
        break;
      case 'happening':
        router.push(`/happening/${item.targetId}`);
        break;
      case 'offer':
        router.push(`/offer/${item.targetId}`);
        break;
      case 'user':
        router.push(`/profile/${item.targetId}`);
        break;
      default:
        console.log(`Unknown target type: ${item.targetType}`);
    }
  };

  // Render activity item
  const renderActivityItem = ({ item }: { item: ActivityItem }) => {
    const userName = item.user?.userProfile
      ? `${item.user.userProfile.firstName || ''} ${item.user.userProfile.lastName || ''}`.trim()
      : item.user?.emails?.[0]?.address?.split('@')[0] || 'Unknown User';
    
    const avatarUrl = item.user?.userProfile?.avatar;
    
    return (
      <TouchableOpacity
        style={styles.activityItem}
        onPress={() => handleActivityPress(item)}
        activeOpacity={0.7}
      >
        <Avatar
          source={avatarUrl}
          name={userName}
          size="small"
        />
        
        <View style={styles.activityContent}>
          <ThemedText style={styles.activityText}>
            {getActivityText(item)}
          </ThemedText>
          
          <ThemedText style={styles.activityTime}>
            {formatRelativeTime(item.createdAt)}
          </ThemedText>
        </View>
        
        <View style={styles.activityIcon}>
          <Ionicons
            name={getActivityIcon(item.type)}
            size={20}
            color={theme.colors.primary}
          />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ThemedView style={styles.container}>
      <VirtualizedList
        data={activities}
        renderItem={renderActivityItem}
        keyExtractor={(item) => item.id}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.5}
        estimatedItemSize={80}
        refreshing={refreshing}
        onRefresh={onRefresh}
        loading={loading}
        error={error}
        emptyText="No activity yet"
        loadingText="Loading activity..."
        errorText="Error loading activity"
        contentContainerStyle={styles.listContent}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  listContent: {
    padding: 10,
  },
  activityItem: {
    flexDirection: 'row',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    alignItems: 'center',
  },
  activityContent: {
    flex: 1,
    marginLeft: 10,
  },
  activityText: {
    fontSize: 14,
  },
  activityTime: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
  activityIcon: {
    marginLeft: 10,
  },
});
