import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import React, { useEffect, useState } from 'react';
import { Modal, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

// Define recurrence types
export type RecurrenceFrequency = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY' | 'NONE';

// Define recurrence interface
export interface RecurrencePattern {
  frequency: RecurrenceFrequency;
  interval: number;
  byWeekday?: number[];
  byMonthDay?: number[];
  byMonth?: number[];
  count?: number;
  until?: Date;
  startDate: Date;
  endDate: Date;
}

interface RecurrenceSelectorProps {
  value?: RecurrencePattern;
  onChange?: (pattern: RecurrencePattern | null) => void;
  startDate: Date;
  endDate: Date;
}

export function RecurrenceSelector({
  value,
  onChange,
  startDate,
  endDate,
}: RecurrenceSelectorProps) {
  const theme = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [recurrenceType, setRecurrenceType] = useState<RecurrenceFrequency>(value?.frequency || 'NONE');
  const [interval, setInterval] = useState(value?.interval || 1);
  const [weekdays, setWeekdays] = useState<number[]>(value?.byWeekday || []);
  const [monthDays, setMonthDays] = useState<number[]>(value?.byMonthDay || []);
  const [months, setMonths] = useState<number[]>(value?.byMonth || []);
  const [endType, setEndType] = useState<'never' | 'count' | 'until'>(
    value?.until ? 'until' : value?.count ? 'count' : 'never'
  );
  const [occurrences, setOccurrences] = useState(value?.count || 10);
  const [untilDate, setUntilDate] = useState(value?.until || new Date(endDate.getTime() + 30 * 24 * 60 * 60 * 1000));
  const [showUntilPicker, setShowUntilPicker] = useState(false);

  // Update recurrence pattern when inputs change
  useEffect(() => {
    if (recurrenceType === 'NONE') {
      onChange?.(null);
      return;
    }

    const pattern: RecurrencePattern = {
      frequency: recurrenceType,
      interval,
      startDate,
      endDate,
    };

    // Add weekdays for weekly recurrence
    if (recurrenceType === 'WEEKLY' && weekdays.length > 0) {
      pattern.byWeekday = weekdays;
    }

    // Add month days for monthly recurrence
    if (recurrenceType === 'MONTHLY' && monthDays.length > 0) {
      pattern.byMonthDay = monthDays;
    }

    // Add months for yearly recurrence
    if (recurrenceType === 'YEARLY' && months.length > 0) {
      pattern.byMonth = months;
    }

    // Add end conditions
    if (endType === 'count') {
      pattern.count = occurrences;
    } else if (endType === 'until') {
      pattern.until = untilDate;
    }

    onChange?.(pattern);
  }, [
    recurrenceType,
    interval,
    weekdays,
    monthDays,
    months,
    endType,
    occurrences,
    untilDate,
    startDate,
    endDate,
  ]);

  // Format recurrence text
  const getRecurrenceText = (): string => {
    if (recurrenceType === 'NONE') {
      return 'Does not repeat';
    }

    let text = '';

    switch (recurrenceType) {
      case 'DAILY':
        text = interval === 1 ? 'Daily' : `Every ${interval} days`;
        break;
      case 'WEEKLY':
        text = interval === 1 ? 'Weekly' : `Every ${interval} weeks`;
        if (weekdays.length > 0) {
          const days = weekdays
            .map((day) => ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][day])
            .join(', ');
          text += ` on ${days}`;
        }
        break;
      case 'MONTHLY':
        text = interval === 1 ? 'Monthly' : `Every ${interval} months`;
        if (monthDays.length > 0) {
          const days = monthDays.map((day) => day.toString()).join(', ');
          text += ` on day${monthDays.length > 1 ? 's' : ''} ${days}`;
        }
        break;
      case 'YEARLY':
        text = interval === 1 ? 'Yearly' : `Every ${interval} years`;
        if (months.length > 0) {
          const monthNames = months
            .map((month) => [
              'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
            ][month - 1])
            .join(', ');
          text += ` in ${monthNames}`;
        }
        break;
    }

    if (endType === 'count') {
      text += `, ${occurrences} time${occurrences > 1 ? 's' : ''}`;
    } else if (endType === 'until') {
      text += `, until ${untilDate.toLocaleDateString()}`;
    }

    return text;
  };

  // Toggle weekday selection
  const toggleWeekday = (day: number) => {
    if (weekdays.includes(day)) {
      setWeekdays(weekdays.filter((d) => d !== day));
    } else {
      setWeekdays([...weekdays, day]);
    }
  };

  // Toggle month day selection
  const toggleMonthDay = (day: number) => {
    if (monthDays.includes(day)) {
      setMonthDays(monthDays.filter((d) => d !== day));
    } else {
      setMonthDays([...monthDays, day]);
    }
  };

  // Toggle month selection
  const toggleMonth = (month: number) => {
    if (months.includes(month)) {
      setMonths(months.filter((m) => m !== month));
    } else {
      setMonths([...months, month]);
    }
  };

  // Render weekday selector
  const renderWeekdaySelector = () => {
    const days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

    return (
      <View style={styles.weekdaySelector}>
        {days.map((day, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.weekdayButton,
              weekdays.includes(index) && { backgroundColor: theme.colors.primary },
            ]}
            onPress={() => toggleWeekday(index)}
          >
            <ThemedText
              style={[
                styles.weekdayText,
                weekdays.includes(index) && { color: '#fff' },
              ]}
            >
              {day}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render month day selector
  const renderMonthDaySelector = () => {
    // Create an array of days 1-31
    const days = Array.from({ length: 31 }, (_, i) => i + 1);

    return (
      <View style={styles.monthDaySelector}>
        {days.map((day) => (
          <TouchableOpacity
            key={day}
            style={[
              styles.monthDayButton,
              monthDays.includes(day) && { backgroundColor: theme.colors.primary },
            ]}
            onPress={() => toggleMonthDay(day)}
          >
            <ThemedText
              style={[
                styles.monthDayText,
                monthDays.includes(day) && { color: '#fff' },
              ]}
            >
              {day}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render month selector
  const renderMonthSelector = () => {
    const monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return (
      <View style={styles.monthSelector}>
        {monthNames.map((name, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.monthButton,
              months.includes(index + 1) && { backgroundColor: theme.colors.primary },
            ]}
            onPress={() => toggleMonth(index + 1)}
          >
            <ThemedText
              style={[
                styles.monthText,
                months.includes(index + 1) && { color: '#fff' },
              ]}
            >
              {name}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setModalVisible(true)}
      >
        <ThemedText style={styles.label}>Repeat</ThemedText>
        <View style={styles.valueContainer}>
          <ThemedText style={styles.value}>{getRecurrenceText()}</ThemedText>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.text} />
        </View>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <ThemedView style={styles.modalContainer}>
          <Card style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Repeat Event</ThemedText>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {/* Frequency selector */}
              <View style={styles.section}>
                <ThemedText style={styles.sectionTitle}>Frequency</ThemedText>
                <View style={styles.radioGroup}>
                  {(['NONE', 'DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY'] as RecurrenceFrequency[]).map((freq) => (
                    <TouchableOpacity
                      key={freq}
                      style={styles.radioOption}
                      onPress={() => setRecurrenceType(freq)}
                    >
                      <View style={styles.radioButton}>
                        {recurrenceType === freq && <View style={styles.radioButtonSelected} />}
                      </View>
                      <ThemedText style={styles.radioLabel}>
                        {freq === 'NONE' ? 'Does not repeat' : 
                         freq === 'DAILY' ? 'Daily' : 
                         freq === 'WEEKLY' ? 'Weekly' : 
                         freq === 'MONTHLY' ? 'Monthly' : 'Yearly'}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {recurrenceType !== 'NONE' && (
                <>
                  {/* Interval selector */}
                  <View style={styles.section}>
                    <ThemedText style={styles.sectionTitle}>Repeat every</ThemedText>
                    <View style={styles.intervalSelector}>
                      <TouchableOpacity
                        style={styles.intervalButton}
                        onPress={() => setInterval(Math.max(1, interval - 1))}
                      >
                        <Ionicons name="remove" size={20} color={theme.colors.text} />
                      </TouchableOpacity>
                      <ThemedText style={styles.intervalValue}>{interval}</ThemedText>
                      <TouchableOpacity
                        style={styles.intervalButton}
                        onPress={() => setInterval(interval + 1)}
                      >
                        <Ionicons name="add" size={20} color={theme.colors.text} />
                      </TouchableOpacity>
                      <ThemedText style={styles.intervalLabel}>
                        {recurrenceType === 'DAILY' ? 'day(s)' : 
                         recurrenceType === 'WEEKLY' ? 'week(s)' : 
                         recurrenceType === 'MONTHLY' ? 'month(s)' : 'year(s)'}
                      </ThemedText>
                    </View>
                  </View>

                  {/* Weekday selector for weekly recurrence */}
                  {recurrenceType === 'WEEKLY' && (
                    <View style={styles.section}>
                      <ThemedText style={styles.sectionTitle}>Repeat on</ThemedText>
                      {renderWeekdaySelector()}
                    </View>
                  )}

                  {/* Month day selector for monthly recurrence */}
                  {recurrenceType === 'MONTHLY' && (
                    <View style={styles.section}>
                      <ThemedText style={styles.sectionTitle}>Repeat on day</ThemedText>
                      {renderMonthDaySelector()}
                    </View>
                  )}

                  {/* Month selector for yearly recurrence */}
                  {recurrenceType === 'YEARLY' && (
                    <View style={styles.section}>
                      <ThemedText style={styles.sectionTitle}>Repeat in</ThemedText>
                      {renderMonthSelector()}
                    </View>
                  )}

                  {/* End selector */}
                  <View style={styles.section}>
                    <ThemedText style={styles.sectionTitle}>Ends</ThemedText>
                    <View style={styles.radioGroup}>
                      <TouchableOpacity
                        style={styles.radioOption}
                        onPress={() => setEndType('never')}
                      >
                        <View style={styles.radioButton}>
                          {endType === 'never' && <View style={styles.radioButtonSelected} />}
                        </View>
                        <ThemedText style={styles.radioLabel}>Never</ThemedText>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.radioOption}
                        onPress={() => setEndType('count')}
                      >
                        <View style={styles.radioButton}>
                          {endType === 'count' && <View style={styles.radioButtonSelected} />}
                        </View>
                        <ThemedText style={styles.radioLabel}>After</ThemedText>
                        <View style={styles.countSelector}>
                          <TouchableOpacity
                            style={styles.countButton}
                            onPress={() => setOccurrences(Math.max(1, occurrences - 1))}
                          >
                            <Ionicons name="remove" size={16} color={theme.colors.text} />
                          </TouchableOpacity>
                          <ThemedText style={styles.countValue}>{occurrences}</ThemedText>
                          <TouchableOpacity
                            style={styles.countButton}
                            onPress={() => setOccurrences(occurrences + 1)}
                          >
                            <Ionicons name="add" size={16} color={theme.colors.text} />
                          </TouchableOpacity>
                          <ThemedText style={styles.countLabel}>occurrences</ThemedText>
                        </View>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.radioOption}
                        onPress={() => setEndType('until')}
                      >
                        <View style={styles.radioButton}>
                          {endType === 'until' && <View style={styles.radioButtonSelected} />}
                        </View>
                        <ThemedText style={styles.radioLabel}>On date</ThemedText>
                        <TouchableOpacity
                          style={styles.dateSelector}
                          onPress={() => setShowUntilPicker(true)}
                        >
                          <ThemedText style={styles.dateValue}>
                            {untilDate.toLocaleDateString()}
                          </ThemedText>
                        </TouchableOpacity>
                      </TouchableOpacity>
                    </View>
                  </View>
                </>
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <Button
                title="Cancel"
                onPress={() => setModalVisible(false)}
                variant="secondary"
                style={styles.footerButton}
              />
              <Button
                title="Done"
                onPress={() => setModalVisible(false)}
                style={styles.footerButton}
              />
            </View>
          </Card>
        </ThemedView>
      </Modal>

      {showUntilPicker && (
        <DateTimePicker
          value={untilDate}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowUntilPicker(false);
            if (selectedDate) {
              setUntilDate(selectedDate);
            }
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
        pointerEvents: 'auto'

  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  label: {
    fontSize: 16,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  value: {
    fontSize: 16,
    marginRight: 5,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 10,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  modalBody: {
    padding: 15,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  footerButton: {
    minWidth: 100,
    marginLeft: 10,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  radioGroup: {
    marginLeft: 10,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#0a7ea4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#0a7ea4',
  },
  radioLabel: {
    fontSize: 16,
  },
  intervalSelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  intervalButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  intervalValue: {
    fontSize: 16,
    marginHorizontal: 10,
  },
  intervalLabel: {
    fontSize: 16,
    marginLeft: 10,
  },
  weekdaySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  weekdayButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 2,
  },
  weekdayText: {
    fontSize: 14,
  },
  monthDaySelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  monthDayButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 2,
  },
  monthDayText: {
    fontSize: 14,
  },
  monthSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  monthButton: {
    width: 60,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 2,
  },
  monthText: {
    fontSize: 14,
  },
  countSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 10,
  },
  countButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  countValue: {
    fontSize: 16,
    marginHorizontal: 10,
  },
  countLabel: {
    fontSize: 16,
    marginLeft: 10,
  },
  dateSelector: {
    marginLeft: 10,
    padding: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  dateValue: {
    fontSize: 16,
  },
});
