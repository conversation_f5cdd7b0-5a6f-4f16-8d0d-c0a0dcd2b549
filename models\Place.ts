import { Category, Place as PlaceInterface } from '@/types';
import { Asset } from 'expo-asset';
/**
 * Place class that implements the PlaceInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Place implements PlaceInterface {
  id!: string;
  name!: string;
  desc?: string;
  type?: string;
  loc?: {
    lat: number;
    lng: number;
  };
  fullLocation?: {
    fullAddress?: string;
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  marker?: any;
  active?: boolean;
  url?: string;
  phone?: string;
  avatar?: string;
  email?: string;
  website?: string;
  hours?: string;
  offerCards?: boolean;
  cardBackground?: string;
  wakeWords?: string;
  alexaDesc?: string;
  members?: {
    id: string;
    profile?: {
      fullName?: string;
      avatar?: string;
    };
    roles?: string[];
  }[];
  categories?: string[];
  categoryDocs?: Category[];
  zone?: string;
  createdAt?: string;
  updatedAt?: string;
  userId?: string;
  likes?: string[];
  comments?: {
    id: string;
    text: string;
    userId: string;
    createdAt: string;
  }[];
  checks?: {
    id: string;
    userId: string;
    createdAt: string;
  }[];
  happenings?: any[];
  offers?: any[];
  proximityads?: any[];

  constructor(placeData: PlaceInterface) {
    Object.assign(this, placeData);
  }

  private ensureProperUrl(url: string): string {
    if (!url) return url;

    // If it already has a protocol, return as is
    if (url.substring(0, 4) === 'http') {
      return url;
    }

    // Add // if needed
    if (url.substring(0, 2) !== '//') {
      url = '//' + url;
    }

    // Add https: protocol
    return 'https:' + url;
  }

  avatarUrl(): string {
    if (this.avatar) {
        return this.ensureProperUrl(this.avatar);
    }
    // Return a default avatar if none is set
    return Asset.fromModule(require('../assets/images/place-placeholder.png')).uri;
  }

  /**
   * Check in to this place
   * @param userId The ID of the user checking in
   * @returns The updated place with the new check-in
   */
  checkin(userId: string): Promise<Place> {
    // This would typically be an API call to the server
    // For now, we'll just update the local object
    const newCheck = {
      id: `check_${Date.now()}`,
      userId,
      linkedObjectId: this.id,
      objectType: 'place',
      checkType: 'checkin',
      createdAt: new Date().toISOString()
    };

    if (!this.checks) {
      this.checks = [];
    }

    this.checks.push(newCheck);
    return Promise.resolve(this);
  }

  /**
   * Create a link to this place
   * @returns A URL to this place's detail page
   */
  linkToMe(): string {
    return `/place/${this.id}`;
  }

  /**
   * Get the icon name for this place based on its categories
   * @returns The name of the icon to use for this place
   */
  iconName(): string {
    // If no categories or categoryDocs, return a default icon
    if ((!this.categories || this.categories.length === 0) &&
        (!this.categoryDocs || this.categoryDocs.length === 0)) {
      return 'faMapMarker';
    }

    // Try to get the icon from the first categoryDoc if available
    if (this.categoryDocs && this.categoryDocs.length > 0) {
      const firstCategoryDoc = this.categoryDocs[0];
      return firstCategoryDoc.iconName || 'faMapMarker';
    }

    // Default fallback
    return 'faMapMarker';
  }

  /**
   * Get the full location as a formatted string
   * @returns A formatted string with the full address
   */
  getFullLocation(): string {
    if (!this.fullLocation) {
      return '';
    }

    const parts = [];

    if (this.fullLocation.street) {
      parts.push(this.fullLocation.street);
    }

    const cityStateZip = [];
    if (this.fullLocation.city) {
      cityStateZip.push(this.fullLocation.city);
    }
    if (this.fullLocation.state) {
      cityStateZip.push(this.fullLocation.state);
    }
    if (this.fullLocation.zip) {
      cityStateZip.push(this.fullLocation.zip);
    }

    if (cityStateZip.length > 0) {
      parts.push(cityStateZip.join(', '));
    }

    if (this.fullLocation.country) {
      parts.push(this.fullLocation.country);
    }

    return parts.join(', ');
  }

  /**
   * Determine if a proximity ad should be shown to a user
   * @param userId The ID of the user to check
   * @returns True if the ad should be shown, false otherwise
   */
  showAdTo(userId: string): boolean {
    // This would typically involve more complex logic
    // For now, we'll just check if the user is the owner
    return this.userId !== userId;
  }
}

/**
 * Factory function to create a Place instance from a plain object
 * @param data The place data
 * @returns A new Place instance
 */
export function createPlace(data: PlaceInterface): Place {
  return new Place(data);
}

/**
 * Factory function to create multiple Place instances from an array
 * @param dataArray Array of place data
 * @returns Array of Place instances
 */
export function createPlaces(dataArray: PlaceInterface[]): Place[] {
  return dataArray.map(data => createPlace(data));
}


