import { useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  TextInput,
  useWindowDimensions
} from 'react-native';

import { HappeningCard } from '../../components/happenings/HappeningCard';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useZone } from '../../contexts/ZoneContext';
import { GET_CATEGORIES, GET_HAPPENINGS } from '../../lib/graphql-operations';
import { createCategories, createHappenings } from '../../models';
import { createUser } from '../../models/User';

export default function HappeningsScreen() {
  const { currentZone, loading: zoneLoading } = useZone();
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const { width } = useWindowDimensions();
  const { user } = useAuth();
  const userCanAddHappening = user && createUser(user).isPlaceOwner();
  const { cat: routeCategory } = useLocalSearchParams();

  // Query categories for events
  const { data: categoriesData, loading: categoriesLoading } = useQuery(GET_CATEGORIES, {
    variables: {
      criteria: {
        zone: currentZone?.id || '',
        ctype: { $in: ['event', 'all'] }
      }
    },
    skip: !currentZone?.id,
  });

  // Memoize variables for GET_HAPPENINGS query
  const happeningsVariables = React.useMemo(() => ({
    criteria: {
      zone: currentZone?.id || '',
      ...(selectedCategory && { categories: selectedCategory })
    },
    limit: 20,
    skip: 0,
  }), [currentZone?.id, selectedCategory]);

  // Query happenings for the current zone
  const { data, loading, error, refetch, fetchMore } = useQuery(GET_HAPPENINGS, {
    variables: happeningsVariables,
    skip: !currentZone?.id,
  });

  // Process categories
  const categories = useMemo(() => {
    const allCategories = categoriesData?.categories
      ? createCategories(categoriesData.categories)
      : [];

    // Add "All" category at the beginning
    return [
      { id: null, title: 'All' },
      ...allCategories
    ];
  }, [categoriesData]);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing happenings:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle load more
  const handleLoadMore = () => {
    if (loading || !data?.happenings?.length) return;

    fetchMore({
      variables: {
        skip: data.happenings.length,
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;

        // Create a map of existing happenings by id
        const existingIds = new Set(prev.happenings.map((h: { id: string }) => h.id));

        // Filter out duplicates from fetchMoreResult
        const newHappenings = fetchMoreResult.happenings.filter((h: { id: string }) => !existingIds.has(h.id));

        return {
          happenings: [...prev.happenings, ...newHappenings],
        };
      },
    });
  };

  // Convert happenings to model instances
  const happenings = React.useMemo(() => {
    return data?.happenings ? createHappenings(data.happenings) : [];
  }, [data?.happenings]);

  // Update selected category based on route parameters
  useEffect(() => {
    if (routeCategory) {
      const category = Array.isArray(routeCategory) ? routeCategory[0] : routeCategory;
      setSelectedCategory(category);
      refetch();
    }
  }, [routeCategory, refetch]);

  // Filter happenings by search term and selected category
  const filteredHappenings = happenings.filter(happening => {
    const matchesSearch = searchTerm === '' ||
      happening.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (happening.desc && happening.desc.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = !selectedCategory || happening.categories?.includes(selectedCategory);
    return matchesSearch && matchesCategory;
  });

  console.log('filteredHappenings Count:', filteredHappenings.length);

  // Determine number of columns based on screen width
  const numColumns = width >= 1024 ? 3 : width >= 768 ? 2 : 1;

  // Loading state
  if (zoneLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        {/* Search input */}
        <Card style={styles.searchContainer}>
          <Ionicons name="search" size={20} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search happenings..."
            value={searchTerm}
            onChangeText={setSearchTerm}
          />
        </Card>

        {/* Category filters */}
        <ThemedView style={styles.categoryContainer}>
          {categoriesLoading ? (
            <ActivityIndicator size="small" />
          ) : (
            categories.map((category) => (
              <Button
                key={category.id || 'all'}
                title={category.title}
                onPress={() => setSelectedCategory(category.id)}
                variant={selectedCategory === category.id ? 'primary' : 'secondary'}
                size="small"
                style={styles.categoryButton}
              />
            ))
          )}
        </ThemedView>

        {/* Add new happening button - only show if user has permission */}
        {userCanAddHappening && (
          <Button
            title="Add New Event"
            onPress={() => router.push('/happening/new')}
            style={styles.addButton}
          />
        )}
      </ThemedView>

      {/* Happenings grid */}
      {filteredHappenings.length > 0 ? (
        <FlatList
          data={filteredHappenings}
          renderItem={({ item }) => <HappeningCard happening={item}  numColumns={numColumns}/>}
          keyExtractor={(item) => item.id}
          numColumns={numColumns}
        key={`happenings-list-${numColumns}`} // Force re-render when columns change
          contentContainerStyle={styles.happeningsGrid}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}
          columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
        />
      ) : loading ? (
        <ThemedView style={styles.emptyContainer}>
          <ActivityIndicator size="large" />
          <ThemedText style={styles.emptyText}>Loading happenings...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.errorText}>
            Error loading happenings: {error.message}
          </ThemedText>
          <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
        </ThemedView>
      ) : (
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.emptyText}>
            No happenings found in this zone. Be the first to add one!
          </ThemedText>
          <Button
            title="Add Event"
            onPress={() => router.push('/happening/new')}
            style={styles.createButton}
          />
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
        pointerEvents: 'auto'

  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
    backgroundColor: 'transparent'
  },
  categoryButton: {
    marginRight: 8,
    marginBottom: 8,
  },
  addButton: {
    marginBottom: 16,
  },
  happeningsGrid: {
    paddingBottom: 16,
  },
  row: {
    flex: 1,
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  createButton: {
    marginTop: 8,
  },
});