import { Comment } from '@/types';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
  FlatList,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';

import { useAuth } from '../../contexts/AuthContext';
import { formatRelativeTime } from '../../utils/date';
import { Avatar } from '../ui/Avatar';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface CommentsListProps {
  comments: Comment[];
  onAddComment: () => void;
  comment: string;
  setComment: (text: string) => void;
}

export function CommentsList({ comments, onAddComment, comment, setComment }: CommentsListProps) {
  const { user } = useAuth();

  // Render a comment item
  const renderCommentItem = ({ item }: { item: Comment }) => {
    const commentOwner = item.owner;
    const ownerName = commentOwner?.userProfile
      ? `${commentOwner.userProfile.firstName || ''} ${commentOwner.userProfile.lastName || ''}`.trim()
      : commentOwner?.emails?.[0]?.address?.split('@')[0] || 'Unknown User';
    
    const avatarUrl = commentOwner?.userProfile?.avatar;
    
    return (
      <Card style={styles.commentCard}>
        <View style={styles.commentHeader}>
          <TouchableOpacity
            style={styles.commentUser}
            onPress={() => commentOwner && router.push(`/user/${commentOwner.id}`)}
          >
            <Avatar
              source={avatarUrl}
              name={ownerName}
              size="small"
            />
            <View style={styles.commentUserInfo}>
              <ThemedText style={styles.commentUserName}>{ownerName}</ThemedText>
              <ThemedText style={styles.commentTime}>
                {formatRelativeTime(item.createdAt || '')}
              </ThemedText>
            </View>
          </TouchableOpacity>
        </View>
        
        <ThemedText style={styles.commentBody}>{item.body}</ThemedText>
        
        <View style={styles.commentActions}>
          <TouchableOpacity style={styles.commentAction}>
            <Ionicons name="heart-outline" size={16} color="#666" />
            <ThemedText style={styles.commentActionText}>
              Like{item.likes && item.likes.length > 0 ? ` (${item.likes.length})` : ''}
            </ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.commentAction}>
            <Ionicons name="chatbubble-outline" size={16} color="#666" />
            <ThemedText style={styles.commentActionText}>Reply</ThemedText>
          </TouchableOpacity>
        </View>
        
        {item.childComments && item.childComments.length > 0 && (
          <View style={styles.repliesContainer}>
            {item.childComments.map(reply => (
              <View key={reply.id} style={styles.replyItem}>
                <View style={styles.replyHeader}>
                  <TouchableOpacity
                    style={styles.commentUser}
                    onPress={() => reply.owner && router.push(`/user/${reply.owner.id}`)}
                  >
                    <Avatar
                      source={reply.owner?.userProfile?.avatar}
                      name={reply.owner?.userProfile
                        ? `${reply.owner.userProfile.firstName || ''} ${reply.owner.userProfile.lastName || ''}`.trim()
                        : reply.owner?.emails?.[0]?.address?.split('@')[0] || 'Unknown User'}
                      size="xsmall"
                    />
                    <View style={styles.commentUserInfo}>
                      <ThemedText style={styles.replyUserName}>
                        {reply.owner?.userProfile
                          ? `${reply.owner.userProfile.firstName || ''} ${reply.owner.userProfile.lastName || ''}`.trim()
                          : reply.owner?.emails?.[0]?.address?.split('@')[0] || 'Unknown User'}
                      </ThemedText>
                      <ThemedText style={styles.commentTime}>
                        {formatRelativeTime(reply.createdAt || '')}
                      </ThemedText>
                    </View>
                  </TouchableOpacity>
                </View>
                
                <ThemedText style={styles.replyBody}>{reply.body}</ThemedText>
              </View>
            ))}
          </View>
        )}
      </Card>
    );
  };

  return (
    <ThemedView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={100}
        style={styles.keyboardAvoidingView}
      >
        <FlatList
          data={comments}
          renderItem={renderCommentItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.commentsList}          
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}
          ListEmptyComponent={
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.emptyText}>No comments yet. Be the first to comment!</ThemedText>
            </ThemedView>
          }
        />
        
        {user && (
          <Card style={styles.commentInputContainer}>
            <TextInput
              style={styles.commentInput}
              placeholder="Write a comment..."
              value={comment}
              onChangeText={setComment}
              multiline
            />
            <Button
              title="Post"
              onPress={onAddComment}
              disabled={!comment.trim()}
              size="small"
            />
          </Card>
        )}
      </KeyboardAvoidingView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  keyboardAvoidingView: {
    flex: 1,
  },
  commentsList: {
    paddingBottom: 16,
  },
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    textAlign: 'center',
    opacity: 0.7,
  },
  commentCard: {
    marginBottom: 12,
    padding: 12,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  commentUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  commentUserInfo: {
    marginLeft: 8,
  },
  commentUserName: {
    fontWeight: '500',
  },
  commentTime: {
    fontSize: 12,
    opacity: 0.7,
  },
  commentBody: {
    marginBottom: 8,
    lineHeight: 20,
  },
  commentActions: {
    flexDirection: 'row',
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#ddd',
    paddingTop: 8,
  },
  commentAction: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  commentActionText: {
    fontSize: 12,
    marginLeft: 4,
  },
  repliesContainer: {
    marginTop: 8,
    paddingLeft: 16,
    borderLeftWidth: 1,
    borderLeftColor: '#ddd',
  },
  replyItem: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#ddd',
  },
  replyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  replyUserName: {
    fontWeight: '500',
    fontSize: 13,
  },
  replyBody: {
    fontSize: 13,
    lineHeight: 18,
  },
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    marginTop: 8,
  },
  commentInput: {
    flex: 1,
    minHeight: 40,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
});
