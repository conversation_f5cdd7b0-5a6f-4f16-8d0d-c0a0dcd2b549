# Legacy App Components Documentation

This document provides a comprehensive overview of the components in the legacy Meteor app, their functionality, and their implementation status in the Expo app.

## Core Templates and Components

### 1. Page Components

#### PagePage Template
- **Description**: Displays a single page with title and content
- **Functionality**:
  - Renders HTML content from the page body
  - Provides edit functionality for owners/admins
  - Supports full-screen mode
- **Data Operations**:
  - Subscribes to single page data
  - Subscribes to likes and comments for the page
- **Implementation Status**: 
  - ✅ Basic implementation in `app/page/[id].tsx`
  - ❌ Missing HTML content rendering (currently renders as plain text)
  - ❌ Missing edit functionality for owners/admins

#### PlacePage Template
- **Description**: Displays a single place with details, map, and related content
- **Functionality**:
  - Shows place image, name, description, address, phone, website
  - Provides action buttons for directions, phone calls, website visits
  - Includes tabs for events, offers, likes, check-ins, recommendations, and comments
  - Shows visit statistics for owners
  - Supports gift card purchases if enabled
- **Data Operations**:
  - Subscribes to single place data
  - Subscribes to likes, comments, and check-ins for the place
  - Loads related happenings, offers, and recommendations
- **Implementation Status**:
  - ✅ Basic implementation in `app/place/[id].tsx`
  - ✅ Map display with marker
  - ❌ Missing tabs for events, offers, likes, check-ins, recommendations
  - ❌ Missing action buttons for social features (like, check-in, recommend)
  - ❌ Missing gift card functionality

#### PostPage Template
- **Description**: Displays a single post with details and social interactions
- **Functionality**:
  - Shows post title, content, author, and creation date
  - Provides social interaction buttons (like, comment)
- **Data Operations**:
  - Subscribes to single post data
  - Subscribes to likes and comments for the post
- **Implementation Status**:
  - ✅ Basic implementation in `app/post/[id].tsx`
  - ✅ Displays post title, content, author, and creation date
  - ❌ Missing social interaction functionality

#### HappeningPage Template
- **Description**: Displays a single event with details and social interactions
- **Functionality**:
  - Shows event title, description, date/time, location
  - Provides social interaction buttons (like, check-in, comment)
- **Data Operations**:
  - Subscribes to single happening data
  - Subscribes to likes, comments, and check-ins for the happening
- **Implementation Status**:
  - ✅ Basic implementation in `app/happening/[id].tsx`
  - ❌ Missing social interaction functionality

#### OfferPage Template
- **Description**: Displays a single offer with details and redemption options
- **Functionality**:
  - Shows offer title, description, expiration date
  - Provides QR code for redemption
  - Includes scanning functionality for redeeming offers
- **Data Operations**:
  - Subscribes to single offer data
  - Subscribes to likes, comments, and check-ins for the offer
- **Implementation Status**:
  - ✅ Basic implementation in `app/offer/[id].tsx`
  - ❌ Missing QR code generation and scanning functionality

#### UserPage Template
- **Description**: Displays a user profile with details and activity
- **Functionality**:
  - Shows user avatar, name, bio
  - Displays tabs for friends, check-ins, recommendations, and activity feed
  - Provides social interaction buttons (add friend, message)
  - Shows subscription status for premium accounts
- **Data Operations**:
  - Subscribes to user data
  - Subscribes to friend requests, check-ins, and recommendations by the user
  - Loads user's activity feed
- **Implementation Status**:
  - ✅ Basic implementation in `app/user/[id].tsx`
  - ❌ Missing tabs for friends, check-ins, recommendations
  - ❌ Missing social interaction functionality
  - ❌ Missing subscription status display

### 2. List Components

#### Places Template
- **Description**: Displays a list of places with filtering options
- **Functionality**:
  - Shows places in a list or grid view
  - Provides category filtering
  - Supports infinite scrolling
- **Data Operations**:
  - Subscribes to places in the current zone
  - Loads places by category if specified
- **Implementation Status**:
  - ✅ Basic implementation in `app/places/index.tsx`
  - ✅ Category filtering
  - ✅ Infinite scrolling
  - ❌ Missing map integration for place locations

#### Happenings Template
- **Description**: Displays a list of events with filtering options
- **Functionality**:
  - Shows events in a list view
  - Provides date and category filtering
  - Shows expired events toggle
- **Data Operations**:
  - Subscribes to happenings in the current zone
  - Loads happenings by category and date if specified
- **Implementation Status**:
  - ✅ Basic implementation in `app/happenings/index.tsx`
  - ❌ Missing date filtering
  - ❌ Missing expired events toggle

#### Posts Template
- **Description**: Displays a list of posts with filtering options
- **Functionality**:
  - Shows posts in a list view
  - Provides category filtering
- **Data Operations**:
  - Subscribes to posts in the current zone
  - Loads posts by category if specified
- **Implementation Status**:
  - ✅ Basic implementation in `app/posts/index.tsx`
  - ✅ Category filtering

#### Offers Template
- **Description**: Displays a list of offers with filtering options
- **Functionality**:
  - Shows offers in a list view
  - Provides category filtering
- **Data Operations**:
  - Subscribes to offers in the current zone
  - Loads offers by category if specified
- **Implementation Status**:
  - ✅ Basic implementation in `app/offers/index.tsx`
  - ❌ Missing category filtering

#### Users Template
- **Description**: Displays a list of users with filtering options
- **Functionality**:
  - Shows users in a list view
  - Provides category filtering
  - Shows friends-only toggle
- **Data Operations**:
  - Subscribes to users in the current zone
  - Loads users by category if specified
  - Loads friends if friends-only is selected
- **Implementation Status**:
  - ✅ Basic implementation in `app/users/index.tsx`
  - ❌ Missing category filtering
  - ❌ Missing friends-only toggle

## 3. Map and Location Features

#### Map Template
- **Description**: Displays an interactive map with places and user locations
- **Functionality**:
  - Shows places as markers on the map
  - Provides category filtering for places
  - Shows user's current location
  - Shows friends' locations
  - Supports directions between locations
- **Data Operations**:
  - Subscribes to places in the visible map area
  - Tracks user location updates
  - Loads friends' locations
- **Implementation Status**:
  - ✅ Basic map implementation with `react-native-maps`
  - ❌ Missing place markers with category filtering
  - ❌ Missing user and friend location tracking
  - ❌ Missing directions functionality

#### Directions Feature
- **Description**: Provides directions between locations
- **Functionality**:
  - Shows route on the map
  - Provides turn-by-turn directions
  - Supports different transportation modes
- **Data Operations**:
  - Uses external routing service for directions
- **Implementation Status**:
  - ❌ Not implemented

#### Proximity Ads Feature
- **Description**: Shows ads when users are near specific places
- **Functionality**:
  - Triggers ads based on user proximity to places
  - Supports different trigger conditions (first visit, every visit)
- **Data Operations**:
  - Checks user location against place locations
  - Tracks visit history
- **Implementation Status**:
  - ❌ Not implemented

## 4. Social Features

#### Like/Unlike Functionality
- **Description**: Allows users to like and unlike content
- **Functionality**:
  - Provides like/unlike buttons for various content types
  - Shows like counts
  - Shows who liked the content
- **Data Operations**:
  - Creates/removes like records
  - Updates user's activity feed
- **Implementation Status**:
  - ❌ Not implemented

#### Check-in Functionality
- **Description**: Allows users to check in at places
- **Functionality**:
  - Provides check-in button for places
  - Shows check-in history
  - Updates user's activity feed
- **Data Operations**:
  - Creates check-in records
  - Updates user's activity feed
- **Implementation Status**:
  - ❌ Not implemented

#### Friend Request Functionality
- **Description**: Allows users to send and respond to friend requests
- **Functionality**:
  - Provides add friend, accept request, deny request buttons
  - Shows friend request notifications
- **Data Operations**:
  - Creates/updates friend request records
  - Sends push notifications
- **Implementation Status**:
  - ❌ Not implemented

#### Messaging Functionality
- **Description**: Allows users to send direct messages to each other
- **Functionality**:
  - Provides messaging interface
  - Shows message history
  - Supports real-time updates
- **Data Operations**:
  - Creates message records
  - Uses subscriptions for real-time updates
- **Implementation Status**:
  - ✅ Basic implementation in `app/messages/[id].tsx`
  - ✅ Real-time messaging with GraphQL subscriptions
  - ❌ Missing unread message indicators

#### Recommendation Functionality
- **Description**: Allows users to recommend places to others
- **Functionality**:
  - Provides recommend button for places
  - Shows recommendation history
  - Updates user's activity feed
- **Data Operations**:
  - Creates recommendation records
  - Updates user's activity feed
- **Implementation Status**:
  - ❌ Not implemented

## 5. Other Features

#### Gift Card Functionality
- **Description**: Allows users to purchase and redeem gift cards
- **Functionality**:
  - Provides gift card purchase interface
  - Shows gift card balance
  - Supports gift card redemption
- **Data Operations**:
  - Creates gift card records
  - Tracks gift card balance and usage
- **Implementation Status**:
  - ❌ Not implemented

#### Campaign and Challenge Functionality
- **Description**: Supports promotional campaigns with challenges
- **Functionality**:
  - Shows active campaigns
  - Provides challenge completion interface
  - Tracks challenge progress
  - Shows leaderboards
- **Data Operations**:
  - Loads active campaigns and challenges
  - Tracks challenge completion
  - Updates scores and leaderboards
- **Implementation Status**:
  - ❌ Not implemented

#### Rich Text Editing
- **Description**: Provides rich text editing for content creation
- **Functionality**:
  - Supports formatting options (bold, italic, etc.)
  - Allows embedding images and links
  - Supports linking to app content (places, events, etc.)
- **Data Operations**:
  - Stores formatted content
- **Implementation Status**:
  - ❌ Not implemented

#### Footer Component
- **Description**: Displays footer information
- **Functionality**:
  - Shows app version
  - Shows current zone information
  - Provides links to additional resources
- **Data Operations**:
  - Loads zone information
- **Implementation Status**:
  - ❌ Not implemented
