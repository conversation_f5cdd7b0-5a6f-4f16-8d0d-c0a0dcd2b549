import { Comment, Like, Post as PostInterface } from '@/types';
import { Asset } from 'expo-asset';
/**
 * Post class that implements the PostInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Post implements PostInterface {
  id: string;
  title: string;
  desc?: string;
  zone: string;
  avatar?: string;
  userId: string;
  createdAt: string;
  updatedAt?: string;
  likes?: Like[];
  comments?: Comment[];
  categories?: string[];

  constructor(postData: PostInterface) {
    Object.assign(this, postData);
  }

  /**
   * Get the URL for this post
   * @returns The URL for the post
   */
  linkToMe(): string {
    return `/post/${this.id}`;
  }

  /**
   * Get the formatted creation date
   * @returns A formatted string with the creation date
   */
  formattedDate(): string {
    if (!this.createdAt) {
      return '';
    }
    
    const date = new Date(this.createdAt);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Get the excerpt of the post description
   * @param length The maximum length of the excerpt
   * @returns A truncated version of the post description
   */
  excerpt(length: number = 150): string {
    if (!this.desc) {
      return '';
    }
    
    // Remove HTML tags
    const textOnly = this.desc.replace(/<[^>]*>/g, '');
    
    if (textOnly.length <= length) {
      return textOnly;
    }
    
    // Truncate and add ellipsis
    return textOnly.substring(0, length) + '...';
  }

  /**
   * Check if the user has liked this post
   * @param userId The ID of the user to check
   * @returns True if the user has liked the post, false otherwise
   */
  isLikedBy(userId: string): boolean {
    if (!this.likes || !userId) {
      return false;
    }
    
    return this.likes.some(like => like.userId === userId);
  }

  /**
   * Get the number of likes for this post
   * @returns The number of likes
   */
  likeCount(): number {
    return this.likes?.length || 0;
  }

  /**
   * Get the number of comments for this post
   * @returns The number of comments
   */
  commentCount(): number {
    return this.comments?.length || 0;
  }

  private ensureProperUrl(url: string): string {
    if (!url) return url;

    // If it already has a protocol, return as is
    if (url.substring(0, 4) === 'http') {
      return url;
    }

    // Add // if needed
    if (url.substring(0, 2) !== '//') {
      url = '//' + url;
    }

    // Add https: protocol
    return 'https:' + url;
  }

  avatarUrl(): string {
    if (this.avatar) {
        return this.ensureProperUrl(this.avatar);
    }
    // Return a default avatar if none is set
    return Asset.fromModule(require('../assets/images/post-placeholder.png')).uri;
  }

  /**
   * Add a like to this post
   * @param userId The ID of the user liking the post
   * @returns The updated post
   */
  addLike(userId: string): Post {
    if (!this.likes) {
      this.likes = [];
    }
    
    // Check if the user has already liked the post
    if (!this.isLikedBy(userId)) {
      this.likes.push({
        id: `like_${Date.now()}`,
        userId,
        postId: this.id,
        createdAt: new Date().toISOString()
      });
    }
    
    return this;
  }

  /**
   * Remove a like from this post
   * @param userId The ID of the user unliking the post
   * @returns The updated post
   */
  removeLike(userId: string): Post {
    if (!this.likes) {
      return this;
    }
    
    this.likes = this.likes.filter(like => like.userId !== userId);
    return this;
  }

  /**
   * Add a comment to this post
   * @param userId The ID of the user commenting
   * @param content The content of the comment
   * @returns The updated post
   */
  addComment(userId: string, content: string): Post {
    if (!this.comments) {
      this.comments = [];
    }
    
    this.comments.push({
      id: `comment_${Date.now()}`,
      content,
      userId,
      postId: this.id,
      createdAt: new Date().toISOString()
    });
    
    return this;
  }
}

/**
 * Factory function to create a Post instance from a plain object
 * @param data The post data
 * @returns A new Post instance
 */
export function createPost(data: PostInterface): Post {
  return new Post(data);
}

/**
 * Factory function to create multiple Post instances from an array
 * @param dataArray Array of post data
 * @returns Array of Post instances
 */
export function createPosts(dataArray: PostInterface[]): Post[] {
  return dataArray.map(data => createPost(data));
}
