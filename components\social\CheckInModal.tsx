import React, { useState } from 'react';
import { Modal, StyleSheet, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Button } from '../ui/Button';
import { TextInput } from '../ui/TextInput';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface CheckInModalProps {
  visible: boolean;
  onClose: () => void;
  onCheckIn: (comment?: string) => void;
  objectType: 'place' | 'happening' | 'offer';
}

export function CheckInModal({ 
  visible, 
  onClose, 
  onCheckIn,
  objectType
}: CheckInModalProps) {
  const [comment, setComment] = useState('');
  const theme = useTheme();

  const handleCheckIn = () => {
    onCheckIn(comment.trim() || undefined);
    setComment('');
  };

  const handleClose = () => {
    setComment('');
    onClose();
  };

  // Get appropriate title based on object type
  const getTitle = () => {
    switch (objectType) {
      case 'place':
        return 'Check In at this Place';
      case 'happening':
        return 'Check In at this Event';
      case 'offer':
        return 'Check In for this Offer';
      default:
        return 'Check In';
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <ThemedView style={styles.container}>
          <ThemedText variant="title" style={styles.title}>
            {getTitle()}
          </ThemedText>
          
          <ThemedText style={styles.description}>
            Let others know you're here! Add an optional comment about your experience.
          </ThemedText>
          
          <TextInput
            value={comment}
            onChangeText={setComment}
            placeholder="Add a comment (optional)"
            multiline
            numberOfLines={3}
            style={styles.input}
            containerStyle={styles.inputContainer}
          />
          
          <View style={styles.buttonContainer}>
            <Button
              title="Cancel"
              onPress={handleClose}
              variant="outline"
              style={styles.button}
            />
            <Button
              title="Check In"
              onPress={handleCheckIn}
              style={styles.button}
            />
          </View>
        </ThemedView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    width: '100%',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
        pointerEvents: 'auto'

  },
  title: {
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 20,
  },
  input: {
    height: 100,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
});
