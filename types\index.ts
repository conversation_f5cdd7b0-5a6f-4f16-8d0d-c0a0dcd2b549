export interface Category {
  id: string;
  title: string;
  ctype: string;
  zone: string;
  iconName?: string;
  count?: number;
  createdAt?: string;
  updatedAt?: string;
}

// User type - aligned with GraphQL schema
export interface User {
  id: string;
  emails?: Email[];
  registered_emails?: Email[];
  status?: string;
  zone: string;
  userProfile?: Profile;
  lastOnline?: string;
  visible?: boolean;
  categories?: string[];
  createdAt?: string;
  updatedAt?: string;
  roles?: string[];
  loc?: {
    lat: number;
    lng: number;
  };
  stripe?: any; // StripePayments type not yet defined

  // Relationships from GraphQL schema
  ownedPlaces?: Place[];
  ownedPosts?: Post[];
  ownedHappenings?: Happening[];
  ownedOffers?: Offer[];
  placeMembership?: string[];
  placeMembershipDetails?: Place[];
  friends?: User[];

  // Client-side only fields (not stored in DB)
  email?: string; // Derived from emails[0].address
  zones?: Zone[]; // Computed from zone
}

// Email type for User
export interface Email {
  address: string;
  verified?: boolean;
}

// Profile type for User
export interface Profile {
  id: string;
  userId: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  avatar?: string;
  visible?: boolean;
  user?: User;
}

// Like type for interactions
export interface Like {
  id: string;
  userId: string;
  linkedObjectId: string;
  objectType: string;
  createdAt?: string;
  updatedAt?: string;

  // Relationships
  owner?: User;

  // Dynamic relationships based on objectType
  place?: Place;
  post?: Post;
  happening?: Happening;
  offer?: Offer;
  comment?: Comment;
  page?: Page;
  fave?: any; // Fave type not yet defined
  proximityAd?: any; // ProximityAd type not yet defined
  user?: User;
}

// Zone type - aligned with GraphQL schema
export interface Zone {
  id: string;
  title: string; // GraphQL uses title, not name
  center?: {
    lat: number;
    lng: number;
  };
  createdAt?: string;
  updatedAt?: string;
  appName?: string;
  temp?: string;
  skytext?: string;
  weatherUpdatedAt?: string;
  subscriptionsEnabled?: boolean;
  useGiftCards?: boolean;
  giftcardHoldback?: number;
  welcomePage?: string;
  signUpText?: string;
  subConfig?: {
    amount?: number;
    currency?: string;
    productCode?: string;
    meta?: {
      subscription?: {
          duration?: {
              years? : number;
          }
      };
      placeLimit?: boolean;
    };
  }
  // Client-side only fields (not stored in DB)
  name?: string; // Alias for title for backward compatibility
  desc?: string; // Derived from properties or computed
  image?: string; // Derived from properties or computed
  pagesMenuLabel?: string; // Client-side configuration
  rootPage?: string; // Client-side configuration

  // Legacy/compatibility fields - should be phased out
  location?: {
    type: string;
    coordinates: [number, number]; // [longitude, latitude]
  };
  loc?: {
    lat: number;
    lng: number;
  };
  boundary?: {
    type: string;
    coordinates: [number, number][][]; // GeoJSON Polygon coordinates
  };

  // Relationships - these are resolved through GraphQL resolvers
  members?: User[];
  places?: Place[];
  posts?: Post[];
  happenings?: Happening[];
  offers?: Offer[];
  campaigns?: Campaign[];
}

// Place type - aligned with GraphQL schema
export interface Place {
  id: string;
  name: string;
  desc?: string;
  type?: string;
  loc?: {
    lat: number;
    lng: number;
  };
  fullLocation?: {
    fullAddress?: string;
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  marker?: any;
  active?: boolean;
  url?: string;
  phone?: string;
  avatar?: string;
  email?: string;
  website?: string;
  hours?: string;
  offerCards?: boolean;
  cardBackground?: string;
  wakeWords?: string;
  alexaDesc?: string;
  members?: {
    id: string;
    profile?: {
      fullName?: string;
      avatar?: string;
    };
    roles?: string[];
  }[];
  categories?: string[];
  categoryDocs?: Category[];
  zone?: string;
  createdAt?: string;
  updatedAt?: string;
  userId?: string;
  likes?: string[];
  comments?: {
    id: string;
    text: string;
    userId: string;
    createdAt: string;
  }[];
  checks?: {
    id: string;
    userId: string;
    createdAt: string;
  }[];
}

// Post type - aligned with GraphQL schema
export interface Post {
  id: string;
  title: string;
  desc?: string;
  zone: string;
  avatar?: string;
  userId: string;
  createdAt?: string;
  updatedAt?: string;
  categories?: string[];

  // Relationships from GraphQL schema
  owner?: User;
  likes?: Like[];
  comments?: Comment[];
  place?: Place;
  categoryDocs?: any[]; // Category type not yet defined

  // Client-side only fields (not stored in DB)
  images?: string[]; // Derived from avatar or properties
  commentCount?: number; // Computed from comments.length
}

// Page type - aligned with GraphQL schema
export interface Page {
  id: string;
  title: string;
  body: string;
  zone: string;
  userId: string;
  createdAt?: string;
  updatedAt?: string;
  categories?: string[];

  // Relationships from GraphQL schema
  owner?: User;
  likes?: Like[];
  comments?: Comment[];
  categoryDocs?: any[]; // Category type not yet defined

  // Client-side only fields (not stored in DB)
  images?: string[]; // Derived from properties or computed
  commentCount?: number; // Computed from comments.length
}

// Comment type - aligned with GraphQL schema
export interface Comment {
  id: string;
  userId: string;
  body: string;
  linkedObjectId: string;
  objectType: string;
  createdAt?: string;
  updatedAt?: string;

  // Relationships from GraphQL schema
  owner?: User;
  childComments?: Comment[];
  likes?: Like[];

  // Dynamic relationships based on objectType
  place?: Place;
  post?: Post;
  happening?: Happening;
  offer?: Offer;
  page?: Page;
  fave?: any; // Fave type not yet defined
  parentComment?: Comment;

  // Client-side only fields (not stored in DB)
  content?: string; // Alias for body
}

// Happening (Event) type - aligned with GraphQL schema
export interface Happening {
  id: string;
  title: string;
  desc?: string;
  zone: string;
  userId?: string;
  place?: string; // ID of the place
  when?: {
    start: string;
    end: string;
  };
  createdAt?: string;
  updatedAt?: string;
  categories?: string[];
  recurrence?: {
    frequency: string;
    interval: number;
    byWeekday?: number[];
    byMonthDay?: number[];
    byMonth?: number[];
    count?: number;
    until?: string;
    rrule?: string;
  };

  // Relationships from GraphQL schema
  owner?: User;
  placeDetails?: Place; // The place object
  likes?: Like[];
  comments?: Comment[];
  categoryDocs?: any[]; // Category type not yet defined

  // Client-side only fields (not stored in DB)
  images?: string[]; // Derived from properties or avatar
  organizer?: User; // Alias for owner
  placeId?: string; // Alias for place
}

// Offer type - aligned with GraphQL schema
export interface Offer {
  id: string;
  title: string;
  desc?: string;
  zone: string;
  userId?: string;
  placeId?: string;
  expires?: string;
  createdAt?: string;
  updatedAt?: string;
  categories?: string[];

  // Relationships from GraphQL schema
  owner?: User;
  place?: Place;
  likes?: Like[];
  comments?: Comment[];
  checks?: any[]; // Check type not yet defined
  categoryDocs?: any[]; // Category type not yet defined

  // Client-side only fields (not stored in DB)
  images?: string[]; // Derived from properties or avatar
  creator?: User; // Alias for owner
  checkIns?: number; // Computed from checks.length
}

// Campaign type - aligned with GraphQL schema
export interface Campaign {
  id: string;
  title: string;
  desc: string;
  image?: string;
  starts?: string;
  stops?: string;
  zoneId: string;
  creatorId?: string;
  createdAt?: string;
  updatedAt?: string;

  // Relationships from GraphQL schema
  zone?: Zone;
  creator?: User;
  challenges?: Challenge[];

  // Client-side only fields (not stored in DB)
  participants?: User[]; // Derived from challenge completions
  challengeCount?: number; // Computed from challenges.length
}

// Challenge type - aligned with GraphQL schema
export interface Challenge {
  id: string;
  title: string;
  desc: string;
  image?: string;
  campaignId: string;
  createdAt?: string;
  updatedAt?: string;

  // Relationships from GraphQL schema
  campaign?: Campaign;
  completions?: ChallengeCompletion[];
}

// Challenge Completion type - aligned with GraphQL schema
export interface ChallengeCompletion {
  id: string;
  userId: string;
  challengeId: string;
  completedAt: string;
  proof?: string;
  createdAt?: string;
  updatedAt?: string;

  // Relationships from GraphQL schema
  user?: User;
  challenge?: Challenge;
}

// Message type - aligned with GraphQL schema
export interface Message {
  id: string;
  content: string;
  senderId: string;
  recipientId: string;
  read: boolean;
  createdAt?: string;
  updatedAt?: string;

  // Relationships from GraphQL schema
  sender?: User;
  recipient?: User;
}

// Conversation type for messaging
export interface Conversation {
  id: string;
  participantId: string;
  lastMessageId?: string;
  unreadCount?: number;

  // Relationships
  participant?: User;
  lastMessage?: Message;
  messages?: Message[];
}

// Authentication types
export interface AuthResponse {
  access_token: string;
  user: User;
}

export interface SignupInput {
  email: string;
  password: string;
  username?: string;
  displayName?: string;
  zone?: string;
}

// Client-side only types (not in GraphQL schema)
export interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}
