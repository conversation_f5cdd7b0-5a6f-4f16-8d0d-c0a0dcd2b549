// This is a shim for the missing asset registry path
// It provides a no-op implementation for asset registration
module.exports = {
  registerAsset: (asset) => {
    // Return a simple asset object with a dummy URI
    return {
      __packager_asset: true,
      fileSystemLocation: asset.fileSystemLocation || '',
      httpServerLocation: asset.httpServerLocation || '',
      width: asset.width || 1,
      height: asset.height || 1,
      scales: asset.scales || [1],
      hash: asset.hash || '',
      name: asset.name || '',
      type: asset.type || '',
      uri: asset.uri || `data:${asset.type || 'image/png'};base64,`
    };
  }
};
