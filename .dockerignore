# flyctl launch added from .gitignore
# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files


# legacy
__next__

# dependencies
node_modules

# Expo
.expo
dist
web-build
expo-env.d.ts

# Native
.kotlin
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env
.env.development
.env.production

# typescript
*.tsbuildinfo

app-example

# flyctl launch added from android\.gitignore
# OSX
#
android\**\.DS_Store

# Android/IntelliJ
#
android\**\build
android\**\.idea
android\**\.gradle
android\**\local.properties
android\**\*.iml
android\**\*.hprof
android\**\.cxx

# Bundle artifacts
android\**\*.jsbundle
fly.toml
