import { useQuery } from '@apollo/client';
import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, FlatList, Image, RefreshControl, StyleSheet } from 'react-native';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { ThemedText } from '../components/ui/ThemedText';
import { ThemedView } from '../components/ui/ThemedView';
import { useZone } from '../contexts/ZoneContext';
import { GET_ZONES } from '../lib/graphql-operations';
import { Zone } from '../types';

export default function ZoneScreen() {
  const { currentZone, setCurrentZoneById } = useZone();
  const [refreshing, setRefreshing] = useState(false);

  // Query zones
  const { data, loading, error, refetch } = useQuery(GET_ZONES);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing zones:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle zone selection
  const handleSelectZone = async (zoneId: string) => {
    try {
      await setCurrentZoneById(zoneId);
      router.back();
    } catch (error) {
      console.error('Error setting zone:', error);
    }
  };

  // Render zone item
  const renderZoneItem = ({ item }: { item: Zone }) => (
    <Card style={styles.zoneCard}>
      {item.image && (
        <Image
          source={{ uri: item.image }}
          style={styles.zoneImage}
          resizeMode="cover"
        />
      )}
      <ThemedView style={styles.zoneContent}>
        <ThemedText variant="subtitle">{item.title}</ThemedText>
        <ThemedView style={styles.zoneFooter}>
          <Button
            title={currentZone?.id === item.id ? 'Current Zone' : 'Select Zone'}
            onPress={() => handleSelectZone(item.id)}
            variant={currentZone?.id === item.id ? 'secondary' : 'primary'}
            size="small"
            disabled={currentZone?.id === item.id}
          />
        </ThemedView>
      </ThemedView>
    </Card>
  );

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading zones...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={data?.zones || []}
        renderItem={renderZoneItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListHeaderComponent={
          <ThemedView style={styles.header}>
            <ThemedText variant="title">Select a Zone</ThemedText>
            <ThemedText style={styles.subtitle}>
              Zones are geographical areas that contain content and communities
            </ThemedText>
          </ThemedView>
        }
        ListEmptyComponent={
          error ? (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.errorText}>
                Error loading zones: {error.message}
              </ThemedText>
              <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
            </ThemedView>
          ) : (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.emptyText}>
                No zones available. Please check back later.
              </ThemedText>
            </ThemedView>
          )
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  listContent: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  subtitle: {
    marginTop: 8,
    marginBottom: 16,
  },
  zoneCard: {
    marginBottom: 16,
    overflow: 'hidden',
  },
  zoneImage: {
    width: '100%',
    height: 120,
  },
  zoneContent: {
    padding: 16,
  },
  zoneDescription: {
    marginTop: 8,
    marginBottom: 16,
  },
  zoneFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
});
