import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { Alert, ScrollView, StyleSheet, Switch } from 'react-native';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { ThemedText } from '../components/ui/ThemedText';
import { ThemedView } from '../components/ui/ThemedView';
import { useAuth } from '../contexts/AuthContext';
import { useTheme, useThemeControls } from '../contexts/ThemeContext';

export default function SettingsScreen() {
  const theme = useTheme();
  const { isDark, toggleTheme } = useThemeControls();
  const { logout } = useAuth();
  
  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };
  
  // Handle confirm logout
  const confirmLogout = () => {
    Alert.alert(
      'Confirm Logout',
      'Are you sure you want to log out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', onPress: handleLogout, style: 'destructive' },
      ]
    );
  };
  
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <ThemedText variant="title" style={styles.title}>Settings</ThemedText>
      
      <Card style={styles.section}>
        <ThemedText variant="subtitle">Appearance</ThemedText>
        <ThemedView style={styles.settingItem}>
          <ThemedView style={styles.settingLabel}>
            <Ionicons name="moon" size={24} color={theme.colors.text} style={styles.icon} />
            <ThemedText>Dark Mode</ThemedText>
          </ThemedView>
          <Switch
            value={isDark}
            onValueChange={toggleTheme}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.card}
          />
        </ThemedView>
      </Card>
      
      <Card style={styles.section}>
        <ThemedText variant="subtitle">Notifications</ThemedText>
        <ThemedView style={styles.settingItem}>
          <ThemedView style={styles.settingLabel}>
            <Ionicons name="notifications" size={24} color={theme.colors.text} style={styles.icon} />
            <ThemedText>Push Notifications</ThemedText>
          </ThemedView>
          <Switch
            value={true}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.card}
          />
        </ThemedView>
        <ThemedView style={styles.settingItem}>
          <ThemedView style={styles.settingLabel}>
            <Ionicons name="mail" size={24} color={theme.colors.text} style={styles.icon} />
            <ThemedText>Email Notifications</ThemedText>
          </ThemedView>
          <Switch
            value={true}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.card}
          />
        </ThemedView>
      </Card>
      
      <Card style={styles.section}>
        <ThemedText variant="subtitle">Privacy</ThemedText>
        <ThemedView style={styles.settingItem}>
          <ThemedView style={styles.settingLabel}>
            <Ionicons name="location" size={24} color={theme.colors.text} style={styles.icon} />
            <ThemedText>Location Services</ThemedText>
          </ThemedView>
          <Switch
            value={true}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.card}
          />
        </ThemedView>
        <ThemedView style={styles.settingItem}>
          <ThemedView style={styles.settingLabel}>
            <Ionicons name="eye-off" size={24} color={theme.colors.text} style={styles.icon} />
            <ThemedText>Private Profile</ThemedText>
          </ThemedView>
          <Switch
            value={false}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.card}
          />
        </ThemedView>
      </Card>
      
      <Card style={styles.section}>
        <ThemedText variant="subtitle">Account</ThemedText>
        <Button
          title="Change Password"
          onPress={() => router.push('/change-password')}
          variant="outline"
          style={styles.accountButton}
        />
        <Button
          title="Delete Account"
          onPress={() => Alert.alert('Delete Account', 'This feature is not implemented yet.')}
          variant="outline"
          style={styles.accountButton}
        />
      </Card>
      
      <Card style={styles.section}>
        <ThemedText variant="subtitle">About</ThemedText>
        <ThemedView style={styles.aboutItem}>
          <ThemedText>Version</ThemedText>
          <ThemedText variant="caption">1.0.0</ThemedText>
        </ThemedView>
        <ThemedView style={styles.aboutItem}>
          <ThemedText>Terms of Service</ThemedText>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.text} />
        </ThemedView>
        <ThemedView style={styles.aboutItem}>
          <ThemedText>Privacy Policy</ThemedText>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.text} />
        </ThemedView>
      </Card>
      
      <Button
        title="Log Out"
        onPress={confirmLogout}
        variant="text"
        style={styles.logoutButton}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  contentContainer: {
    padding: 16,
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  section: {
    marginBottom: 16,
    padding: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  settingLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 12,
  },
  accountButton: {
    marginTop: 12,
  },
  aboutItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  logoutButton: {
    marginTop: 8,
    marginBottom: 24,
  },
});
