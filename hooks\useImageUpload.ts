import { useState, useCallback } from 'react';
import * as ImagePicker from 'expo-image-picker';
import { Platform, Alert } from 'react-native';
import { gql, useMutation } from '@apollo/client';

// GraphQL mutation for uploading an image
const UPLOAD_IMAGE = gql`
  mutation UploadImage($file: Upload!) {
    uploadImage(file: $file) {
      url
    }
  }
`;

interface UseImageUploadOptions {
  maxImages?: number;
  allowMultiple?: boolean;
  quality?: number;
}

export function useImageUpload(options: UseImageUploadOptions = {}) {
  const {
    maxImages = 5,
    allowMultiple = true,
    quality = 0.7,
  } = options;
  
  const [images, setImages] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Upload image mutation
  const [uploadImageMutation] = useMutation(UPLOAD_IMAGE);
  
  // Request permissions
  const requestPermissions = useCallback(async () => {
    if (Platform.OS !== 'web') {
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant camera and photo library permissions to use this feature.',
          [{ text: 'OK' }]
        );
        return false;
      }
    }
    
    return true;
  }, []);
  
  // Take a photo with the camera
  const takePhoto = useCallback(async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) return;
      
      if (images.length >= maxImages) {
        Alert.alert('Maximum Images', `You can only upload up to ${maxImages} images.`);
        return;
      }
      
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newImage = result.assets[0].uri;
        setImages((prevImages) => [...prevImages, newImage]);
      }
    } catch (err) {
      setError('Error taking photo');
      console.error('Error taking photo:', err);
    }
  }, [images, maxImages, quality, requestPermissions]);
  
  // Pick images from the library
  const pickImages = useCallback(async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) return;
      
      if (images.length >= maxImages) {
        Alert.alert('Maximum Images', `You can only upload up to ${maxImages} images.`);
        return;
      }
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality,
        allowsMultipleSelection: allowMultiple,
        selectionLimit: maxImages - images.length,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newImages = result.assets.map((asset) => asset.uri);
        setImages((prevImages) => [...prevImages, ...newImages]);
      }
    } catch (err) {
      setError('Error picking images');
      console.error('Error picking images:', err);
    }
  }, [images, maxImages, quality, allowMultiple, requestPermissions]);
  
  // Remove an image
  const removeImage = useCallback((index: number) => {
    setImages((prevImages) => prevImages.filter((_, i) => i !== index));
  }, []);
  
  // Clear all images
  const clearImages = useCallback(() => {
    setImages([]);
  }, []);
  
  // Upload images to server
  const uploadImages = useCallback(async (): Promise<string[]> => {
    if (images.length === 0) return [];
    
    try {
      setUploading(true);
      setError(null);
      
      const uploadPromises = images.map(async (uri) => {
        // Create a file object from the URI
        const filename = uri.split('/').pop() || 'image.jpg';
        const match = /\.(\w+)$/.exec(filename);
        const type = match ? `image/${match[1]}` : 'image/jpeg';
        
        // For web, the URI is already a blob URL
        // For native, we need to create a blob from the URI
        let file;
        if (Platform.OS === 'web') {
          const response = await fetch(uri);
          const blob = await response.blob();
          file = new File([blob], filename, { type });
        } else {
          // On native, we'll use the URI directly
          // The server will need to handle the URI format
          file = { uri, name: filename, type };
        }
        
        // Upload the file
        const { data } = await uploadImageMutation({
          variables: { file },
        });
        
        return data.uploadImage.url;
      });
      
      const uploadedUrls = await Promise.all(uploadPromises);
      return uploadedUrls;
    } catch (err) {
      setError('Error uploading images');
      console.error('Error uploading images:', err);
      return [];
    } finally {
      setUploading(false);
    }
  }, [images, uploadImageMutation]);
  
  return {
    images,
    uploading,
    error,
    takePhoto,
    pickImages,
    removeImage,
    clearImages,
    uploadImages,
  };
}
