import { Image } from 'expo-image';
import React, { useState } from 'react';
import { ActivityIndicator, ImageStyle, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

// Define the cache options
const CACHE_OPTIONS = {
  // Cache images for 7 days
  cachePolicy: 'memory-disk' as const,
  expiresIn: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
};

// Define the placeholder content
const PLACEHOLDER = {
  // Blurhash placeholder for smooth loading
  placeholder: 'L6PZfSi_.AyE_3t7t7R**0o#DgR4',
};

interface OptimizedImageProps {
  source: string | null | undefined;
  style?: StyleProp<ImageStyle>;
  containerStyle?: StyleProp<ViewStyle>;
  placeholder?: string;
  fallbackSource?: string;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'center';
  priority?: 'low' | 'normal' | 'high';
  showLoadingIndicator?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  testID?: string;
}

export function OptimizedImage({
  source,
  style,
  containerStyle,
  placeholder = PLACEHOLDER.placeholder,
  fallbackSource = 'https://via.placeholder.com/150',
  resizeMode = 'cover',
  priority = 'normal',
  showLoadingIndicator = false,
  onLoad,
  onError,
  testID,
}: OptimizedImageProps) {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Determine the actual source to use
  const actualSource = source || fallbackSource;

  // Handle image load
  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  // Handle image error
  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <Image
        source={actualSource}
        style={[styles.image, style]}
        contentFit={resizeMode}
        placeholder={placeholder}
        transition={200}
        cachePolicy={CACHE_OPTIONS.cachePolicy}
        onLoad={handleLoad}
        onError={handleError}
        priority={priority}
        testID={testID}
      />
      
      {showLoadingIndicator && isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    position: 'relative',
        pointerEvents: 'auto'

  },
  image: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
});
