import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Platform, Share, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemedText } from './ThemedText';

interface ShareButtonProps {
  title: string;
  message: string;
  url: string;
  iconSize?: number;
  showText?: boolean;
  buttonText?: string;
  onShareComplete?: () => void;
  onShareError?: (error: any) => void;
  style?: any;
  textStyle?: any;
}

export function ShareButton({
  title,
  message,
  url,
  iconSize = 24,
  showText = false,
  buttonText = 'Share',
  onShareComplete,
  onShareError,
  style,
  textStyle,
}: ShareButtonProps) {
  const theme = useTheme();

  const handleShare = async () => {
    try {
      // Prepare share content based on platform
      const shareContent = Platform.select({
        ios: {
          title,
          message,
          url,
        },
        android: {
          title,
          message: `${message} ${url}`,
        },
        default: {
          title,
          message: `${message} ${url}`,
        },
      });

      const result = await Share.share(shareContent);

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // Shared with activity type of result.activityType
          console.log(`Shared with activity type: ${result.activityType}`);
        } else {
          // Shared
          console.log('Shared successfully');
        }
        onShareComplete?.();
      } else if (result.action === Share.dismissedAction) {
        // Dismissed
        console.log('Share dismissed');
      }
    } catch (error) {
      console.error('Error sharing:', error);
      onShareError?.(error);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handleShare}
      activeOpacity={0.7}
    >
      <Ionicons
        name="share-outline"
        size={iconSize}
        color={theme.colors.primary}
      />
      {showText && (
        <ThemedText style={[styles.text, textStyle]}>
          {buttonText}
        </ThemedText>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
        pointerEvents: 'auto'

  },
  text: {
    marginLeft: 8,
    fontSize: 14,
  },
});
