import { gql, useMutation } from '@apollo/client';
import { router } from 'expo-router';
import { useState } from 'react';
import { Alert, KeyboardAvoidingView, Platform, ScrollView, StyleSheet } from 'react-native';
import { Button } from '../../components/ui/Button';
import { TextInput } from '../../components/ui/TextInput';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useTheme } from '../../contexts/ThemeContext';

// GraphQL mutation for requesting password reset
const REQUEST_PASSWORD_RESET = gql`
  mutation RequestPasswordReset($email: String!) {
    requestPasswordReset(email: $email)
  }
`;


export default function ResetPasswordScreen() {
  const [email, setEmail] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const theme = useTheme();

  const errorStyles = StyleSheet.create({
    error: {
      color: theme.colors.error,
      marginBottom: 16,
      textAlign: 'center',
    },
  });
  
  // Request password reset mutation
  const [requestReset, { loading, error }] = useMutation(REQUEST_PASSWORD_RESET, {
    onCompleted: () => {
      setSubmitted(true);
    },
    onError: (error) => {
      Alert.alert('Error', error.message);
    },
  });
  
  // Handle submit
  const handleSubmit = () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }
    
    requestReset({
      variables: { email: email.trim() },
    });
  };
  
  // If request has been submitted, show success message
  if (submitted) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText variant="title" style={styles.title}>Check Your Email</ThemedText>
        <ThemedText style={styles.message}>
          If an account exists with the email {email}, we&apos;ve sent instructions on how to reset your password.
        </ThemedText>
        <ThemedText style={styles.note}>
          Please check your spam folder if you don&apos;t see the email in your inbox.
        </ThemedText>
        <Button
          title="Back to Login"
          onPress={() => router.replace('/login')}
          style={styles.button}
        />
      </ThemedView>
    );
  }
  
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <ThemedView style={styles.content}>
          <ThemedText variant="title" style={styles.title}>Reset Password</ThemedText>
          <ThemedText style={styles.subtitle}>
            Enter your email address and we&apos;ll send you instructions to reset your password.
          </ThemedText>
          
          {error && (
            <ThemedText style={errorStyles.error}>{error.message}</ThemedText>
          )}
          
          <TextInput
            label="Email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            style={styles.input}
            placeholder="Enter your email"
          />
          
          <Button
            title={loading ? 'Sending...' : 'Send Reset Instructions'}
            onPress={handleSubmit}
            loading={loading}
            disabled={loading || !email.trim()}
            fullWidth
            style={styles.button}
          />
          
          <Button
            title="Back to Login"
            onPress={() => router.back()}
            variant="text"
            style={styles.backButton}
          />
        </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 24,
  },
  input: {
    marginBottom: 16,
  },
  button: {
    marginTop: 8,
  },
  backButton: {
    marginTop: 16,
    alignSelf: 'center',
  },
  message: {
    textAlign: 'center',
    marginBottom: 16,
  },
  note: {
    textAlign: 'center',
    marginBottom: 24,
    fontStyle: 'italic',
  },
});
