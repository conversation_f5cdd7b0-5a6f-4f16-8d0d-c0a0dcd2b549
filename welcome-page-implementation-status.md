# Welcome Page Implementation Status

## Current Issue
The app is currently opening with a 400 Response error from GraphQL and displaying "Downtown" and a "New Post" button, which does not match the legacy app's behavior. The legacy app opens on the `/page/[welcomepage id]` route where the welcome page ID comes from the current zone.

## Legacy App Behavior
In the legacy app:
1. The app starts by loading the current zone from the server
2. The zone contains a `welcomePage` or `homePage` property that references a page ID
3. The app redirects to the `/page/[id]` route with this ID
4. The PagePage template renders the welcome page content

## Current Implementation Status

### What's Implemented
- ✅ Basic zone context with mock data in `contexts/ZoneContext.tsx`
- ✅ Basic page component in `app/page/[id].tsx`
- ✅ GraphQL query for fetching page content in `lib/graphql-operations.ts`

### What's Missing
- ❌ The zone data doesn't include a `welcomePage` ID
- ❌ The app entry point (`app/index.js`) redirects to `/(tabs)` instead of `/page/[welcomepage id]`
- ❌ There's no proper handling of HTML content in the page component
- ❌ The GraphQL client isn't properly configured to handle page queries

## Implementation Plan

1. **Update ZoneContext**:
   - Add `welcomePage` IDs to the mock zone data
   - Ensure the zone context properly loads and uses the welcomePage ID

2. **Update App Entry Point**:
   - Modify `app/index.js` to redirect to the welcome page of the current zone
   - Add fallback to tabs if no welcome page is defined

3. **Enhance Page Component**:
   - Improve HTML content rendering
   - Add support for zone context

4. **Fix GraphQL Integration**:
   - Add proper error handling for GraphQL queries
   - Implement mock data for development

## Changes Made

1. **Added `welcomePage` IDs to mock zone data**:
   ```typescript
   {
     id: '1',
     name: 'Downtown',
     description: '...',
     welcomePage: 'welcome-downtown',
     // other properties
   }
   ```

2. **Updated app entry point to redirect to welcome page**:
   ```typescript
   // If the current zone has a welcome page, redirect to it
   if (currentZone?.welcomePage) {
     return <Redirect href={`/page/${currentZone.welcomePage}`} />;
   } else {
     // Otherwise, redirect to the tabs
     return <Redirect href="/(tabs)" />;
   }
   ```

3. **Created a mock page provider for development**:
   ```typescript
   // Mock page data for development
   const MOCK_PAGES = {
     'welcome-downtown': {
       id: 'welcome-downtown',
       title: 'Welcome to Downtown',
       content: `
         <h1>Welcome to Downtown</h1>
         <p>Downtown is the heart of the city with shops, restaurants, and entertainment.</p>
         <p>Explore the vibrant streets, visit local businesses, and enjoy the urban atmosphere.</p>
         <p>Check out the latest events and offers in the area!</p>
       `,
       // other properties
     },
     // other mock pages
   };
   ```

4. **Updated Apollo client to use mock page data**:
   ```typescript
   // Create mock page link
   const mockPageLink = createMockPageLink();

   // Add to Apollo client link chain
   const client = new ApolloClient({
     link: from([debugLink, errorLink, mockPageLink, authLink, splitLink]),
     // other configuration
   });
   ```

## Next Steps

1. **Implement HTML content rendering**:
   - Add a proper HTML renderer for page content (e.g., `react-native-render-html`)

2. **Add edit functionality for owners/admins**:
   - Implement edit mode for pages when the user is an owner or admin

3. **Enhance zone loading**:
   - Replace mock data with actual API calls to load zone data
   - Handle zone switching properly

4. **Improve error handling**:
   - Add better error messages for GraphQL errors
   - Implement retry logic for failed queries

By implementing these changes, the app will correctly open on the welcome page from the current zone, matching the behavior of the legacy app.
