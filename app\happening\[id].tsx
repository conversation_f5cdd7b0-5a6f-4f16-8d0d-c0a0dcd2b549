import { Happening } from '@/models';
import { useMutation, useQuery } from '@apollo/client';
import { useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, Dimensions, Image, RefreshControl, ScrollView, View } from 'react-native';
import RenderHtml from 'react-native-render-html';
import { CommentsList } from '../../components/common/CommentsList';
import UniversalMapWrapper from '../../components/map/UniversalMapWrapper';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { ADD_COMMENT, GET_HAPPENING } from '../../lib/graphql-operations';
import { getShareableUrl, openDirections, shareContent } from '../../utils/sharing';

// Import shared comment utility
import { sharedStyles } from '../../styles/sharedStyles';
import { handleAddComment } from '../../utils/commentUtils';

const { width } = Dimensions.get('window');

export default function HappeningDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [refreshing, setRefreshing] = useState(false);
  const [comment, setComment] = useState('');

  // Query happening details
  const { data, loading, error, refetch } = useQuery<{ happening: Happening }>(GET_HAPPENING, {
    variables: { id },
  });

  // Add comment mutation
  const [addComment] = useMutation(ADD_COMMENT);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing happening:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Ensure happening is defined and add type safety
  const happening = data?.happening; // Ensure happening is properly typed

  // Handle adding a comment
  const handleAddCommentWrapper = async () => {
    await handleAddComment(addComment, {
      linkedObjectId: id,
      objectType: 'happening',
      body: comment,
    }, (cache, newComment) => {
      const existingHappening = cache.readQuery({ query: GET_HAPPENING, variables: { id } });
      if (existingHappening?.happening) {
        cache.writeQuery({
          query: GET_HAPPENING,
          variables: { id },
          data: {
            happening: {
              ...existingHappening.happening,
              comments: [...(existingHappening.happening.comments || []), newComment],
            },
          },
        });
      }
    });
  };

  // Handle sharing
  const handleShare = async () => {
    if (!happening) return;

    const shareableUrl = getShareableUrl('happening', happening.id);
    await shareContent(
      happening.title,
      `Check out this event: ${happening.title}`,
      shareableUrl
    );
  };

  // Handle getting directions
  const handleGetDirections = () => {
    if (!happening?.placeDetails?.loc) return;

    openDirections(
      happening.placeDetails.name,
      happening.placeDetails.loc.lat,
      happening.placeDetails.loc.lng
    );
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Format time
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={sharedStyles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={sharedStyles.loadingText}>Loading event...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={sharedStyles.errorContainer}>
        <ThemedText style={sharedStyles.errorText}>
          Error loading event: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={onRefresh} style={sharedStyles.retryButton} />
      </ThemedView>
    );
  }

  if (!happening) {
    return (
      <ThemedView style={sharedStyles.errorContainer}>
        <ThemedText style={sharedStyles.errorText}>Event not found</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ScrollView
      style={sharedStyles.container}
      contentContainerStyle={sharedStyles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {happening.avatar && (
        <Image
          source={{ uri: happening.avatarUrl() }}
          style={sharedStyles.avatar}
          resizeMode="cover"
        />
      )}

      <ThemedView style={sharedStyles.header}>
        <ThemedText variant="title">{happening.title}</ThemedText>
        <ThemedText variant="caption" style={sharedStyles.date}>
          {happening.when?.start ? formatDate(happening.when.start) : 'Unknown date'}
          {happening.when?.start && happening.when?.end && ` - ${formatDate(happening.when.end)}`}
          {happening.when?.start && ` at ${formatTime(happening.when.start)}`}
          {happening.when?.end && ` - ${formatTime(happening.when.end)}`}
        </ThemedText>
      </ThemedView>

      {happening.desc && (
        <Card style={sharedStyles.section}>
          <ThemedText variant="subtitle">About</ThemedText>
          <RenderHtml
            contentWidth={width}
            source={{ html: happening.desc }}
          />
        </Card>
      )}

      {happening.placeDetails && (
        <Card style={sharedStyles.section}>
          <ThemedText variant="subtitle">Location</ThemedText>
          <ThemedText style={sharedStyles.placeName}>{happening.placeDetails.name}</ThemedText>
          {happening.placeDetails.fullLocation?.fullAddress && (
            <ThemedText style={sharedStyles.address}>{happening.placeDetails.fullLocation?.fullAddress}</ThemedText>
          )}
          {happening.placeDetails.loc && happening.placeDetails.loc.lat && happening.placeDetails.loc.lng && (
            <View style={sharedStyles.mapContainer}>
              <UniversalMapWrapper
                selectedCategory={null}
                markerPosition={[happening.placeDetails.loc.lat, happening.placeDetails.loc.lng]}
              />
            </View>
          )}
        </Card>
      )}

      <ThemedView style={sharedStyles.actionsContainer}>
        <Button
          title="Share"
          variant="outline"
          style={sharedStyles.actionButton}
          onPress={handleShare}
        />
        {happening.placeDetails && happening.placeDetails.loc && (
          <Button
            title="Directions"
            variant="outline"
            style={sharedStyles.actionButton}
            onPress={handleGetDirections}
          />
        )}
      </ThemedView>

      <Card style={sharedStyles.section}>
        <ThemedText variant="subtitle">Comments ({happening.comments?.length || 0})</ThemedText>
        <CommentsList
          comments={happening.comments || []}
          onAddComment={handleAddCommentWrapper}
          comment={comment}
          setComment={setComment}
        />
      </Card>
    </ScrollView>
  );
}

