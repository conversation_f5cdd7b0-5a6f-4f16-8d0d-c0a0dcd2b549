import { FlashList } from '@shopify/flash-list';
import React from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { VirtualizedListProps } from '../../hooks/useInfiniteScroll';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface Props<T> extends VirtualizedListProps<T> {
  useFlashList?: boolean;
  emptyText?: string;
  loadingText?: string;
  errorText?: string;
  error?: Error | null;
  loading?: boolean;
}

export function VirtualizedList<T>({
  data,
  renderItem,
  keyExtractor,
  onEndReached,
  onEndReachedThreshold = 0.5,
  estimatedItemSize = 100,
  numColumns = 1,
  ListEmptyComponent,
  ListHeaderComponent,
  ListFooterComponent,
  contentContainerStyle,
  refreshing = false,
  onRefresh,
  useFlashList = true,
  emptyText = 'No items found',
  loadingText = 'Loading...',
  errorText = 'Error loading items',
  error = null,
  loading = false,
}: Props<T>) {
  const theme = useTheme();

  // Default empty component
  const defaultEmptyComponent = (
    <ThemedView style={styles.emptyContainer}>
      <ThemedText style={styles.emptyText}>{emptyText}</ThemedText>
    </ThemedView>
  );

  // Loading footer
  const loadingFooter = loading && !refreshing ? (
    <View style={styles.loadingFooter}>
      <ActivityIndicator size="small" color={theme.colors.primary} />
      <ThemedText style={styles.loadingText}>{loadingText}</ThemedText>
    </View>
  ) : null;

  // Error component
  const errorComponent = error ? (
    <ThemedView style={styles.errorContainer}>
      <ThemedText style={styles.errorText}>
        {errorText}: {error.message}
      </ThemedText>
    </ThemedView>
  ) : null;

  // Combine footer components
  const combinedFooter = (
    <>
      {loadingFooter}
      {errorComponent}
      {ListFooterComponent}
    </>
  );

  // Use FlashList if specified and available
  if (useFlashList) {
    return (
      <FlashList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        onEndReached={onEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        estimatedItemSize={estimatedItemSize}
        numColumns={numColumns}
        ListEmptyComponent={ListEmptyComponent || defaultEmptyComponent}
        ListHeaderComponent={ListHeaderComponent}
        ListFooterComponent={combinedFooter}
        contentContainerStyle={contentContainerStyle}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          ) : undefined
        }
      />
    );
  }

  // Fallback to FlatList
  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      numColumns={numColumns}
      ListEmptyComponent={ListEmptyComponent || defaultEmptyComponent}
      ListHeaderComponent={ListHeaderComponent}
      ListFooterComponent={combinedFooter}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}

      contentContainerStyle={contentContainerStyle}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        ) : undefined
      }
    />
  );
}

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 10,
  },
  loadingFooter: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  loadingText: {
    marginLeft: 10,
  },
  errorContainer: {
    padding: 20,
    backgroundColor: 'rgba(255, 0, 0, 0.05)',
    borderRadius: 5,
    margin: 10,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
  },
});
