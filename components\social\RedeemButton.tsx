import { useMutation } from '@apollo/client';
import React, { useState } from 'react';
import { Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { REDEEM_OFFER } from '../../lib/graphql-operations';
import { Button } from '../ui/Button';
import { RedeemModal } from './RedeemModal';

interface RedeemButtonProps {
  offerId: string;
  isRedeemed?: boolean;
  redeemCount?: number;
  maxRedeems?: number;
  isValid?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: any;
}

export function RedeemButton({
  offerId,
  isRedeemed = false,
  redeemCount = 0,
  maxRedeems,
  isValid = true,
  size = 'small',
  style
}: RedeemButtonProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const [redeem, { loading }] = useMutation(REDEEM_OFFER);
  const theme = useTheme();

  const handleRedeem = async (comment?: string) => {
    try {
      await redeem({
        variables: {
          offerId,
          comment
        },
        optimisticResponse: {
          __typename: 'Mutation',
          createCheck: {
            __typename: 'Check',
            id: 'temp-id-' + Date.now(),
            linkedObjectId: offerId,
            objectType: 'offer',
            comment: comment || null,
            type: 'redeem',
            createdAt: new Date().toISOString(),
          },
        },
        update: (cache, { data }) => {
          // Update the cache to reflect the new redemption count
          console.log('Redemption successful:', data);
        }
      });
      
      // Show success message
      Alert.alert('Success', 'Offer redeemed successfully!');
    } catch (error) {
      console.error('Error redeeming offer:', error);
      Alert.alert('Error', 'Failed to redeem offer. Please try again.');
    } finally {
      setModalVisible(false);
    }
  };

  // Get icon based on redemption status
  const getIcon = () => {
    if (isRedeemed) {
      return 'checkmark-circle';
    }
    return 'ticket-outline';
  };

  // Get button title based on redemption status
  const getTitle = () => {
    if (isRedeemed) {
      return `Redeemed (${redeemCount})`;
    }
    
    if (maxRedeems) {
      return `Redeem (${redeemCount}/${maxRedeems})`;
    }
    
    return `Redeem (${redeemCount})`;
  };

  // Check if the offer is fully redeemed
  const isFullyRedeemed = () => {
    if (!maxRedeems) return false;
    return redeemCount >= maxRedeems;
  };

  // Determine if the button should be disabled
  const isDisabled = () => {
    return isRedeemed || !isValid || isFullyRedeemed();
  };

  return (
    <>
      <Button
        title={getTitle()}
        variant={isRedeemed ? 'primary' : 'outline'}
        size={size}
        onPress={() => setModalVisible(true)}
        loading={loading}
        icon={getIcon()}
        disabled={isDisabled()}
        style={style}
      />

      <RedeemModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onRedeem={handleRedeem}
      />
    </>
  );
}
