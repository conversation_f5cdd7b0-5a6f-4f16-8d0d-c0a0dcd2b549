import { useQuery } from '@apollo/client';
import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';
import { Avatar } from '../../components/ui/Avatar';
import { Button } from '../../components/ui/Button';
import { NotificationBadge } from '../../components/ui/NotificationBadge';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import useUnreadMessages from '../../hooks/useUnreadMessages';
import { GET_CONVERSATIONS } from '../../lib/graphql-operations';

// Conversation type
interface Conversation {
  id: string;
  lastMessage: {
    id: string;
    content: string;
    createdAt: string;
    read: boolean;
  };
  participant: {
    id: string;
    emails: {
      address: string;
      verified: boolean;
    }[];
    userProfile: {
      id: string;
      firstName: string;
      lastName: string;
      avatar: string;
    };
    isOnline: boolean;
    lastOnline?: string;
  };
  unreadCount: number;
}

export default function MessagesScreen() {
  const [refreshing, setRefreshing] = useState(false);

  // Query conversations
  const { data, loading, error, refetch } = useQuery(GET_CONVERSATIONS);

  // Use our unread messages hook for real-time updates
  const {
    conversationUnreadCounts,
    totalUnreadCount,
    markConversationAsRead,
    refetch: refetchUnread
  } = useUnreadMessages();

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([refetch(), refetchUnread()]);
    } catch (error) {
      console.error('Error refreshing conversations:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();

    // If today, show time
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // If this year, show month and day
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }

    // Otherwise, show full date
    return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
  };

  // Handle conversation press
  const handleConversationPress = (conversation: Conversation) => {
    // Mark the conversation as read when navigating to it
    markConversationAsRead(conversation.id);
    router.push(`/messages/${conversation.id}`);
  };

  // Render conversation item
  const renderConversationItem = ({ item }: { item: Conversation }) => {
    // Get the unread count from our hook (which has real-time updates)
    // or fall back to the count from the query
    const unreadCount = conversationUnreadCounts[item.id] ?? item.unreadCount;

    return (
      <TouchableOpacity
        style={styles.conversationItem}
        onPress={() => handleConversationPress(item)}
      >
        <Avatar
          source={item.participant.userProfile?.avatar}
          name={item.participant.userProfile ?
          `${item.participant.userProfile.firstName || ''} ${item.participant.userProfile.lastName || ''}`.trim() :
          item?.participant.emails?.[0]?.address?.split('@')[0]
        }
          online={item.participant.isOnline}
          size="medium"
        />
        <ThemedView style={styles.conversationContent}>
          <ThemedView style={styles.conversationHeader}>
            <ThemedText variant="subtitle">{item.participant.userProfile ?
          `${item.participant.userProfile.firstName || ''} ${item.participant.userProfile.lastName || ''}`.trim() :
          item?.participant.emails?.[0]?.address?.split('@')[0]
        }</ThemedText>
            <ThemedText variant="caption">
              {item.lastMessage ? formatDate(item.lastMessage.createdAt) : ''}
            </ThemedText>
          </ThemedView>
          <ThemedView style={styles.conversationFooter}>
            <ThemedText
              numberOfLines={1}
              style={[
                styles.lastMessage,
                unreadCount > 0 && styles.unreadMessage,
              ]}
            >
              {item.lastMessage ? item.lastMessage.content : 'No messages yet'}
            </ThemedText>
            <NotificationBadge count={unreadCount} size="medium" />
          </ThemedView>
        </ThemedView>
      </TouchableOpacity>
    );
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading conversations...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={data?.conversations || []}
        renderItem={renderConversationItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        keyboardShouldPersistTaps="handled"
        scrollEnabled={true}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListHeaderComponent={
          <ThemedView style={styles.header}>
            <ThemedView style={styles.headerTitleContainer}>
              <ThemedText variant="title">Messages</ThemedText>
              {totalUnreadCount > 0 && (
                <NotificationBadge
                  count={totalUnreadCount}
                  size="small"
                  style={styles.headerBadge}
                />
              )}
            </ThemedView>
            <Button
              title="New Message"
              onPress={() => router.push('/messages/new')}
              size="small"
            />
          </ThemedView>
        }
        ListEmptyComponent={
          error ? (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.errorText}>
                Error loading conversations: {error.message}
              </ThemedText>
              <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
            </ThemedView>
          ) : (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.emptyText}>
                No conversations yet. Start a new message to connect with others!
              </ThemedText>
              <Button
                title="Start a Conversation"
                onPress={() => router.push('/messages/new')}
                style={styles.startButton}
              />
            </ThemedView>
          )
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  listContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerBadge: {
    marginLeft: 8,
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  conversationContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    flex: 1,
    marginRight: 8,
  },
  unreadMessage: {
    fontWeight: 'bold',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  startButton: {
    marginTop: 8,
  },
});
