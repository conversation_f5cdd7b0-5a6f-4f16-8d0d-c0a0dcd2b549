import { useMutation, useQuery } from '@apollo/client';
import React, { useState } from 'react';
import { Alert } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { REDEEM_OFFER, GET_CHECK_INS } from '../../lib/graphql-operations';
import { Button } from '../ui/Button';
import { QRCodeGenerator } from '../common/QRCodeGenerator';
import { QRCodeScanner } from '../common/QRCodeScanner';

interface RedeemOfferButtonProps {
  offer: {
    id: string;
    title: string;
    placeId?: string;
    place?: {
      id: string;
      name: string;
    };
    maxRedeems?: number;
    redeems?: number;
  };
  isPlaceOwner: boolean;
  isPlaceMember: boolean;
  userRedeemCount: number;
  style?: any;
}

export function RedeemOfferButton({
  offer,
  isPlaceOwner,
  isPlaceMember,
  userRedeemCount,
  style
}: RedeemOfferButtonProps) {
  const [showQRCode, setShowQRCode] = useState(false);
  const [showScanner, setShowScanner] = useState(false);
  const { user } = useAuth();
  const [redeem, { loading }] = useMutation(REDEEM_OFFER);

  // Check if the user has reached the maximum number of redemptions
  const hasReachedMaxRedeems = offer.maxRedeems ? userRedeemCount >= offer.maxRedeems : false;

  // Check if the offer is fully redeemed
  const isFullyRedeemed = offer.maxRedeems ? offer.redeems >= offer.maxRedeems : false;

  // Handle redeem action
  const handleRedeem = async (scannedData?: string) => {
    try {
      // Verify the scanned data matches the offer ID
      if (scannedData && !scannedData.includes(offer.id)) {
        Alert.alert('Invalid QR Code', 'The scanned QR code does not match this offer.');
        return;
      }

      await redeem({
        variables: {
          offerId: offer.id,
          comment: `Redeemed by ${user?.userProfile?.firstName || 'user'}`
        },
        update: (cache, { data }) => {
          // Update the cache to reflect the new redemption
          console.log('Redemption successful:', data);
        }
      });
      
      // Show success message
      Alert.alert('Success', 'Offer redeemed successfully!');
      setShowScanner(false);
    } catch (error) {
      console.error('Error redeeming offer:', error);
      Alert.alert('Error', 'Failed to redeem offer. Please try again.');
    }
  };

  // If the user is a place owner or member, show the scanner button
  if (isPlaceOwner || isPlaceMember) {
    return (
      <>
        <Button
          title="Redeem"
          variant="primary"
          onPress={() => setShowScanner(true)}
          disabled={loading || isFullyRedeemed}
          style={style}
        />

        {/* QR Code Scanner Modal */}
        <QRCodeScanner
          isVisible={showScanner}
          onClose={() => setShowScanner(false)}
          onScan={handleRedeem}
        />
      </>
    );
  }

  // For regular users, show the QR code button if they haven't reached max redeems
  return (
    <>
      <Button
        title={hasReachedMaxRedeems ? `Redeemed (${userRedeemCount}/${offer.maxRedeems})` : "Redeem"}
        variant={hasReachedMaxRedeems ? "secondary" : "primary"}
        onPress={() => setShowQRCode(true)}
        disabled={hasReachedMaxRedeems || isFullyRedeemed || loading}
        style={style}
      />

      {/* QR Code Modal */}
      {showQRCode && (
        <QRCodeGenerator
          deepLink={{
            type: 'offer',
            id: offer.id,
            action: 'redeem'
          }}
          title={`Redeem: ${offer.title}`}
          description={`Show this QR code to ${offer.place?.name || 'the business'} to redeem this offer.`}
          showShareButton={true}
          onPress={() => setShowQRCode(false)}
        />
      )}
    </>
  );
}
