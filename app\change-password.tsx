import { gql, useMutation } from '@apollo/client';
import { router } from 'expo-router';
import { useState } from 'react';
import { Alert, KeyboardAvoidingView, Platform, ScrollView, StyleSheet } from 'react-native';
import { Button } from '../components/ui/Button';
import { TextInput } from '../components/ui/TextInput';
import { ThemedText } from '../components/ui/ThemedText';
import { ThemedView } from '../components/ui/ThemedView';
import { useTheme } from '../contexts/ThemeContext';

// GraphQL mutation for changing password
const CHANGE_PASSWORD = gql`
  mutation ChangePassword($currentPassword: String!, $newPassword: String!) {
    changePassword(currentPassword: $currentPassword, newPassword: $newPassword)
  }
`;

export default function ChangePasswordScreen() {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [validationError, setValidationError] = useState<string | null>(null);
  
  // Change password mutation
  const [changePassword, { loading, error }] = useMutation(CHANGE_PASSWORD, {
    onCompleted: () => {
      Alert.alert(
        'Success',
        'Your password has been changed successfully.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    },
    onError: (error) => {
      Alert.alert('Error', error.message);
    },
  });
  
  // Validate password
  const validatePassword = () => {
    if (!currentPassword) {
      setValidationError('Current password is required');
      return false;
    }
    
    if (!newPassword) {
      setValidationError('New password is required');
      return false;
    }
    
    if (newPassword.length < 8) {
      setValidationError('New password must be at least 8 characters long');
      return false;
    }
    
    if (newPassword !== confirmPassword) {
      setValidationError('New passwords do not match');
      return false;
    }
    
    setValidationError(null);
    return true;
  };
  
  // Handle submit
  const handleSubmit = () => {
    if (!validatePassword()) {
      return;
    }
    
    changePassword({
      variables: {
        currentPassword,
        newPassword,
      },
    });
  };
  
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
          pointerEvents: 'auto'

    },
    scrollContent: {
      flexGrow: 1,
    },
    content: {
      flex: 1,
      padding: 20,
      justifyContent: 'center',
    },
    title: {
      marginBottom: 24,
      textAlign: 'center',
    },
    input: {
      marginBottom: 16,
    },
    passwordRequirements: {
      marginBottom: 24,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    cancelButton: {
      flex: 1,
      marginRight: 8,
    },
    submitButton: {
      flex: 1,
      marginLeft: 8,
    },
    error: {
      color: theme.colors.error,
    },
  });
  
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <ThemedView style={styles.content}>
          <ThemedText variant="title" style={styles.title}>Change Password</ThemedText>
          
          {validationError && (
            <ThemedText style={styles.error}>{validationError}</ThemedText>
          )}
          
          {error && (
            <ThemedText style={styles.error}>{error.message}</ThemedText>
          )}
          
          <TextInput
            label="Current Password"
            value={currentPassword}
            onChangeText={setCurrentPassword}
            secureTextEntry
            style={styles.input}
            placeholder="Enter your current password"
          />
          
          <TextInput
            label="New Password"
            value={newPassword}
            onChangeText={setNewPassword}
            secureTextEntry
            style={styles.input}
            placeholder="Enter your new password"
          />
          
          <TextInput
            label="Confirm New Password"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            style={styles.input}
            placeholder="Confirm your new password"
          />
          
          <ThemedText variant="caption" style={styles.passwordRequirements}>
            Password must be at least 8 characters long and include a mix of letters, numbers, and special characters.
          </ThemedText>
          
          <ThemedView style={styles.buttonContainer}>
            <Button
              title="Cancel"
              onPress={() => router.back()}
              variant="outline"
              style={styles.cancelButton}
            />
            <Button
              title={loading ? 'Changing...' : 'Change Password'}
              onPress={handleSubmit}
              loading={loading}
              disabled={loading}
              style={styles.submitButton}
            />
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
