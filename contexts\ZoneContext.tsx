import { gql, useQuery } from '@apollo/client';
import * as Location from 'expo-location';
import * as SecureStore from 'expo-secure-store';
import { ReactNode, createContext, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { Zone } from '../types';

// Helper function to safely access localStorage
const safeLocalStorage = {
  getItem: (key: string): string | null => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        return window.localStorage.getItem(key);
      }
      return null;
    } catch (error) {
      console.error('Error accessing localStorage.getItem:', error);
      return null;
    }
  },
  setItem: (key: string, value: string): void => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem(key, value);
      }
    } catch (error) {
      console.error('Error accessing localStorage.setItem:', error);
    }
  },
  removeItem: (key: string): void => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error accessing localStorage.removeItem:', error);
    }
  }
};

// Get the initial zone ID from environment variables
const INITIAL_ZONE_ID = process.env.EXPO_PUBLIC_INITIAL_ZONE || '5fac5dc69a25e8cfbe217c6b';

// Storage keys
const CURRENT_ZONE_KEY = 'current_zone_id';

// GraphQL query for all zones - simplified to just get what we need
const GET_ZONES = gql`
  query GetZones {
    zones {
      id
      title
      center {
        lat
        lng
      }
      welcomePage
      rootPage
    }
  }
`;

// Types
interface ZoneContextType {
  zones: Zone[];
  currentZone: Zone | null;
  setCurrentZoneById: (id: string) => Promise<void>;
  loading: boolean;
  error: string | null;
  initialized: boolean;
}

// Create context
const ZoneContext = createContext<ZoneContextType | undefined>(undefined);

// Provider component
export function ZoneProvider({ children }: { children: ReactNode }) {
  const [zones, setZones] = useState<Zone[]>([]);
  const [currentZone, setCurrentZone] = useState<Zone | null>(null);
  const [currentZoneId, setCurrentZoneId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [initialized, setInitialized] = useState(false);

  // Get user's current location
  const getUserLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        console.log('Location permission denied');
        return null;
      }

      const position = await Location.getCurrentPositionAsync({});
      return {
        lat: position.coords.latitude,
        lng: position.coords.longitude
      };
    } catch (err) {
      console.error('Error getting location:', err);
      return null;
    }
  };

  // Load current zone ID from storage
  const loadCurrentZoneId = async (): Promise<string | null> => {
    try {
      if (Platform.OS === 'web') {
        return safeLocalStorage.getItem(CURRENT_ZONE_KEY);
      } else {
        return await SecureStore.getItemAsync(CURRENT_ZONE_KEY);
      }
    } catch (err) {
      console.error('Error loading zone ID:', err);
      return null;
    }
  };

  // Save current zone ID
  const saveCurrentZoneId = async (id: string) => {
    try {
      if (Platform.OS === 'web') {
        safeLocalStorage.setItem(CURRENT_ZONE_KEY, id);
      } else {
        await SecureStore.setItemAsync(CURRENT_ZONE_KEY, id);
      }
    } catch (err) {
      console.error('Error saving zone ID:', err);
    }
  };

  // Set current zone by ID
  const setCurrentZoneById = async (id: string) => {
    setCurrentZoneId(id);
    await saveCurrentZoneId(id);
    
    // Find the zone in the zones list
    const zone = zones.find(z => z.id === id);
    if (zone) {
      setCurrentZone(zone);
    }
  };

  // Load saved zone ID on mount
  useEffect(() => {
    const loadZone = async () => {
      try {
        // Try to load saved zone ID from storage
        let savedId = await loadCurrentZoneId();

        // Clear any previously saved invalid zone ID
        if (savedId === "1") {
          console.warn('Found invalid saved zone ID "1", clearing it');
          if (Platform.OS === 'web') {
            safeLocalStorage.removeItem(CURRENT_ZONE_KEY);
          } else {
            await SecureStore.deleteItemAsync(CURRENT_ZONE_KEY);
          }
          savedId = null;
        }

        if (savedId) {
          console.log('Found saved zone ID:', savedId);
          setCurrentZoneId(savedId);
        } else {
          // If no saved zone, use the initial zone from environment
          console.log('No saved zone ID, using default:', INITIAL_ZONE_ID);
          setCurrentZoneId(INITIAL_ZONE_ID);
          await saveCurrentZoneId(INITIAL_ZONE_ID);
        }
      } catch (err) {
        console.error('Error loading zone:', err);
        setError('Failed to load zone. Please try again.');
        setCurrentZoneId(INITIAL_ZONE_ID);
      }
    };

    loadZone();
  }, []);

  // Fetch zones
  const { loading: zonesLoading, error: zonesError } = useQuery(GET_ZONES, {
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      if (data?.zones && data.zones.length > 0) {
        console.log('Loaded zones:', data.zones.length);
        console.log('Available zone IDs:', data.zones.map((z: Zone) => z.id).join(', '));
        setZones(data.zones);
        
        // If we have a currentZoneId, try to find it in the zones list
        if (currentZoneId) {
          const zone = data.zones.find((z: Zone) => z.id === currentZoneId);
          if (zone) {
            console.log('Found current zone in zones list:', zone.id);
            setCurrentZone(zone);
            setInitialized(true);
            setError(null);
          } else {
            console.log('Current zone ID not found in zones list, using first zone');
            setCurrentZone(data.zones[0]);
            setCurrentZoneId(data.zones[0].id);
            saveCurrentZoneId(data.zones[0].id);
            setInitialized(true);
            setError(null);
          }
        } else {
          // If no currentZoneId, use the first zone
          console.log('No current zone ID, using first zone:', data.zones[0].id);
          setCurrentZone(data.zones[0]);
          setCurrentZoneId(data.zones[0].id);
          saveCurrentZoneId(data.zones[0].id);
          setInitialized(true);
          setError(null);
        }
      } else {
        console.error('No zones returned from API');
        setError('Failed to load zones. No zones available.');
        setInitialized(true);
      }
    },
    onError: (error) => {
      console.error('Error fetching zones:', error);
      setError('Failed to load zones. Please try again later.');
      setInitialized(true);
    }
  });

  // Initialize zone based on user location
  useEffect(() => {
    const initializeZone = async () => {
      try {
        // Try to get user's location
        const location = await getUserLocation();
        if (location) {
          console.log('Got user location:', location);
          
          // If we have zones and a current zone, we're good
          if (zones.length > 0 && currentZone) {
            console.log('Already have zones and current zone');
            return;
          }
        }
      } catch (err) {
        console.error('Error initializing zone:', err);
      }
    };

    initializeZone();
  }, [zones, currentZone]);

  // Context value
  const value = {
    zones,
    currentZone,
    setCurrentZoneById,
    loading: zonesLoading,
    error,
    initialized
  };

  return (
    <ZoneContext.Provider value={value}>
      {children}
    </ZoneContext.Provider>
  );
}

// Hook for using the zone context
export function useZone() {
  const context = useContext(ZoneContext);
  if (context === undefined) {
    throw new Error('useZone must be used within a ZoneProvider');
  }
  return context;
}
