import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { useAuth } from '../../contexts/AuthContext';
import { ThemedText } from '../ui/ThemedText';

interface LikeButtonProps {
  objectId: string;
  objectType: string;
  initialLikeState?: boolean;
  likesCount?: number;
  onToggle?: (isLiked: boolean) => void;
  style?: any;
}

export function LikeButton({
  objectId,
  objectType,
  initialLikeState = false,
  likesCount = 0,
  onToggle,
  style
}: LikeButtonProps) {
  const { user } = useAuth();
  const [isLiked, setIsLiked] = useState(initialLikeState);
  const [count, setCount] = useState(likesCount);

  const handlePress = () => {
    if (!user) return;
    
    const newLikeState = !isLiked;
    setIsLiked(newLikeState);
    setCount(prevCount => newLikeState ? prevCount + 1 : Math.max(0, prevCount - 1));
    
    if (onToggle) {
      onToggle(newLikeState);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      disabled={!user}
    >
      <View style={styles.content}>
        <Ionicons
          name={isLiked ? 'heart' : 'heart-outline'}
          size={20}
          color={isLiked ? '#F44336' : '#666'}
        />
        <ThemedText style={[styles.text, isLiked && styles.likedText]}>
          {isLiked ? 'Liked' : 'Like'} {count > 0 && `(${count})`}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 8,
    marginBottom: 8,
        pointerEvents: 'auto'

  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    marginLeft: 4,
    fontSize: 14,
  },
  likedText: {
    color: '#F44336',
  },
});
