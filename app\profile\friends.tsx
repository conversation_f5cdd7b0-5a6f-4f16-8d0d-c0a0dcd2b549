import { useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { Avatar } from '../../components/ui/Avatar';
import { Button } from '../../components/ui/Button';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { GET_CURRENT_USER_WITH_FRIENDS, GET_USER_FRIENDS } from '../../lib/graphql-operations';
import { formatRelativeTime } from '../../utils/date';

export default function FriendsScreen() {
  const { currentUser } = useAuth();
  const theme = useTheme();
  const params = useLocalSearchParams();
  const userId = params.userId as string || currentUser?.id;
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // Query user's friends
  const { data, loading, error, refetch } = useQuery(
    userId === currentUser?.id ? GET_CURRENT_USER_WITH_FRIENDS : GET_USER_FRIENDS,
    {
      variables: {
        userId,
        limit: 50,
        skip: 0,
      },
      skip: !userId,
    }
  );

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing friends:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle friend press
  const handleFriendPress = (friendId: string) => {
    router.push(`/profile/${friendId}`);
  };

  // Get friends from data
  const friends = userId === currentUser?.id 
    ? data?.currentUser?.friends || [] 
    : data?.user?.friends || [];

  // Filter friends by search query
  const filteredFriends = searchQuery
    ? friends.filter((friend: any) => {
        const name = friend.userProfile
          ? `${friend.userProfile.firstName || ''} ${friend.userProfile.lastName || ''}`.trim()
          : friend.emails?.[0]?.address?.split('@')[0] || '';
        return name.toLowerCase().includes(searchQuery.toLowerCase());
      })
    : friends;

  // Render friend item
  const renderFriendItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.friendItem}
      onPress={() => handleFriendPress(item.id)}
    >
      <Avatar
        source={item.userProfile?.avatar}
        name={item.userProfile ? 
          `${item.userProfile.firstName || ''} ${item.userProfile.lastName || ''}`.trim() :
          item.emails?.[0]?.address?.split('@')[0]
        }
        size="medium"
        online={item.isOnline}
      />
      <ThemedView style={styles.friendInfo}>
        <ThemedText variant="subtitle">{item.userProfile ? 
          `${item.userProfile.firstName || ''} ${item.userProfile.lastName || ''}`.trim() :
          item.emails?.[0]?.address?.split('@')[0]
        }</ThemedText>
        {item.lastOnline && (
          <ThemedText variant="caption">
            {item.isOnline ? 'Online now' : `Last seen ${formatRelativeTime(item.lastOnline)}`}
          </ThemedText>
        )}
      </ThemedView>
    </TouchableOpacity>
  );

  // Render empty state
  const renderEmptyState = () => (
    <ThemedView style={styles.emptyState}>
      <ThemedText>
        {searchQuery ? 'No friends match your search' : 'No friends found'}
      </ThemedText>
    </ThemedView>
  );

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <ThemedText variant="title">Friends</ThemedText>
      </ThemedView>

      <ThemedView style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={theme.colors.text} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search friends"
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={theme.colors.textLight}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={theme.colors.text} />
          </TouchableOpacity>
        ) : null}
      </ThemedView>

      {loading && !data ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <ThemedText style={styles.loadingText}>Loading friends...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>
            Error loading friends: {error.message}
          </ThemedText>
          <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
        </ThemedView>
      ) : (
        <FlatList
          data={filteredFriends}
          keyExtractor={(item) => item.id}
          renderItem={renderFriendItem}
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={styles.listContent}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    marginRight: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    backgroundColor: 'rgba(0,0,0,0.05)',
    margin: 12,
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    padding: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  listContent: {
    flexGrow: 1,
  },
  friendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  friendInfo: {
    marginLeft: 12,
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
});
