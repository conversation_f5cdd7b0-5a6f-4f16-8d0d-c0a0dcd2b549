import { useLazyQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Modal,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useZone } from '../../contexts/ZoneContext';
import { SEARCH_ENTITIES } from '../../lib/graphql-operations';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';
import { OptimizedImage } from './OptimizedImage';

// Define the types of entities that can be searched
export type EntityType = 'post' | 'place' | 'happening' | 'offer' | 'user' | 'all';

interface EntitySearchProps {
  onSelect: (entity: any) => void;
  onCancel: () => void;
  initialType?: EntityType;
  placeholder?: string;
  modalVisible?: boolean;
}

export function EntitySearch({
  onSelect,
  onCancel,
  initialType = 'all',
  placeholder = 'Search for posts, places, events, offers, or users...',
  modalVisible = true,
}: EntitySearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<EntityType>(initialType);
  const { currentZone } = useZone();
  const theme = useTheme();

  // Define the search query
  const [searchEntities, { loading, error, data }] = useLazyQuery(SEARCH_ENTITIES, {
    fetchPolicy: 'network-only',
  });

  // Perform search when query changes
  useEffect(() => {
    if (searchQuery.length >= 2 && currentZone) {
      searchEntities({
        variables: {
          query: searchQuery,
          type: selectedType === 'all' ? null : selectedType,
          zoneId: currentZone.id,
          limit: 20,
        },
      });
    }
  }, [searchQuery, selectedType, currentZone]);

  // Get search results
  const searchResults = data?.searchEntities || [];

  // Handle entity selection
  const handleSelectEntity = (entity: any) => {
    onSelect(entity);
  };

  // Render entity item
  const renderEntityItem = ({ item }: { item: any }) => {
    // Determine entity type
    const entityType = item.title && item.when ? 'happening' :
                      item.title && item.desc ? 'post' :
                      item.name ? 'place' :
                      item.title && item.redemptionCode ? 'offer' : 'user';

    // Get entity name
    const entityName = item.title || item.name ||
                      (item.userProfile ?
                        `${item.userProfile.firstName || ''} ${item.userProfile.lastName || ''}`.trim() :
                        'Unknown');

    // Get entity image
    const entityImage = item.avatar ||
                       (item.userProfile ? item.userProfile.avatar : null);

    // Get entity description
    const entityDesc = item.desc || 
                      (entityType === 'user' && item.userProfile ? 
                        (item.userProfile.bio || 'User') : 
                        (entityType === 'happening' ? 'Event' : 
                         entityType === 'place' ? 'Place' : 
                         entityType === 'offer' ? 'Offer' : 'Post'));

    // Get entity icon
    const getEntityIcon = () => {
      switch (entityType) {
        case 'happening':
          return 'calendar';
        case 'place':
          return 'location';
        case 'offer':
          return 'pricetag';
        case 'user':
          return 'person';
        case 'post':
        default:
          return 'document-text';
      }
    };

    return (
      <TouchableOpacity
        style={styles.entityItem}
        onPress={() => handleSelectEntity(item)}
      >
        <View style={styles.entityImageContainer}>
          {entityImage ? (
            <OptimizedImage
              source={entityImage}
              style={styles.entityImage}
              containerStyle={styles.entityImageWrapper}
              fallbackSource={`https://via.placeholder.com/50?text=${entityName.charAt(0)}`}
            />
          ) : (
            <View style={[styles.entityImagePlaceholder, { backgroundColor: theme.colors.border }]}>
              <Ionicons name={getEntityIcon()} size={20} color={theme.colors.text} />
            </View>
          )}
        </View>

        <View style={styles.entityInfo}>
          <ThemedText style={styles.entityName} numberOfLines={1}>
            {entityName}
          </ThemedText>
          <ThemedText style={styles.entityType} numberOfLines={1}>
            {entityType.charAt(0).toUpperCase() + entityType.slice(1)}
          </ThemedText>
          <ThemedText style={styles.entityDesc} numberOfLines={2}>
            {entityDesc}
          </ThemedText>
        </View>

        <Ionicons name="chevron-forward" size={20} color={theme.colors.text} />
      </TouchableOpacity>
    );
  };

  // Render entity type filter buttons
  const renderEntityTypeFilters = () => {
    const entityTypes: EntityType[] = ['all', 'post', 'place', 'happening', 'offer', 'user'];

    return (
      <View style={styles.entityTypeFilters}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {entityTypes.map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.entityTypeButton,
                selectedType === type && { backgroundColor: theme.colors.primary },
              ]}
              onPress={() => setSelectedType(type)}
            >
              <ThemedText
                style={[
                  styles.entityTypeText,
                  selectedType === type && { color: '#fff' },
                ]}
              >
                {type === 'all' ? 'All' : type.charAt(0).toUpperCase() + type.slice(1)}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <Modal
      visible={modalVisible}
      transparent
      animationType="slide"
      onRequestClose={onCancel}
    >
      <ThemedView style={styles.modalContainer}>
        <Card style={styles.container}>
          <View style={styles.header}>
            <ThemedText style={styles.title}>Link to Entity</ThemedText>
            <TouchableOpacity onPress={onCancel} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color={theme.colors.text} style={styles.searchIcon} />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              placeholder={placeholder}
              placeholderTextColor={theme.colors.textLight}
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={theme.colors.text} />
              </TouchableOpacity>
            ) : null}
          </View>

          {renderEntityTypeFilters()}

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <ThemedText style={styles.loadingText}>Searching...</ThemedText>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <ThemedText style={styles.errorText}>
                Error searching entities: {error.message}
              </ThemedText>
            </View>
          ) : (
            <FlatList
              data={searchResults}
              renderItem={renderEntityItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContent}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}

              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <ThemedText style={styles.emptyText}>
                    {searchQuery.length < 2
                      ? 'Type at least 2 characters to search'
                      : 'No results found'}
                  </ThemedText>
                </View>
              }
            />
          )}

          <View style={styles.footer}>
            <Button
              title="Cancel"
              onPress={onCancel}
              variant="secondary"
              style={styles.footerButton}
            />
          </View>
        </Card>
      </ThemedView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
        pointerEvents: 'auto'

  },
  container: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 10,
    overflow: 'hidden',
        pointerEvents: 'auto'

  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  entityTypeFilters: {
    padding: 10,
  },
  entityTypeButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#f0f0f0',
  },
  entityTypeText: {
    fontSize: 14,
  },
  listContent: {
    padding: 10,
  },
  entityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  entityImageContainer: {
    marginRight: 10,
  },
  entityImageWrapper: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
  },
  entityImage: {
    width: '100%',
    height: '100%',
  },
  entityImagePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  entityInfo: {
    flex: 1,
    marginRight: 10,
  },
  entityName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  entityType: {
    fontSize: 12,
    marginBottom: 4,
  },
  entityDesc: {
    fontSize: 14,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  errorContainer: {
    padding: 20,
  },
  errorText: {
    color: 'red',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  footerButton: {
    minWidth: 100,
  },
});
