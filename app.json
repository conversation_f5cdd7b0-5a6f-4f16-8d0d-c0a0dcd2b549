{"expo": {"jsEngine": "hermes", "name": "Dunedin", "slug": "townapp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "townapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "owner": "townapp", "runtimeVersion": {"policy": "sdkVersion"}, "updates": {"url": "https://u.expo.dev/your-project-id", "enabled": true, "fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logo.png"}, "ios": {"supportsTablet": true, "jsEngine": "jsc", "bundleIdentifier": "com.townapp", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app uses your location to show you nearby zones, places, and events.", "NSCameraUsageDescription": "This app uses your camera to take photos for posts and profile pictures.", "NSPhotoLibraryUsageDescription": "This app uses your photo library to select images for posts and profile pictures."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundColor": "#ffffff"}, "package": "com.townapp", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"], "edgeToEdgeEnabled": true}, "plugins": ["expo-router", "expo-location", "expo-image-picker", ["expo-splash-screen", {"image": "./assets/images/logo.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-barcode-scanner"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "your-project-id"}}}}