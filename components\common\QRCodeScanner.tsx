import { Ionicons } from '@expo/vector-icons';
import { Camera } from 'expo-camera';
import React, { useEffect, useState } from 'react';
import { Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { handleDeepLink } from '../../services/deepLinkingService';
import { Button } from '../ui/Button';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface QRCodeScannerProps {
  isVisible: boolean;
  onClose: () => void;
  onScan?: (data: string) => void;
}

export function QRCodeScanner({ isVisible, onClose, onScan }: QRCodeScannerProps) {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [scanResult, setScanResult] = useState<string | null>(null);
  const theme = useTheme();

  // Request camera permission
  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  // Reset state when modal is opened
  useEffect(() => {
    if (isVisible) {
      setScanned(false);
      setScanResult(null);
    }
  }, [isVisible]);

  // Handle barcode scan
  const handleBarCodeScanned = ({ type, data }: { type: string; data: string }) => {
    setScanned(true);
    setScanResult(data);

    // Call the onScan callback if provided
    if (onScan) {
      onScan(data);
    }

    // Try to handle the scanned data as a deep link
    const isDeepLink = handleDeepLink(data);

    // If it's not a deep link, we'll just display the data
    if (!isDeepLink) {
      console.log(`Bar code with type ${type} and data ${data} has been scanned!`);
    }
  };

  // Render permission denied state
  if (hasPermission === false) {
    return (
      <Modal
        visible={isVisible}
        transparent
        animationType="slide"
        onRequestClose={onClose}
      >
        <ThemedView style={styles.container}>
          <ThemedView style={styles.permissionContainer}>
            <Ionicons name="camera-outline" size={64} color={theme.colors.error} />
            <ThemedText style={styles.permissionText}>
              Camera permission is required to scan QR codes.
            </ThemedText>
            <Button
              title="Close"
              onPress={onClose}
              style={styles.closeButton}
            />
          </ThemedView>
        </ThemedView>
      </Modal>
    );
  }

  // Render loading state
  if (hasPermission === null) {
    return (
      <Modal
        visible={isVisible}
        transparent
        animationType="slide"
        onRequestClose={onClose}
      >
        <ThemedView style={styles.container}>
          <ThemedView style={styles.loadingContainer}>
            <ThemedText>Requesting camera permission...</ThemedText>
          </ThemedView>
        </ThemedView>
      </Modal>
    );
  }

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.scannerContainer}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.closeIcon}>
              <Ionicons name="close" size={28} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.headerText}>Scan QR Code</Text>
          </View>

          {/* Scanner */}
          {!scanned ? (
            <Camera
              onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
              style={styles.scanner}
              type={CameraType.back}
              barCodeScannerSettings={{
                barCodeTypes: ['qr'],
              }}
            >
              <View style={styles.scannerOverlay}>
                <View style={styles.scannerMarker}>
                  <View style={styles.scannerCorner} />
                  <View style={[styles.scannerCorner, styles.topRight]} />
                  <View style={[styles.scannerCorner, styles.bottomRight]} />
                  <View style={[styles.scannerCorner, styles.bottomLeft]} />
                </View>
                <Text style={styles.scannerText}>
                  Position the QR code within the frame
                </Text>
              </View>
            </Camera>
          ) : (
            <ThemedView style={styles.resultContainer}>
              <Ionicons name="checkmark-circle" size={64} color={theme.colors.success} />
              <ThemedText style={styles.resultText}>QR Code Scanned!</ThemedText>
              {scanResult && (
                <ThemedText style={styles.resultData} numberOfLines={3}>
                  {scanResult}
                </ThemedText>
              )}
              <Button
                title="Scan Again"
                onPress={() => setScanned(false)}
                style={styles.scanAgainButton}
              />
            </ThemedView>
          )}

          {/* Footer */}
          <View style={styles.footer}>
            <Button
              title="Cancel"
              onPress={onClose}
              variant="secondary"
              style={styles.footerButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
        pointerEvents: 'auto'

  },
  scannerContainer: {
    width: '90%',
    height: '80%',
    backgroundColor: '#000',
    borderRadius: 16,
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
  },
  header: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 10,
  },
  closeIcon: {
    position: 'absolute',
    left: 16,
    padding: 4,
  },
  headerText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  scanner: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerOverlay: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerMarker: {
    width: 250,
    height: 250,
    borderRadius: 16,
    position: 'relative',
  },
  scannerCorner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#fff',
    borderTopWidth: 3,
    borderLeftWidth: 3,
    top: 0,
    left: 0,
  },
  topRight: {
    right: 0,
    left: undefined,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderLeftWidth: 0,
  },
  bottomRight: {
    right: 0,
    bottom: 0,
    top: undefined,
    left: undefined,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderTopWidth: 0,
    borderLeftWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    top: undefined,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderTopWidth: 0,
  },
  scannerText: {
    color: '#fff',
    marginTop: 24,
    fontSize: 14,
    textAlign: 'center',
  },
  footer: {
    padding: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
  },
  footerButton: {
    minWidth: 120,
  },
  permissionContainer: {
    width: '80%',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  permissionText: {
    textAlign: 'center',
    marginVertical: 16,
    fontSize: 16,
  },
  closeButton: {
    marginTop: 16,
    minWidth: 120,
  },
  loadingContainer: {
    width: '80%',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  resultText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  resultData: {
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  scanAgainButton: {
    marginTop: 16,
    minWidth: 120,
  },
});
