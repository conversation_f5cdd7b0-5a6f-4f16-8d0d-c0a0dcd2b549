import { Redirect } from 'expo-router';
import { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useZone } from '../contexts/ZoneContext';

export default function Index() {
  console.log('Index screen rendering');
  const [authChecked, setAuthChecked] = useState(false);
  const { user, loading: authLoading } = useAuth();
  const { currentZone, loading: zoneLoading, initialized, error } = useZone();

  // Check authentication status
  useEffect(() => {
    // Mark auth as checked after the auth context has loaded
    if (!authLoading) {
      setAuthChecked(true);
    }
  }, [authLoading]);

  console.log('Index state:', {
    authChecked,
    authLoading,
    user: !!user,
    zoneLoading,
    initialized,
    currentZone: currentZone?.id,
    welcomePage: currentZone?.welcomePage,
    rootPage: currentZone?.rootPage,
  });

  // Show loading indicator while checking authentication or loading zone
  if (authLoading || !authChecked) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#2196F3" />
      </View>
    );
  }

  // If zone context is not initialized yet, show loading
  // (This should be handled by the _layout.js, but we add it here as a fallback)
  if (!initialized) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#2196F3" />
      </View>
    );
  }

  // If there's a zone error, show it
  // (This should be handled by the _layout.js, but we add it here as a fallback)
  if (error) {
    console.error('Zone context error:', error);
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>An unexpected error occurred. Please try again later.</Text>
      </View>
    );
  }

  // Otherwise, redirect to the tabs
  console.log('No welcome page found, redirecting to tabs');
  return <Redirect href="/(tabs)" />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
        pointerEvents: 'auto'

  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 16,
  }
});
