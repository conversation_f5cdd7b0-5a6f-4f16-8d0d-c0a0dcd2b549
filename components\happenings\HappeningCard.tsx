import { Happening, createHappening } from '@/models';
import { Happening as HappeningInterface } from '@/types';
import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, useWindowDimensions } from 'react-native';
import { formatDate } from '../../utils/date';
import { ImageListItem } from '../common/ImageListItem';
import { TeaseCard } from '../common/TeaseCard';



interface HappeningCardProps {
  happening: HappeningInterface | Happening;
  onPress?: () => void;
  forceVariant?: 'tease' | 'image'; // Optional override for testing
  numColumns?: number;
}

function HappeningCardComponent({ happening: happeningData, onPress, forceVariant, numColumns = 1  }: HappeningCardProps) {
  // Get window dimensions for responsive design
  const { width } = useWindowDimensions();

    // Calculate item width based on number of columns
  const itemWidth = numColumns > 1 ? `${100 / numColumns - 2}%` : '100%';

  // Convert to Happening instance if it's not already one
  const happening = happeningData instanceof Happening ? happeningData : createHappening(happeningData);

  // Handle happening press
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(`/happening/${happening.id}` as any);
    }
  };

  // Get status of the event
  const getStatus = () => {
    const now = new Date();

    const startDate = happening.when?.start ? new Date(happening.when.start) : null;
    const endDate = happening.when?.end ? new Date(happening.when.end) : null;

    if (!startDate) return 'Unknown';

    // Check if happening is today
    const isToday = startDate.toDateString() === now.toDateString();

    if (isToday) {
      return 'Today';
    } else if (startDate > now) {
      return 'Upcoming';
    } else if (endDate && endDate > now) {
      return 'Ongoing';
    } else {
      return 'Past';
    }
  };

  // Format date for display
  const getFormattedDate = () => {
    const dateStr = happening.when?.start || '';
    if (!dateStr) return '';

    return formatDate(dateStr);
  };

  // Determine which variant to use based on screen width
  const useTeaseVariant = forceVariant ? forceVariant === 'tease' : width < 768;

  // Get badge color based on status
  const status = getStatus();
  const getBadgeColor = () => {
    switch (status) {
      case 'Today':
        return '#4CAF50'; // Green
      case 'Upcoming':
        return '#2196F3'; // Blue
      case 'Ongoing':
        return '#FF9800'; // Orange
      case 'Past':
        return '#9E9E9E'; // Gray
      default:
        return '#9E9E9E'; // Gray
    }
  };

  // For TeaseCard variant (smaller screens)
  if (useTeaseVariant) {
    return (
      <TeaseCard
        id={happening.id}
        title={happening.title}
        linkPath={happening.linkToMe()}
        iconName={happening.iconName()}
        badge={status}
        badgeColor={getBadgeColor()}
        onPress={handlePress}
      />
    );
  }

  // For ImageListItem variant (larger screens)
  return (
    <ImageListItem
      id={happening.id}
      title={happening.title}
      description={happening.desc}
      avatarURL={happening.avatarUrl()}
      linkPath={happening.linkToMe()}
      iconName={happening.iconName()}
      onPress={handlePress}
      style={[styles.imageListItem, { width: itemWidth }]}

    />
  );
}

// Memoize the component to prevent unnecessary re-renders
export const HappeningCard = memo(HappeningCardComponent);

const styles = StyleSheet.create({
  imageListItem: {
    margin: 8,
  }
});


