import { useEffect, useState, useCallback } from 'react';
import { DocumentNode, useMutation } from '@apollo/client';
import { offlineSupport, NetworkStatus, QueuedMutation } from '../lib/offline-support';

/**
 * Hook for offline support functionality
 */
export function useOfflineSupport() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>(
    offlineSupport.getNetworkStatus()
  );
  const [mutationQueue, setMutationQueue] = useState<QueuedMutation[]>(
    offlineSupport.getMutationQueue()
  );
  const [isSyncing, setIsSyncing] = useState(false);

  // Subscribe to network status changes
  useEffect(() => {
    const unsubscribe = offlineSupport.addNetworkListener((status) => {
      setNetworkStatus(status);
    });

    return unsubscribe;
  }, []);

  // Update mutation queue when it changes
  useEffect(() => {
    const interval = setInterval(() => {
      setMutationQueue(offlineSupport.getMutationQueue());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  /**
   * Queue a mutation for offline execution
   */
  const queueMutation = useCallback(async (
    mutation: DocumentNode,
    variables: any,
    optimisticResponse?: any
  ): Promise<string> => {
    return await offlineSupport.queueMutation(mutation, variables, optimisticResponse);
  }, []);

  /**
   * Manually trigger sync of queued mutations
   */
  const syncMutations = useCallback(async () => {
    setIsSyncing(true);
    try {
      await offlineSupport.syncQueuedMutations();
    } finally {
      setIsSyncing(false);
    }
  }, []);

  /**
   * Clear all queued mutations
   */
  const clearQueue = useCallback(async () => {
    await offlineSupport.clearMutationQueue();
    setMutationQueue([]);
  }, []);

  /**
   * Check if device is online
   */
  const isOnline = useCallback(() => {
    return offlineSupport.isOnline();
  }, []);

  return {
    networkStatus,
    mutationQueue,
    isSyncing,
    isOnline: isOnline(),
    queueMutation,
    syncMutations,
    clearQueue,
  };
}

/**
 * Hook for offline-aware mutations
 * Automatically queues mutations when offline and executes when online
 */
export function useOfflineMutation<TData = any, TVariables = any>(
  mutation: DocumentNode,
  options?: {
    onCompleted?: (data: TData) => void;
    onError?: (error: any) => void;
    optimisticResponse?: (variables: TVariables) => any;
  }
) {
  const { isOnline, queueMutation } = useOfflineSupport();
  const [mutate, { loading, error, data }] = useMutation<TData, TVariables>(mutation, {
    onCompleted: options?.onCompleted,
    onError: options?.onError,
  });

  /**
   * Execute mutation with offline support
   */
  const executeOfflineMutation = useCallback(async (variables: TVariables) => {
    if (isOnline) {
      // Execute immediately if online
      return await mutate({ variables });
    } else {
      // Queue for later execution if offline
      const optimisticResponse = options?.optimisticResponse?.(variables);
      const mutationId = await queueMutation(mutation, variables, optimisticResponse);
      
      // Return a promise-like object for consistency
      return {
        data: optimisticResponse,
        mutationId,
        queued: true,
      };
    }
  }, [isOnline, mutate, queueMutation, mutation, options]);

  return [executeOfflineMutation, { loading, error, data }] as const;
}

/**
 * Hook for network status monitoring
 */
export function useNetworkStatus() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>(
    offlineSupport.getNetworkStatus()
  );

  useEffect(() => {
    const unsubscribe = offlineSupport.addNetworkListener(setNetworkStatus);
    return unsubscribe;
  }, []);

  return {
    networkStatus,
    isOnline: networkStatus.isConnected && networkStatus.isInternetReachable !== false,
    isConnected: networkStatus.isConnected,
    connectionType: networkStatus.type,
  };
}

/**
 * Hook for mutation queue monitoring
 */
export function useMutationQueue() {
  const [mutationQueue, setMutationQueue] = useState<QueuedMutation[]>(
    offlineSupport.getMutationQueue()
  );

  useEffect(() => {
    const interval = setInterval(() => {
      setMutationQueue(offlineSupport.getMutationQueue());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return {
    mutationQueue,
    queueLength: mutationQueue.length,
    hasQueuedMutations: mutationQueue.length > 0,
  };
}
