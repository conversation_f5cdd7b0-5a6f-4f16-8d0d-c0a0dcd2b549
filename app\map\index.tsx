import { useApolloClient } from '@apollo/client';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Platform, ScrollView, StyleSheet, Text, View } from 'react-native';
import { Button } from '../../components/ui/Button';
import { ThemedView } from '../../components/ui/ThemedView';
import { useMap } from '../../contexts/MapContext';
import { useZone } from '../../contexts/ZoneContext';
import { GET_CATEGORIES } from '../../lib/graphql-operations';
import { createCategories } from '../../models';


export default function MapFilter() {
  const { currentZone, loading: zoneLoading } = useZone();
  const { selectedCategory, setSelectedCategory } = useMap();
  const [categories, setCategories] = useState([{ id: '', title: 'All' }]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const zoneId = currentZone?.id || '';
  const client = useApolloClient();
  const isMounted = useRef(true);

  const fetchCategories = useCallback(async () => {
    if (!zoneId || isLoadingCategories) return;

    try {
      setIsLoadingCategories(true);

      // Execute query manually
      const result = await client.query({
        query: GET_CATEGORIES,
        variables: {
          criteria: {
            zone: zoneId,
            ctype: { $in: ['place', 'all'] }
          }
        },
        fetchPolicy: 'cache-first'
      });

      // Process results
      if (isMounted.current && result?.data?.categories) {
        const fetchedCategories = createCategories(result.data.categories);
        setCategories([
          { id: '', title: 'All' },
          ...fetchedCategories,
        ]);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      if (isMounted.current) {
        setIsLoadingCategories(false);
      }
    }
    // fetchCategories dependencies are handled by useCallback
  }, [client, zoneId]);


  useEffect(() => {
    if (zoneId && isMounted.current) {
        fetchCategories();      }
    // Removed fetchCategories from dependencies as it's a useCallback
  }, [zoneId]);
  

    const handleCategorySelect = useCallback((categoryId: string) => {
      setSelectedCategory(categoryId);
    }, []);
  

  // 2. Determine if running in a client-side (browser) environment
  //    We check for 'web' platform explicitly.
  const isClientWeb = Platform.OS === 'web';
  // For native platforms (Android, iOS), we can render directly as they have a window-like environment.
  const canRenderMap = Platform.OS !== 'web' || isClientWeb;

  
  return (
    <div id="pre1">
    <ThemedView style={[styles.container]}>
      {canRenderMap ? (
        // Use React.Suspense while the dynamically imported component loads
        <React.Suspense
          fallback={
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', pointerEvents: 'box-none' }}>
              <ActivityIndicator size="large" />
              {/* Optional: Add a loading message */}
              {isClientWeb && <Text>Loading map...</Text>}
            </View>
          }
        >
        </React.Suspense>
      ) : null}

      {categories.length > 1 && (
        <View style={styles.categoryWrapper}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoryScrollContainer}
            contentContainerStyle={styles.categoryContainer}
          >
            {isLoadingCategories ? (
              <ActivityIndicator size="small" />
            ) : (
              categories.map((category) => (
                <Button
                  key={category.id || 'all'}
                  title={category.title || 'All'}
                  onPress={() => handleCategorySelect(category.id)}
                  variant={selectedCategory === category.id ? 'primary' : 'secondary'}
                  size="small"
                  style={styles.categoryButton}
                />
              ))
            )}
          </ScrollView>
        </View>
      )}
    </ThemedView>
    </div>
  );
  
  
}

const styles = StyleSheet.create({
container: {
    flex: 1,
    padding: 16,
    backgroundColor: 'transparent',
  },  
  categoryScrollContainer: {
    marginBottom: 16,
  },
  categoryContainer: {
    flexDirection: 'row',
    paddingRight: 16,
  },
  categoryButton: {
    marginRight: 8,
    minWidth: 80,
    paddingHorizontal: 12,
    height: 26,
  },
  categoryWrapper: {
    top: 10,
    justifyContent: 'center',
        backgroundColor: 'transparent', 

  },
  categoryFilter: {
    position: 'absolute',
    top: 10,
    left: 10,
    right: 10,
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 5,
    elevation: 1,
    zIndex: 1,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  categoryList: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
  },
  categoryButtonSelected: {
    backgroundColor: '#007bff',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#333',
  },
  categoryButtonTextSelected: {
    color: 'white',
  },
});
