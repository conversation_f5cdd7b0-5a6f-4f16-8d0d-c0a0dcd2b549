import { Happening } from '@/models/Happening';
import { Place } from '@/models/Place';
import { useMutation, useQuery } from '@apollo/client';
import { router, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import WebMapComponent from '../../components/map/WebMapComponent';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useZone } from '../../contexts/ZoneContext';
import { GET_ZONE_DETAILS, JOIN_ZONE, LEAVE_ZONE } from '../../lib/graphql-operations';

export default function ZoneDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { currentZone, setCurrentZoneById } = useZone();
  const [refreshing, setRefreshing] = useState(false);

  // Query zone details
  const { data, loading, error, refetch } = useQuery(GET_ZONE_DETAILS, {
    variables: { id },
  });

  // Join zone mutation
  const [joinZone, { loading: joinLoading }] = useMutation(JOIN_ZONE, {
    onCompleted: () => {
      refetch();
    },
  });

  // Leave zone mutation
  const [leaveZone, { loading: leaveLoading }] = useMutation(LEAVE_ZONE, {
    onCompleted: () => {
      refetch();
    },
  });

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing zone:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle join zone
  const handleJoinZone = () => {
    joinZone({ variables: { id } });
  };

  // Handle leave zone
  const handleLeaveZone = () => {
    leaveZone({ variables: { id } });
  };

  // Handle set as active zone
  const handleSetActiveZone = async () => {
    try {
      await setCurrentZoneById(id as string);
      router.back();
    } catch (error) {
      console.error('Error setting active zone:', error);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Loading state
  if (loading && !data) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  // Error state
  if (error) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading zone: {error.message}
        </ThemedText>
        <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
      </ThemedView>
    );
  }

  const zone = data?.zone;

  if (!zone) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>Zone not found</ThemedText>
      </ThemedView>
    );
  }

  const isActiveZone = currentZone?.id === zone.id;

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <ThemedView style={styles.header}>
        <ThemedText variant="title">{zone.title}</ThemedText>
        <ThemedText variant="caption" style={styles.memberCount}>
          {zone.memberCount} {zone.memberCount === 1 ? 'member' : 'members'}
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.actionsContainer}>
        {zone.isMember ? (
          <>
            {!isActiveZone && (
              <Button
                title="Set as Active Zone"
                onPress={handleSetActiveZone}
                style={styles.actionButton}
              />
            )}
            <Button
              title={leaveLoading ? 'Leaving...' : 'Leave Zone'}
              onPress={handleLeaveZone}
              variant="outline"
              loading={leaveLoading}
              disabled={leaveLoading}
              style={styles.actionButton}
            />
          </>
        ) : (
          <Button
            title={joinLoading ? 'Joining...' : 'Join Zone'}
            onPress={handleJoinZone}
            loading={joinLoading}
            disabled={joinLoading}
            style={styles.actionButton}
          />
        )}
      </ThemedView>

      <Card style={styles.section}>
        <ThemedText variant="subtitle">Location</ThemedText>
        <View style={styles.mapContainer}>
          <WebMapComponent selectedCategory={null} >
          </WebMapComponent>
        </View>
      </Card>

      {zone.places && zone.places.length > 0 && (
        <Card style={styles.section}>
          <ThemedView style={styles.sectionHeader}>
            <Button
              title="View All"
              onPress={() => router.push(`/zone/${zone.id}/places`)}
              variant="text"
              size="small"
            />
          </ThemedView>
          {zone.places.slice(0, 5).map((place: Place) => (
            <ThemedView key={place.id} style={styles.listItem}>
              <ThemedText>{place.name}</ThemedText>
            </ThemedView>
          ))}
          {zone.places.length > 5 && (
            <ThemedText variant="caption" style={styles.moreItems}>
              +{zone.places.length - 5} more places
            </ThemedText>
          )}
        </Card>
      )}

      {zone.happenings && zone.happenings.length > 0 && (
        <Card style={styles.section}>
          <ThemedView style={styles.sectionHeader}>
            <ThemedText variant="subtitle">Upcoming Events</ThemedText>
            <Button
              title="View All"
              onPress={() => router.push(`/zone/${zone.id}/happenings`)}
              variant="text"
              size="small"
            />
          </ThemedView>
          {zone.happenings.slice(0, 5).map((happening: Happening) => (
            <ThemedView key={happening.id} style={styles.listItem}>
              <ThemedText>{happening.title}</ThemedText>
              <ThemedText variant="caption">{happening.when?.start ? formatDate(happening.when.start) : ''}</ThemedText>
            </ThemedView>
          ))}
          {zone.happenings.length > 5 && (
            <ThemedText variant="caption" style={styles.moreItems}>
              +{zone.happenings.length - 5} more events
            </ThemedText>
          )}
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  contentContainer: {
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  zoneImage: {
    width: '100%',
    height: 200,
  },
  noImageContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  header: {
    padding: 16,
  },
  memberCount: {
    marginTop: 4,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  description: {
    marginTop: 8,
    lineHeight: 24,
  },
  mapContainer: {
    marginTop: 8,
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  moreItems: {
    marginTop: 8,
    textAlign: 'center',
  },
});
