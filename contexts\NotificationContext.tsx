import React, { createContext, useContext, useState, useEffect } from 'react';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { useQuery, useMutation, useSubscription } from '@apollo/client';
import { useAuth } from './AuthContext';
import {
  GET_NOTIFICATIONS,
  MARK_NOTIFICATION_READ,
  REGISTER_PUSH_TOKEN,
  NOTIFICATION_RECEIVED,
} from '../lib/graphql-operations';

// Define notification types
export type NotificationType = 
  | 'message' 
  | 'like' 
  | 'comment' 
  | 'friend_request' 
  | 'friend_accepted' 
  | 'proximity' 
  | 'system';

// Define notification interface
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  body: string;
  data?: any;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    userProfile?: {
      firstName?: string;
      lastName?: string;
      avatar?: string;
    };
  };
  entityId?: string;
  entityType?: string;
}

// Define context interface
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: Error | null;
  registerForPushNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refreshNotifications: () => Promise<void>;
  hasPermission: boolean;
}

// Create context
const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null,
  registerForPushNotifications: async () => {},
  markAsRead: async () => {},
  markAllAsRead: async () => {},
  refreshNotifications: async () => {},
  hasPermission: false,
});

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Provider component
export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const { user, isAuthenticated } = useAuth();
  const [expoPushToken, setExpoPushToken] = useState<string | undefined>();
  const [hasPermission, setHasPermission] = useState(false);
  const [localNotifications, setLocalNotifications] = useState<Notification[]>([]);

  // Query to get notifications
  const { data, loading, error, refetch } = useQuery(GET_NOTIFICATIONS, {
    skip: !isAuthenticated,
    fetchPolicy: 'network-only',
  });

  // Mutation to mark notification as read
  const [markNotificationRead] = useMutation(MARK_NOTIFICATION_READ);

  // Mutation to register push token
  const [registerPushToken] = useMutation(REGISTER_PUSH_TOKEN);

  // Subscribe to new notifications
  useSubscription(NOTIFICATION_RECEIVED, {
    skip: !isAuthenticated,
    onData: ({ data }) => {
      if (data?.data?.notificationReceived) {
        const newNotification = data.data.notificationReceived;
        setLocalNotifications((prev) => [newNotification, ...prev]);
      }
    },
  });

  // Update notifications when data changes
  useEffect(() => {
    if (data?.notifications) {
      setLocalNotifications(data.notifications);
    }
  }, [data]);

  // Register for push notifications
  const registerForPushNotifications = async () => {
    if (!Device.isDevice) {
      console.log('Push notifications are not available in the simulator');
      return;
    }

    try {
      // Request permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      setHasPermission(finalStatus === 'granted');

      if (finalStatus !== 'granted') {
        console.log('Permission not granted for push notifications');
        return;
      }

      // Get push token
      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
      });

      setExpoPushToken(tokenData.data);

      // Register token with server
      if (isAuthenticated && tokenData.data) {
        await registerPushToken({
          variables: {
            token: tokenData.data,
            deviceType: Platform.OS,
          },
        });
      }

      // Configure for Android
      if (Platform.OS === 'android') {
        Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }
    } catch (error) {
      console.error('Error registering for push notifications:', error);
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      await markNotificationRead({
        variables: { id: notificationId },
      });

      // Update local state
      setLocalNotifications((prev) =>
        prev.map((notification) =>
          notification.id === notificationId
            ? { ...notification, read: true }
            : notification
        )
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      // This would be a server-side mutation
      // For now, we'll just update the local state
      setLocalNotifications((prev) =>
        prev.map((notification) => ({ ...notification, read: true }))
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Refresh notifications
  const refreshNotifications = async () => {
    if (isAuthenticated) {
      try {
        await refetch();
      } catch (error) {
        console.error('Error refreshing notifications:', error);
      }
    }
  };

  // Calculate unread count
  const unreadCount = localNotifications.filter((n) => !n.read).length;

  // Set up notification listener
  useEffect(() => {
    const subscription = Notifications.addNotificationReceivedListener((notification) => {
      const { title, body, data } = notification.request.content;
      
      // Add notification to local state
      const newNotification: Notification = {
        id: notification.request.identifier,
        type: (data?.type as NotificationType) || 'system',
        title: title || 'New Notification',
        body: body || '',
        data: data,
        read: false,
        createdAt: new Date().toISOString(),
        entityId: data?.entityId,
        entityType: data?.entityType,
      };

      setLocalNotifications((prev) => [newNotification, ...prev]);
    });

    // Clean up
    return () => {
      subscription.remove();
    };
  }, []);

  // Register for push notifications on mount
  useEffect(() => {
    if (isAuthenticated) {
      registerForPushNotifications();
    }
  }, [isAuthenticated]);

  return (
    <NotificationContext.Provider
      value={{
        notifications: localNotifications,
        unreadCount,
        loading,
        error,
        registerForPushNotifications,
        markAsRead,
        markAllAsRead,
        refreshNotifications,
        hasPermission,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}

// Hook to use the notification context
export function useNotifications() {
  return useContext(NotificationContext);
}
