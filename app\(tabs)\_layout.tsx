import { Stack } from 'expo-router';
import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

export default function MainLayout() {
  const theme = useTheme();

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: {
          backgroundColor: 'transparent',
        }
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="profile" />
    </Stack>
  );
}
