import { Redirect, Slot } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import { AppProviders } from '../components/AppProviders';
import { NetworkStatus } from '../components/common/NetworkStatus';
import ResponsiveNav from '../components/navigation/ResponsiveNav';
import PersistentMapLayout from '../components/PersistentMapLayout';
import { Button } from '../components/ui/Button';
import { ThemedText } from '../components/ui/ThemedText';
import { ThemedView } from '../components/ui/ThemedView';
import { useTheme, useThemeControls } from '../contexts/ThemeContext';
import { useZone } from '../contexts/ZoneContext';

// Loading screen component
function LoadingScreen({ message = 'Loading...' }: { message?: string }) {
  
  return (
    <View style={styles.centeredContainer}>
      <ActivityIndicator size="large" color="#2196F3" />
      <Text style={styles.loadingText}>{message}</Text>
    </View>
  );
}

// Error screen component
function ErrorScreen({ message, onRetry }: { message: string; onRetry?: () => void }) {
  
  const theme = useTheme();

  const errorStyles = StyleSheet.create({
  errorText: {
    color: theme.colors.error, // Updated to use theme-based color
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 16,
  },
});

  return (
    <ThemedView style={styles.centeredContainer}>
      <ThemedText style={errorStyles.errorText}>{message}</ThemedText>
      {onRetry && (
        <Button title="Retry" onPress={onRetry} style={styles.retryButton} />
      )}
    </ThemedView>
  );
}

// Main layout component
function MainLayout() {
  const { currentZone, loading, error, initialized } = useZone();
  const { toggleTheme, isDark } = useThemeControls();

  console.log('MainLayout rendering', { loading, error, initialized, currentZone });

  // If zone context is not initialized yet, show a loading screen
  if (!initialized) {
    return <LoadingScreen message="Loading..." />;
  }

  // If there's an error, show an error screen
  if (error) {
    return <ErrorScreen message={error} />;
  }

  if (currentZone?.welcomePage) {
    const hasWindow = typeof window !== 'undefined';
    const pathname = hasWindow && window.location ? window.location.pathname : '';
    const isHomePath = pathname === '/';
    if (isHomePath) {
      return <Redirect href={`/page/${currentZone.welcomePage}`} />;
    }
  }

  // Otherwise, use the default layout
  return (
    <ThemedView style={styles.container}>
      <ResponsiveNav />
      <Button
        title={isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        onPress={toggleTheme}
        style={styles.themeToggleButton}
      />
      <PersistentMapLayout>
        <Slot />
      </PersistentMapLayout>
      <NetworkStatus />
      <StatusBar style="auto" />
    </ThemedView>
  );
}

// Root layout component
export default function RootLayout() {
  console.log('RootLayout rendering');

  return (
    <AppProviders>
      <MainLayout />
    </AppProviders>
  );
}

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'transparent',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  retryButton: {
    marginTop: 16,
  },
  themeToggleButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1000,
  },
});
