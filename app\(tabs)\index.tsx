'use client';

import { useRouter } from 'expo-router';
import { useEffect } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedView } from '../../components/ui/ThemedView';
import { useZone } from '../../contexts/ZoneContext';

export default function HomeScreen() {
  const { currentZone, loading: zoneLoading } = useZone();
  const router = useRouter();

  useEffect(() => {
    if (currentZone?.welcomePage) {
      router.push(`/page/${currentZone.welcomePage}`);
    }
  }, [currentZone, router]);

  if (zoneLoading || !currentZone?.welcomePage) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </ThemedView>
    );
  }

  return null;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
