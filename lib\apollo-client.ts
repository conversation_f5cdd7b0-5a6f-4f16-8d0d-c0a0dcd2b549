import { ApolloClient, ApolloLink, from, HttpLink, InMemoryCache } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Helper function to safely access localStorage
const safeLocalStorage = {
  getItem: (key: string): string | null => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        return window.localStorage.getItem(key);
      }
      return null;
    } catch (error) {
      console.error('Error accessing localStorage.getItem:', error);
      return null;
    }
  },
  setItem: (key: string, value: string): void => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem(key, value);
      }
    } catch (error) {
      console.error('Error accessing localStorage.setItem:', error);
    }
  },
  removeItem: (key: string): void => {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error accessing localStorage.removeItem:', error);
    }
  }
};

// Get the GraphQL endpoint from environment variables or use default
const GRAPHQL_URL = process.env.EXPO_PUBLIC_GRAPHQL_URL || 'https://townql.fly.dev/graphql';

// Create a simple HTTP link
const httpLink = new HttpLink({
  uri: GRAPHQL_URL,
});

// Auth link to add the token to every request
const authLink = setContext(async (_, { headers }) => {
  // Get the authentication token
  let token = null;
  let zoneId = null;

  try {
    if (Platform.OS === 'web') {
      token = safeLocalStorage.getItem('auth_token');
      zoneId = safeLocalStorage.getItem('current_zone_id');
    } else {
      token = await SecureStore.getItemAsync('auth_token');
      zoneId = await SecureStore.getItemAsync('current_zone_id');
    }
  } catch (error) {
    console.error('Error getting auth token or zone ID:', error);
  }

  console.log('Using token in apollo call:', token);
  // Return the headers to the context
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
      'x-zone-id': zoneId || '',
    },
  };
});

// Error handling link
const errorLink = onError(({ graphQLErrors, networkError, operation }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(
        `[GraphQL error]: Message: ${message}, Location: ${JSON.stringify(locations)}, Path: ${path}, Operation: ${operation.operationName}`
      );
    });
  }
  if (networkError) {
    console.error(`[Network error]: ${networkError}`);
    console.error(`Operation: ${operation.operationName}`);
  }
});

// Debug link to log all operations in development
const debugLink = new ApolloLink((operation, forward) => {
  if (__DEV__) {
    console.log(`Apollo operation: ${operation.operationName}`, {
      variables: operation.variables,
    });
  }

  return forward(operation).map((result) => {
    if (__DEV__) {
      console.log(`Apollo result: ${operation.operationName}`, {
        data: result.data,
        errors: result.errors,
      });
    }
    return result;
  });
});

// Create an optimized cache with type policies
const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        // Merge items in arrays by ID to avoid duplicates
        posts: {
          keyArgs: ['zoneId', 'criteria'],
          merge(existing = [], incoming = [], { args }) {
            // If we're loading the first page or refreshing, just return the incoming data
            if (!args?.offset || args.offset === 0) {
              return incoming;
            }

            // Otherwise, merge the existing and incoming data
            return [...existing, ...incoming];
          }
        },
        places: {
          keyArgs: ['zoneId', 'criteria'],
          merge(existing = [], incoming = [], { readField }) {
            // incoming is an array of references { __ref: 'Place:some-id' }
            // Use readField to get the id from each reference
            const existingMap = new Map(existing.map(item => [readField('id', item), item]));
        
            incoming.forEach(item => {
              const id = readField('id', item);
              if (id) {
                existingMap.set(id, item);
              }
            });
        
            return Array.from(existingMap.values());
          }
        }
      }
    }
  }
});

// Create Apollo Client with optimized configuration
const client = new ApolloClient({
  link: from([debugLink, errorLink, authLink, httpLink]),
  cache,
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    },
    query: {
      fetchPolicy: 'cache-and-network',
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
  connectToDevTools: __DEV__,
  assumeImmutableResults: true, // Performance optimization
  queryDeduplication: true, // Prevent duplicate requests
});

console.log('New Apollo client created with GraphQL URL:', GRAPHQL_URL);

export default client;
