import { Campaign as CampaignInterface } from '@/types/campaign';
import { Challenge } from '@/types/challenge';
import { Asset } from 'expo-asset';

/**
 * Campaign class that implements the CampaignInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Campaign implements CampaignInterface {
  id: string;
  title: string;
  desc?: string;
  avatar?: string;
  starts: string;
  stops: string;
  targetScore?: number;
  winnerCount?: number;
  prize?: string;
  autoEnroll?: boolean;
  zone: string;
  userId: string;
  createdAt: string;
  updatedAt?: string;
  challenges?: Challenge[];
  likes?: {
    id: string;
    userId: string;
  }[];
  comments?: {
    id: string;
    body: string;
    userId: string;
    createdAt: string;
  }[];

  constructor(campaignData: CampaignInterface) {
    Object.assign(this, campaignData);
  }

  /**
   * Get the URL for this campaign
   * @returns The URL for the campaign
   */
  linkToMe(): string {
    return `/campaign/${this.id}`;
  }

  /**
   * Get the avatar URL for this campaign
   * @returns The URL for the avatar image
   */
  avatarUrl(): string {
    if (this.avatar) {
      return this.ensureProperUrl(this.avatar);
    }
    // Return a default avatar if none is set
    return Asset.fromModule(require('../assets/images/campaign-placeholder.png')).uri;
  }

  /**
   * Format the start date
   * @returns A formatted string with the start date
   */
  formattedStarts(): string {
    if (!this.starts) {
      return '';
    }
    
    const date = new Date(this.starts);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Format the end date
   * @returns A formatted string with the end date
   */
  formattedStops(): string {
    if (!this.stops) {
      return '';
    }
    
    const date = new Date(this.stops);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Check if the campaign has started
   * @returns True if the campaign start date is in the past
   */
  hasStarted(): boolean {
    const starts = new Date(this.starts);
    const now = new Date();
    return starts <= now;
  }

  /**
   * Check if the campaign has ended
   * @returns True if the campaign end date is in the past
   */
  hasEnded(): boolean {
    const stops = new Date(this.stops);
    const now = new Date();
    return stops < now;
  }

  /**
   * Check if the campaign is currently active
   * @returns True if the campaign is currently active
   */
  isActive(): boolean {
    return this.hasStarted() && !this.hasEnded();
  }

  /**
   * Get the total number of challenges in the campaign
   * @returns The number of challenges
   */
  challengeCount(): number {
    return this.challenges ? this.challenges.length : 0;
  }

  /**
   * Get a short excerpt of the description
   * @param length Maximum length of the excerpt
   * @returns A truncated version of the description
   */
  excerpt(length: number = 150): string {
    if (!this.desc) {
      return '';
    }
    
    // Remove HTML tags
    const textOnly = this.desc.replace(/<[^>]*>/g, '');
    
    if (textOnly.length <= length) {
      return textOnly;
    }
    
    // Truncate and add ellipsis
    return textOnly.substring(0, length) + '...';
  }

  /**
   * Ensure a URL has the proper protocol
   * @param url The URL to check
   * @returns The URL with proper protocol
   */
  private ensureProperUrl(url: string): string {
    if (!url) return url;

    // If it already has a protocol, return as is
    if (url.substring(0, 4) === 'http') {
      return url;
    }

    // Add // if needed
    if (url.substring(0, 2) !== '//') {
      url = '//' + url;
    }

    // Add https: protocol
    return 'https:' + url;
  }
}

/**
 * Factory function to create a Campaign instance from a plain object
 * @param data The campaign data
 * @returns A new Campaign instance
 */
export function createCampaign(data: CampaignInterface): Campaign {
  return new Campaign(data);
}

/**
 * Factory function to create multiple Campaign instances from an array
 * @param dataArray Array of campaign data
 * @returns Array of Campaign instances
 */
export function createCampaigns(dataArray: CampaignInterface[]): Campaign[] {
  return dataArray.map(data => createCampaign(data));
}
