-- Merging decision tree log ---
manifest
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:1:1-37:12
MERGED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:1:1-37:12
INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-community_datetimepicker] C:\ckt_web\townexpo\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-gesture-handler] C:\ckt_web\townexpo\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\ckt_web\townexpo\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\ckt_web\townexpo\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-edge-to-edge] C:\ckt_web\townexpo\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:expo] C:\ckt_web\townexpo\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] C:\ckt_web\townexpo\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-barcode-scanner] C:\ckt_web\townexpo\node_modules\expo-barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:expo-image-loader] C:\ckt_web\townexpo\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ff243dfa84fd6415f0286bf98b8cdd4\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:2:1-19:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\11a07a58f4f278e5636d3955c85c4005\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12ce7c399589985cd9a17f6a56d3e5e\transformed\expo.modules.systemui-5.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f50bf460fa354c8e12420350dfccc25c\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:2:1-16:12
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:2:1-57:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\80f536539ab86eaa04dabc7c96574dce\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ee98fb9f9f8f1778cc9146def7e919f\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\602de934b91283f0efb3aa8dfaded082\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9732a0b964bbab8793beb16c8023a40\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:14:1-22:12
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9db9bf2b239dc3189dc697ed91bd6a\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d8aa4595c7c088876e8d349b817bdb\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\474a689798ab843029c3b1706caa25b0\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfeea66209e4b1f108143c44d47bb08a\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\64cc707f832d1c4771a0527ab5968ba9\transformed\glide-plugin-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:2:1-36:12
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b31fc8addf479ce1997bfb5b00efa6e\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c87ac20925a863f474dc9b953e673ca\transformed\awebp-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\70fdfccc588f9321ea3d49b87ba858bb\transformed\apng-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3424c2fd71faf5e5b5167b3ce1f389a8\transformed\gif-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1df82dfa871eece927dac1cd38803ae\transformed\avif-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4b476bd3d63236221fa50b96965e8a3\transformed\frameanimation-3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33edec337de23b6d7afccb07bf9c5a56\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ef21dbb346e7cd0de80e551a2681b6e\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-33:12
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e44cf8f179dd9cc40ffc4aa5add7e55\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e425266743d78bd82b101ed6eacd4e9\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ee4aa3e9ef054ea30909d8286b16a3\transformed\avif-integration-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e7b9e7a66d1ecd12367a68b703474de\transformed\glide-transformations-4.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1da7927f873a411fb54b7b77f2e1995b\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\334e915e1090d282f1372c2ee823f435\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-43:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3f0422d9d6a0477fddeb479992ab832\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f9c9fae8352e7baf79e8aa03ee3b5ff\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7685c2c6a1cd6c65622b16cf4b7b0bcd\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3974ea1fa6ae95ae0e48f28a28300ac\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f0637a06d2bd18b5dc45a5cb644d9fa\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a08b4cd8d2d0635798883ef0caf16c8\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a932d8d9c77d055eecfc9d50e5584c97\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b9aab596c524bc904af394b58ce492a\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a0765da38a4bc9f174a555257d5eb09\transformed\activity-ktx-1.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece8ff3fce1bd18ec182193249154aa7\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35871f0c249b283487af41b762edc9c1\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f53fc86618013be3473017fba58bd2\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f198d0277aba890cd5594f78cc2a9ca\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de1fe5e58f77e62da44b1ec0e8feff96\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56ea8ea5280c784cd8f4638797d5ed10\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eff23ba191a43b757a0a228443ac73bb\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93af6480dfb89bd842051126a2578b5a\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\133efea1024462122da8bb7b37f8b356\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c084df09d92122a458663fe6ffcb5df\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f997e077d97d5ba7bd1864c0ab9822\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0ebd35670d77efa572b2d2e775067b1\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\799db128f658cb8ec4e4111102e8cc1c\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f86df5c6c29f2c13aa69652907495866\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91ff427e00e9af2ad040fe5d990b099b\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cdf07aa61e1df0d4b55d391cc94d05\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7461ce11257921df3bc43ba3b17edf47\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ff1451445a52c77065e078eb075821b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54565c25958a7f4b15edda7e7c438894\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\622e2425e6e7f8fa39b69d11fc9d9be2\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a26bc77b69995eee87f406079b5ce75f\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6ab924621b2f036ab2d3120d61b1cf2\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc1d002fd69ab43b19bc83b368833c0e\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65d4ddd65ecd855ba77979c6c35172d9\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fa470005cfc34c029326d0c0c465254\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50f102e539d507ee7534b15f042ba5cb\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cdadffb8f8b405863102824e5feb13f\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac19dc5a5207c2c98cebfdf8d05f66d1\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df4f7478d75e5fc452a628eeea7bd8fc\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fee46ee68ab1504291923b8eb23245a5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa13e8657fce0ddec4843a79409f337a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\645ca5691b753ff506b30dfcf922a75a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0fb56928e50de3ce5e04860e2a1ea6e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fdcbb7eb51ef8e1bfa08105cbda6005\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8b4693213e13d15211b76916a14a86c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\907660c1eed9db1fbe306717f8018b8a\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\054fe50356e6fc46219a3df1f0c26342\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94de804fa8d1513a5090fa9cbd9ac284\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e06c1a83d91b43e66ff956d7af621262\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84b542bd85cb3df645b3b30e5fb720fa\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b7e9a33af5a30767aaf80618b1f92d8\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fffe61cf8789e2cf7cf2d11dc79095e8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8674f204c6eca6eaf65aad3a1a8b910\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b74851ffeb8d9a462885e4f257d01c0e\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5964219d36e5b7d52648a071b70b5cd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\04e54340660830c32eee28badf0296dd\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c53d07303439f8e066007b090b8453b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e379020a267009186dab9b942fa6fc99\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\90e210e969d5cb571ac2d79399939d6b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d123fe5796fed332bb6d367efa4916a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\86b24f0bc2049a1cfdf63053979f6c1a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba6e8ebe91b6b316e13fa0c2021f49df\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d0bf68e7122ef278630ebe7463ebf92\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fa3f20ae2eed2574420e15e5e9c535\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6d5546b7d03dd9341fbc65b877f56fa\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dd5076bcb66e9b842e24100d682b7b1\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-constants] C:\ckt_web\townexpo\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] C:\ckt_web\townexpo\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a26b5f2cc61db919d909417f2609434\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c828745ef19678d71760f37c2f995e97\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad506c59ed829706853f415989d4ff0e\transformed\expo.modules.blur-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7699414200306d2418f81e61bc3463f\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe931e5948208a1eb6a2669e553f6627\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a354129a802daa996d09d38a03470bb\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\019fbd4571fa72f7d92bd7da6eff94f3\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af2eed43806ad0c03354c8a90f3130f1\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ca26bc7ceb4c96a70355493aa4a4b33\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbbd42d578db9754127eff2d0850d066\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4283c846aac54d9c544dfb1a29f46af7\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f448083111b7cd846b6fdfc7adbb3e45\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17231d9788bbda824baa8689fb0f19df\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87a020a61d364b36d092a54ed20cd4ed\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f71fa759416485b618a7790c45fe6ffd\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\307710da9674a4305503d85af5febdbe\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04210093dcd5e70600dcb4b9187f667f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f77330de1c2ec0312b5be11acb9c409a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b6f773569a47427a567d5b6041be05e\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e2816fafe5f3d07e7950eda52686a1d\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c091eeb76134ad2931f57fb3344091a0\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2745e58e193a6e9f92fa81c5980ea3\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b336d76ccf6b45156fd6aa6c568d91ab\transformed\viewbinding-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7260aa4853075dc3a3daa140ed849a4\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2532bec72fa4263c034c15098814dfc2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1558d5233106c167f46c6d493462ef6d\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a95c313c9c4557c601aaf659b17372b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [:shopify_flash-list] C:\ckt_web\townexpo\node_modules\@shopify\flash-list\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a89f88737dcaf880ea21b9fdcb02480\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e034a32d50552180691625f26b2a57\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\25e9ad356f5d45298c5a28aadcc3cf0e\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\099a42e66893b10cc59c759614bf353c\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa65e09bc3810e2ab00dbe80065ced04\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:2:3-78
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:3:3-76
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:3:20-74
uses-permission#android.permission.CAMERA
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:4:3-62
MERGED from [:expo-barcode-scanner] C:\ckt_web\townexpo\node_modules\expo-barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:expo-barcode-scanner] C:\ckt_web\townexpo\node_modules\expo-barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:7:5-65
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:7:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:4:20-60
uses-permission#android.permission.INTERNET
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:5:3-64
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:5:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:6:3-77
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:15:5-17:38
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:15:5-17:38
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:12:5-80
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:12:5-80
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
	android:maxSdkVersion
		ADDED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:6:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:7:3-68
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:8:5-71
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:8:5-71
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:7:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:8:3-75
MERGED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:8:3-75
MERGED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:8:3-75
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:8:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:9:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7699414200306d2418f81e61bc3463f\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7699414200306d2418f81e61bc3463f\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:9:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:10:3-78
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:11:5-81
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:11:5-81
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:10:20-76
queries
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:11:3-17:13
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:14:5-25:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:14:5-25:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:12:5-16:14
action#android.intent.action.VIEW
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:13:7-58
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:13:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:14:7-67
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:14:17-65
data
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:15:7-37
	android:scheme
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:15:13-35
application
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:3-36:17
MERGED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:3-36:17
MERGED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:3-36:17
INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-community_datetimepicker] C:\ckt_web\townexpo\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-community_datetimepicker] C:\ckt_web\townexpo\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f50bf460fa354c8e12420350dfccc25c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f50bf460fa354c8e12420350dfccc25c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:10:5-14:19
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:10:5-14:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:27:5-55:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:27:5-55:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\602de934b91283f0efb3aa8dfaded082\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\602de934b91283f0efb3aa8dfaded082\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\474a689798ab843029c3b1706caa25b0\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\474a689798ab843029c3b1706caa25b0\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-15:19
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-15:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\334e915e1090d282f1372c2ee823f435\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\334e915e1090d282f1372c2ee823f435\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7685c2c6a1cd6c65622b16cf4b7b0bcd\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7685c2c6a1cd6c65622b16cf4b7b0bcd\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3974ea1fa6ae95ae0e48f28a28300ac\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3974ea1fa6ae95ae0e48f28a28300ac\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f0637a06d2bd18b5dc45a5cb644d9fa\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f0637a06d2bd18b5dc45a5cb644d9fa\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d0bf68e7122ef278630ebe7463ebf92\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d0bf68e7122ef278630ebe7463ebf92\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a354129a802daa996d09d38a03470bb\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a354129a802daa996d09d38a03470bb\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87a020a61d364b36d092a54ed20cd4ed\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87a020a61d364b36d092a54ed20cd4ed\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f77330de1c2ec0312b5be11acb9c409a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f77330de1c2ec0312b5be11acb9c409a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e034a32d50552180691625f26b2a57\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e034a32d50552180691625f26b2a57\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\099a42e66893b10cc59c759614bf353c\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\099a42e66893b10cc59c759614bf353c\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa65e09bc3810e2ab00dbe80065ced04\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa65e09bc3810e2ab00dbe80065ced04\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:221-247
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:221-247
	android:label
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:48-80
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:48-80
	tools:ignore
		ADDED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:116-161
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:116-161
	tools:targetApi
		ADDED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:81-115
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:81-115
	android:allowBackup
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:162-188
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:162-188
	android:theme
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:189-220
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:189-220
	tools:replace
		ADDED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:16-47
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:19:5-82
	android:value
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:19:60-80
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:19:16-59
meta-data#expo.modules.updates.EXPO_RUNTIME_VERSION
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:20:5-119
	android:value
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:20:73-117
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:20:16-72
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:21:5-105
	android:value
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:21:81-103
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:21:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:22:5-99
	android:value
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:22:80-97
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:22:16-79
meta-data#expo.modules.updates.EXPO_UPDATE_URL
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:23:5-120
	android:value
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:23:68-118
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:23:16-67
activity#com.townapp.MainActivity
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:5-35:16
	android:screenOrientation
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:280-316
	android:launchMode
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:135-166
	android:windowSoftInputMode
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:167-209
	android:exported
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:256-279
	android:configChanges
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:44-134
	android:theme
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:210-255
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:25:7-28:23
action#android.intent.action.MAIN
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:26:9-60
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:26:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:27:9-68
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:27:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:townapp
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:29:7-34:23
category#android.intent.category.DEFAULT
ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:31:9-67
	android:name
		ADDED from C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:31:19-65
uses-sdk
INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-community_datetimepicker] C:\ckt_web\townexpo\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] C:\ckt_web\townexpo\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\ckt_web\townexpo\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\ckt_web\townexpo\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\ckt_web\townexpo\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\ckt_web\townexpo\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\ckt_web\townexpo\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\ckt_web\townexpo\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\ckt_web\townexpo\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\ckt_web\townexpo\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\ckt_web\townexpo\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\ckt_web\townexpo\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\ckt_web\townexpo\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\ckt_web\townexpo\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-barcode-scanner] C:\ckt_web\townexpo\node_modules\expo-barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-barcode-scanner] C:\ckt_web\townexpo\node_modules\expo-barcode-scanner\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\ckt_web\townexpo\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\ckt_web\townexpo\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ff243dfa84fd6415f0286bf98b8cdd4\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ff243dfa84fd6415f0286bf98b8cdd4\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:4:5-44
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:4:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\11a07a58f4f278e5636d3955c85c4005\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\11a07a58f4f278e5636d3955c85c4005\transformed\expo.modules.splashscreen-0.30.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12ce7c399589985cd9a17f6a56d3e5e\transformed\expo.modules.systemui-5.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12ce7c399589985cd9a17f6a56d3e5e\transformed\expo.modules.systemui-5.0.7\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f50bf460fa354c8e12420350dfccc25c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f50bf460fa354c8e12420350dfccc25c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:6:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\80f536539ab86eaa04dabc7c96574dce\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\80f536539ab86eaa04dabc7c96574dce\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ee98fb9f9f8f1778cc9146def7e919f\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ee98fb9f9f8f1778cc9146def7e919f\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\602de934b91283f0efb3aa8dfaded082\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\602de934b91283f0efb3aa8dfaded082\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9732a0b964bbab8793beb16c8023a40\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9732a0b964bbab8793beb16c8023a40\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9db9bf2b239dc3189dc697ed91bd6a\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9db9bf2b239dc3189dc697ed91bd6a\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d8aa4595c7c088876e8d349b817bdb\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d8aa4595c7c088876e8d349b817bdb\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\474a689798ab843029c3b1706caa25b0\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\474a689798ab843029c3b1706caa25b0\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfeea66209e4b1f108143c44d47bb08a\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfeea66209e4b1f108143c44d47bb08a\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\64cc707f832d1c4771a0527ab5968ba9\transformed\glide-plugin-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\64cc707f832d1c4771a0527ab5968ba9\transformed\glide-plugin-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b31fc8addf479ce1997bfb5b00efa6e\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b31fc8addf479ce1997bfb5b00efa6e\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c87ac20925a863f474dc9b953e673ca\transformed\awebp-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c87ac20925a863f474dc9b953e673ca\transformed\awebp-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\70fdfccc588f9321ea3d49b87ba858bb\transformed\apng-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\70fdfccc588f9321ea3d49b87ba858bb\transformed\apng-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3424c2fd71faf5e5b5167b3ce1f389a8\transformed\gif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3424c2fd71faf5e5b5167b3ce1f389a8\transformed\gif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1df82dfa871eece927dac1cd38803ae\transformed\avif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1df82dfa871eece927dac1cd38803ae\transformed\avif-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4b476bd3d63236221fa50b96965e8a3\transformed\frameanimation-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4b476bd3d63236221fa50b96965e8a3\transformed\frameanimation-3.0.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33edec337de23b6d7afccb07bf9c5a56\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33edec337de23b6d7afccb07bf9c5a56\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ef21dbb346e7cd0de80e551a2681b6e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ef21dbb346e7cd0de80e551a2681b6e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e44cf8f179dd9cc40ffc4aa5add7e55\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e44cf8f179dd9cc40ffc4aa5add7e55\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e425266743d78bd82b101ed6eacd4e9\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e425266743d78bd82b101ed6eacd4e9\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ee4aa3e9ef054ea30909d8286b16a3\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ee4aa3e9ef054ea30909d8286b16a3\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e7b9e7a66d1ecd12367a68b703474de\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e7b9e7a66d1ecd12367a68b703474de\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1da7927f873a411fb54b7b77f2e1995b\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1da7927f873a411fb54b7b77f2e1995b\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\334e915e1090d282f1372c2ee823f435\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\334e915e1090d282f1372c2ee823f435\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3f0422d9d6a0477fddeb479992ab832\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3f0422d9d6a0477fddeb479992ab832\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f9c9fae8352e7baf79e8aa03ee3b5ff\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f9c9fae8352e7baf79e8aa03ee3b5ff\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7685c2c6a1cd6c65622b16cf4b7b0bcd\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7685c2c6a1cd6c65622b16cf4b7b0bcd\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3974ea1fa6ae95ae0e48f28a28300ac\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3974ea1fa6ae95ae0e48f28a28300ac\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f0637a06d2bd18b5dc45a5cb644d9fa\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f0637a06d2bd18b5dc45a5cb644d9fa\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a08b4cd8d2d0635798883ef0caf16c8\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a08b4cd8d2d0635798883ef0caf16c8\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a932d8d9c77d055eecfc9d50e5584c97\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a932d8d9c77d055eecfc9d50e5584c97\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b9aab596c524bc904af394b58ce492a\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b9aab596c524bc904af394b58ce492a\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a0765da38a4bc9f174a555257d5eb09\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a0765da38a4bc9f174a555257d5eb09\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece8ff3fce1bd18ec182193249154aa7\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece8ff3fce1bd18ec182193249154aa7\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35871f0c249b283487af41b762edc9c1\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35871f0c249b283487af41b762edc9c1\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f53fc86618013be3473017fba58bd2\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f53fc86618013be3473017fba58bd2\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f198d0277aba890cd5594f78cc2a9ca\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f198d0277aba890cd5594f78cc2a9ca\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de1fe5e58f77e62da44b1ec0e8feff96\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de1fe5e58f77e62da44b1ec0e8feff96\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56ea8ea5280c784cd8f4638797d5ed10\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56ea8ea5280c784cd8f4638797d5ed10\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eff23ba191a43b757a0a228443ac73bb\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eff23ba191a43b757a0a228443ac73bb\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93af6480dfb89bd842051126a2578b5a\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93af6480dfb89bd842051126a2578b5a\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\133efea1024462122da8bb7b37f8b356\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\133efea1024462122da8bb7b37f8b356\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c084df09d92122a458663fe6ffcb5df\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c084df09d92122a458663fe6ffcb5df\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f997e077d97d5ba7bd1864c0ab9822\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f997e077d97d5ba7bd1864c0ab9822\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0ebd35670d77efa572b2d2e775067b1\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0ebd35670d77efa572b2d2e775067b1\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\799db128f658cb8ec4e4111102e8cc1c\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\799db128f658cb8ec4e4111102e8cc1c\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f86df5c6c29f2c13aa69652907495866\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f86df5c6c29f2c13aa69652907495866\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91ff427e00e9af2ad040fe5d990b099b\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91ff427e00e9af2ad040fe5d990b099b\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cdf07aa61e1df0d4b55d391cc94d05\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cdf07aa61e1df0d4b55d391cc94d05\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7461ce11257921df3bc43ba3b17edf47\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7461ce11257921df3bc43ba3b17edf47\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ff1451445a52c77065e078eb075821b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ff1451445a52c77065e078eb075821b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54565c25958a7f4b15edda7e7c438894\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54565c25958a7f4b15edda7e7c438894\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\622e2425e6e7f8fa39b69d11fc9d9be2\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\622e2425e6e7f8fa39b69d11fc9d9be2\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a26bc77b69995eee87f406079b5ce75f\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a26bc77b69995eee87f406079b5ce75f\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6ab924621b2f036ab2d3120d61b1cf2\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6ab924621b2f036ab2d3120d61b1cf2\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc1d002fd69ab43b19bc83b368833c0e\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc1d002fd69ab43b19bc83b368833c0e\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65d4ddd65ecd855ba77979c6c35172d9\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65d4ddd65ecd855ba77979c6c35172d9\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fa470005cfc34c029326d0c0c465254\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fa470005cfc34c029326d0c0c465254\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50f102e539d507ee7534b15f042ba5cb\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50f102e539d507ee7534b15f042ba5cb\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cdadffb8f8b405863102824e5feb13f\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cdadffb8f8b405863102824e5feb13f\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac19dc5a5207c2c98cebfdf8d05f66d1\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac19dc5a5207c2c98cebfdf8d05f66d1\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df4f7478d75e5fc452a628eeea7bd8fc\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df4f7478d75e5fc452a628eeea7bd8fc\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fee46ee68ab1504291923b8eb23245a5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fee46ee68ab1504291923b8eb23245a5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa13e8657fce0ddec4843a79409f337a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa13e8657fce0ddec4843a79409f337a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\645ca5691b753ff506b30dfcf922a75a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\645ca5691b753ff506b30dfcf922a75a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0fb56928e50de3ce5e04860e2a1ea6e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0fb56928e50de3ce5e04860e2a1ea6e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fdcbb7eb51ef8e1bfa08105cbda6005\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fdcbb7eb51ef8e1bfa08105cbda6005\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8b4693213e13d15211b76916a14a86c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8b4693213e13d15211b76916a14a86c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\907660c1eed9db1fbe306717f8018b8a\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\907660c1eed9db1fbe306717f8018b8a\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\054fe50356e6fc46219a3df1f0c26342\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\054fe50356e6fc46219a3df1f0c26342\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94de804fa8d1513a5090fa9cbd9ac284\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94de804fa8d1513a5090fa9cbd9ac284\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e06c1a83d91b43e66ff956d7af621262\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e06c1a83d91b43e66ff956d7af621262\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84b542bd85cb3df645b3b30e5fb720fa\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84b542bd85cb3df645b3b30e5fb720fa\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b7e9a33af5a30767aaf80618b1f92d8\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b7e9a33af5a30767aaf80618b1f92d8\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fffe61cf8789e2cf7cf2d11dc79095e8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fffe61cf8789e2cf7cf2d11dc79095e8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8674f204c6eca6eaf65aad3a1a8b910\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8674f204c6eca6eaf65aad3a1a8b910\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b74851ffeb8d9a462885e4f257d01c0e\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b74851ffeb8d9a462885e4f257d01c0e\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5964219d36e5b7d52648a071b70b5cd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5964219d36e5b7d52648a071b70b5cd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\04e54340660830c32eee28badf0296dd\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\04e54340660830c32eee28badf0296dd\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c53d07303439f8e066007b090b8453b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c53d07303439f8e066007b090b8453b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e379020a267009186dab9b942fa6fc99\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e379020a267009186dab9b942fa6fc99\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\90e210e969d5cb571ac2d79399939d6b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\90e210e969d5cb571ac2d79399939d6b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d123fe5796fed332bb6d367efa4916a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d123fe5796fed332bb6d367efa4916a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\86b24f0bc2049a1cfdf63053979f6c1a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\86b24f0bc2049a1cfdf63053979f6c1a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba6e8ebe91b6b316e13fa0c2021f49df\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba6e8ebe91b6b316e13fa0c2021f49df\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d0bf68e7122ef278630ebe7463ebf92\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d0bf68e7122ef278630ebe7463ebf92\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fa3f20ae2eed2574420e15e5e9c535\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fa3f20ae2eed2574420e15e5e9c535\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6d5546b7d03dd9341fbc65b877f56fa\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6d5546b7d03dd9341fbc65b877f56fa\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dd5076bcb66e9b842e24100d682b7b1\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dd5076bcb66e9b842e24100d682b7b1\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\ckt_web\townexpo\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\ckt_web\townexpo\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\ckt_web\townexpo\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\ckt_web\townexpo\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a26b5f2cc61db919d909417f2609434\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a26b5f2cc61db919d909417f2609434\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c828745ef19678d71760f37c2f995e97\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c828745ef19678d71760f37c2f995e97\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad506c59ed829706853f415989d4ff0e\transformed\expo.modules.blur-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad506c59ed829706853f415989d4ff0e\transformed\expo.modules.blur-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7699414200306d2418f81e61bc3463f\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7699414200306d2418f81e61bc3463f\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe931e5948208a1eb6a2669e553f6627\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe931e5948208a1eb6a2669e553f6627\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a354129a802daa996d09d38a03470bb\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a354129a802daa996d09d38a03470bb\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\019fbd4571fa72f7d92bd7da6eff94f3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\019fbd4571fa72f7d92bd7da6eff94f3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af2eed43806ad0c03354c8a90f3130f1\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af2eed43806ad0c03354c8a90f3130f1\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ca26bc7ceb4c96a70355493aa4a4b33\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ca26bc7ceb4c96a70355493aa4a4b33\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbbd42d578db9754127eff2d0850d066\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbbd42d578db9754127eff2d0850d066\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4283c846aac54d9c544dfb1a29f46af7\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4283c846aac54d9c544dfb1a29f46af7\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f448083111b7cd846b6fdfc7adbb3e45\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f448083111b7cd846b6fdfc7adbb3e45\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17231d9788bbda824baa8689fb0f19df\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17231d9788bbda824baa8689fb0f19df\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87a020a61d364b36d092a54ed20cd4ed\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87a020a61d364b36d092a54ed20cd4ed\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f71fa759416485b618a7790c45fe6ffd\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f71fa759416485b618a7790c45fe6ffd\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\307710da9674a4305503d85af5febdbe\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\307710da9674a4305503d85af5febdbe\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04210093dcd5e70600dcb4b9187f667f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04210093dcd5e70600dcb4b9187f667f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f77330de1c2ec0312b5be11acb9c409a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f77330de1c2ec0312b5be11acb9c409a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b6f773569a47427a567d5b6041be05e\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b6f773569a47427a567d5b6041be05e\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e2816fafe5f3d07e7950eda52686a1d\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e2816fafe5f3d07e7950eda52686a1d\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c091eeb76134ad2931f57fb3344091a0\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c091eeb76134ad2931f57fb3344091a0\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2745e58e193a6e9f92fa81c5980ea3\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2745e58e193a6e9f92fa81c5980ea3\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b336d76ccf6b45156fd6aa6c568d91ab\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b336d76ccf6b45156fd6aa6c568d91ab\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7260aa4853075dc3a3daa140ed849a4\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7260aa4853075dc3a3daa140ed849a4\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2532bec72fa4263c034c15098814dfc2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2532bec72fa4263c034c15098814dfc2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1558d5233106c167f46c6d493462ef6d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1558d5233106c167f46c6d493462ef6d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a95c313c9c4557c601aaf659b17372b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a95c313c9c4557c601aaf659b17372b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [:shopify_flash-list] C:\ckt_web\townexpo\node_modules\@shopify\flash-list\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shopify_flash-list] C:\ckt_web\townexpo\node_modules\@shopify\flash-list\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a89f88737dcaf880ea21b9fdcb02480\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a89f88737dcaf880ea21b9fdcb02480\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e034a32d50552180691625f26b2a57\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e034a32d50552180691625f26b2a57\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\25e9ad356f5d45298c5a28aadcc3cf0e\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\25e9ad356f5d45298c5a28aadcc3cf0e\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\099a42e66893b10cc59c759614bf353c\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.13\transforms\099a42e66893b10cc59c759614bf353c\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa65e09bc3810e2ab00dbe80065ced04\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa65e09bc3810e2ab00dbe80065ced04\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9732a0b964bbab8793beb16c8023a40\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:20:9-67
	android:targetSdkVersion
		INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:12:5-79
MERGED from [BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:12:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
	android:name
		ADDED from [:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e034a32d50552180691625f26b2a57\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e034a32d50552180691625f26b2a57\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e034a32d50552180691625f26b2a57\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:11:9-13:42
	android:value
		ADDED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:13:13-39
	android:name
		ADDED from [host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:12:13-64
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
	android:theme
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\474a689798ab843029c3b1706caa25b0\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\474a689798ab843029c3b1706caa25b0\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
action#android.intent.action.GET_CONTENT
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
category#android.intent.category.OPENABLE
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
	android:grantUriPermissions
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
service#expo.modules.location.services.LocationTaskService
ADDED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-53
	android:name
		ADDED from [:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-78
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0aa71dffcf4388e5c02a8fd5166892f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
activity#com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity
ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
	android:screenOrientation
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
	android:exported
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:19:13-42
	android:name
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a354129a802daa996d09d38a03470bb\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a354129a802daa996d09d38a03470bb\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.townapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.townapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
