import { createUser } from '@/models/User';
import { useMutation, useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';

import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { ThemedText } from '../../../components/ui/ThemedText';
import { ThemedView } from '../../../components/ui/ThemedView';
import { useAuth } from '../../../contexts/AuthContext';
import { useZone } from '../../../contexts/ZoneContext';
import { GET_CATEGORIES, GET_HAPPENING, GET_PLACES, UPDATE_HAPPENING } from '../../../lib/graphql-operations';
import { createCategories, createHappening, createPlaces } from '../../../models';

export default function EditHappeningScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { currentZone, loading: zoneLoading } = useZone();
  const { user } = useAuth();

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(new Date().getTime() + 2 * 60 * 60 * 1000));
  const [selectedPlace, setSelectedPlace] = useState<string | null>(null);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Query happening details
  const { data: happeningData, loading: happeningLoading, error: happeningError } = useQuery(GET_HAPPENING, {
    variables: { id },
    skip: !id,
  });

  // Query categories
  const { data: categoriesData, loading: categoriesLoading } = useQuery(GET_CATEGORIES, {
    variables: {
      criteria: {
        zone: currentZone?.id || '',
        ctype: { $in: ['event', 'all'] }
      }
    },
    skip: !currentZone?.id,
  });

  // Query places
  const { data: placesData, loading: placesLoading } = useQuery(GET_PLACES, {
    variables: {
      criteria: {
        zone: currentZone?.id || '',
      },
      limit: 100,
      skip: 0,
    },
    skip: !currentZone?.id,
  });

  // Update happening mutation
  const [updateHappening, { loading: updateLoading, error: updateError }] = useMutation(UPDATE_HAPPENING);

  // Process happening data
  const userModel = user ? createUser(user) : null;
  const happening = happeningData?.happening ? createHappening(happeningData.happening) : null;
  const canEdit = userModel && (userModel.isAdmin() || (userModel.id === happening?.userId));

  // Process categories
  const categories = categoriesData?.categories
    ? createCategories(categoriesData.categories)
    : [];

  // Process places
  const places = placesData?.places
    ? createPlaces(placesData.places)
    : [];

  // Initialize form with happening data
  useEffect(() => {
    if (happening) {
      setTitle(happening.title);
      setDescription(happening.desc || '');

      if (happening.when?.start) {
        setStartDate(new Date(happening.when.start));
      }

      if (happening.when?.end) {
        setEndDate(new Date(happening.when.end));
      }

      if (happening.placeId) {
        setSelectedPlace(happening.placeId);
      }

      if (happening.categories && happening.categories.length > 0) {
        setSelectedCategories(happening.categories);
      }
    }
  }, [happening]);

  // Handle category selection
  const toggleCategory = (categoryId: string) => {
    if (selectedCategories.includes(categoryId)) {
      setSelectedCategories(selectedCategories.filter(id => id !== categoryId));
    } else {
      setSelectedCategories([...selectedCategories, categoryId]);
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Handle date changes
  const onStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      setStartDate(selectedDate);

      // If end date is before start date, update it
      if (endDate < selectedDate) {
        setEndDate(new Date(selectedDate.getTime() + 2 * 60 * 60 * 1000));
      }
    }
  };

  const onEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setEndDate(selectedDate);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a title for the event');
      return;
    }

    if (!selectedPlace) {
      Alert.alert('Error', 'Please select a place for the event');
      return;
    }

    if (endDate < startDate) {
      Alert.alert('Error', 'End date cannot be before start date');
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await updateHappening({
        variables: {
          id,
          input: {
            title,
            desc: description,
            when: {
              start: startDate.toISOString(),
              end: endDate.toISOString()
            },
            placeId: selectedPlace,
            categories: selectedCategories,
          }
        }
      });

      if (result.data?.updateHappening) {
        Alert.alert('Success', 'Event updated successfully', [
          { text: 'OK', onPress: () => router.push(`/happening/${id}`) }
        ]);
      }
    } catch (error) {
      console.error('Error updating happening:', error);
      Alert.alert('Error', 'Failed to update event. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check permissions
  useEffect(() => {
    if (happening && user && !canEdit) {
      Alert.alert('Permission Denied', 'You do not have permission to edit this event', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    }
  }, [happening, user, canEdit]);

  // Loading state
  if (happeningLoading || zoneLoading || categoriesLoading || placesLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>Loading...</ThemedText>
      </ThemedView>
    );
  }

  // Error state
  if (happeningError) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          Error loading event: {happeningError.message}
        </ThemedText>
        <Button title="Go Back" onPress={() => router.back()} style={styles.errorButton} />
      </ThemedView>
    );
  }

  // Not found state
  if (!happening) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>Event not found</ThemedText>
        <Button title="Go Back" onPress={() => router.back()} style={styles.errorButton} />
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}
    >
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <ThemedText variant="title" style={styles.screenTitle}>Edit Event</ThemedText>

        <Card style={styles.formSection}>
          <ThemedText variant="subtitle">Event Details</ThemedText>

          <ThemedText style={styles.label}>Title *</ThemedText>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="Enter event title"
            maxLength={100}
          />

          <ThemedText style={styles.label}>Description</ThemedText>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Enter event description"
            multiline
            numberOfLines={4}
            maxLength={1000}
          />

          <ThemedText style={styles.label}>Start Date & Time *</ThemedText>
          <TouchableOpacity
            style={styles.datePickerButton}
            onPress={() => setShowStartDatePicker(true)}
          >
            <ThemedText>{formatDate(startDate)}</ThemedText>
            <Ionicons name="calendar-outline" size={20} color="#666" />
          </TouchableOpacity>

          <ThemedText style={styles.label}>End Date & Time *</ThemedText>
          <TouchableOpacity
            style={styles.datePickerButton}
            onPress={() => setShowEndDatePicker(true)}
          >
            <ThemedText>{formatDate(endDate)}</ThemedText>
            <Ionicons name="calendar-outline" size={20} color="#666" />
          </TouchableOpacity>

          {showStartDatePicker && (
            <DateTimePicker
              value={startDate}
              mode="datetime"
              display="default"
              onChange={onStartDateChange}
            />
          )}

          {showEndDatePicker && (
            <DateTimePicker
              value={endDate}
              mode="datetime"
              display="default"
              onChange={onEndDateChange}
              minimumDate={startDate}
            />
          )}
        </Card>

        <Card style={styles.formSection}>
          <ThemedText variant="subtitle">Location</ThemedText>
          <ThemedText style={styles.label}>Place *</ThemedText>
          <ScrollView style={styles.placesContainer} horizontal={false}>
            {places.map(place => (
              <TouchableOpacity
                key={place.id}
                style={[
                  styles.placeItem,
                  selectedPlace === place.id && styles.selectedPlaceItem
                ]}
                onPress={() => setSelectedPlace(place.id)}
              >
                <ThemedText style={styles.placeName}>{place.name}</ThemedText>
                {place.fullLocation && (
                  <ThemedText style={styles.placeAddress}>
                    {place.fullLocation.street}, {place.fullLocation.city}
                  </ThemedText>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </Card>

        <Card style={styles.formSection}>
          <ThemedText variant="subtitle">Categories</ThemedText>
          <ThemedText style={styles.helperText}>Select categories that apply to your event</ThemedText>
          <View style={styles.categoriesContainer}>
            {categories.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryItem,
                  selectedCategories.includes(category.id) && styles.selectedCategoryItem
                ]}
                onPress={() => toggleCategory(category.id)}
              >
                <ThemedText
                  style={[
                    styles.categoryText,
                    selectedCategories.includes(category.id) && styles.selectedCategoryText
                  ]}
                >
                  {category.title}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={() => router.back()}
            style={styles.button}
          />
          <Button
            title={isSubmitting ? 'Saving...' : 'Save Changes'}
            onPress={handleSubmit}
            disabled={isSubmitting || !title || !selectedPlace}
            style={styles.button}
          />
        </View>

        {updateError && (
          <ThemedText style={styles.errorText}>
            Error: {updateError.message}
          </ThemedText>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorButton: {
    minWidth: 120,
  },
  screenTitle: {
    marginBottom: 16,
  },
  formSection: {
    marginBottom: 16,
    padding: 16,
  },
  label: {
    marginTop: 12,
    marginBottom: 4,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    marginBottom: 12,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 12,
    marginBottom: 12,
  },
  placesContainer: {
    maxHeight: 200,
    marginTop: 8,
  },
  placeItem: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    marginBottom: 8,
  },
  selectedPlaceItem: {
    borderColor: '#2196F3',
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  placeName: {
    fontWeight: '500',
  },
  placeAddress: {
    fontSize: 12,
    marginTop: 4,
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
    marginBottom: 8,
    opacity: 0.7,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  categoryItem: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedCategoryItem: {
    borderColor: '#2196F3',
    backgroundColor: '#2196F3',
  },
  categoryText: {
    fontSize: 12,
  },
  selectedCategoryText: {
    color: 'white',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
});
