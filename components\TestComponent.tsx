import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

/**
 * A simple test component to verify rendering is working
 */
export function TestComponent() {
  console.log('TestComponent rendering');
  
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Test Component</Text>
      <Text style={styles.subText}>If you can see this, rendering is working!</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    margin: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
        pointerEvents: 'auto'

  },
  text: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subText: {
    fontSize: 16,
    color: '#333',
  },
});
