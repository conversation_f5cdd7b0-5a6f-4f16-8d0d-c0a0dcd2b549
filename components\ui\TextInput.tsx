import { useState } from 'react';
import { TextInput as RNTextInput, StyleSheet, Text, TextInputProps, View, ViewStyle } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface CustomTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
}

export function TextInput({
  label,
  error,
  containerStyle,
  style,
  onFocus,
  onBlur,
  ...props
}: CustomTextInputProps) {
  const theme = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  
  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus && onFocus(e);
  };
  
  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur && onBlur(e);
  };
  
  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text
          style={[
            styles.label,
            {
              color: error
                ? theme.colors.error
                : isFocused
                ? theme.colors.primary
                : theme.colors.textSecondary,
              fontFamily: theme.typography.families.medium,
            },
          ]}
        >
          {label}
        </Text>
      )}
      
      <RNTextInput
        style={[
          styles.input,
          {
            borderColor: error
              ? theme.colors.error
              : isFocused
              ? theme.colors.primary
              : theme.colors.border,
            color: theme.colors.text,
            backgroundColor: theme.colors.backgroundSecondary,
            fontFamily: theme.typography.families.regular,
          },
          style,
        ]}
        placeholderTextColor={theme.colors.textSecondary}
        onFocus={handleFocus}
        onBlur={handleBlur}
        {...props}
      />
      
      {error && (
        <Text
          style={[
            styles.error,
            {
              color: theme.colors.error,
              fontFamily: theme.typography.families.regular,
            },
          ]}
        >
          {error}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
        pointerEvents: 'auto'

  },
  label: {
    fontSize: 14,
    marginBottom: 6,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  error: {
    fontSize: 12,
    marginTop: 4,
  },
});
