import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export type MessageStatusType = 'sending' | 'sent' | 'delivered' | 'read' | 'error';

interface MessageStatusProps {
  status: MessageStatusType;
  size?: number;
}

/**
 * A component that displays the status of a message
 */
const MessageStatus: React.FC<MessageStatusProps> = ({ 
  status, 
  size = 14 
}) => {
  const { theme } = useTheme();
  
  // Determine the icon and color based on the status
  const getStatusIcon = () => {
    switch (status) {
      case 'sending':
        return {
          name: 'time-outline',
          color: theme.colors.text + '80', // 50% opacity
        };
      case 'sent':
        return {
          name: 'checkmark',
          color: theme.colors.text + '80', // 50% opacity
        };
      case 'delivered':
        return {
          name: 'checkmark-done',
          color: theme.colors.text + '80', // 50% opacity
        };
      case 'read':
        return {
          name: 'checkmark-done',
          color: theme.colors.primary,
        };
      case 'error':
        return {
          name: 'alert-circle',
          color: theme.colors.error,
        };
      default:
        return {
          name: 'checkmark',
          color: theme.colors.text + '80', // 50% opacity
        };
    }
  };
  
  const { name, color } = getStatusIcon();
  
  return (
    <View style={styles.container}>
      <Ionicons name={name} size={size} color={color} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginLeft: 4,
        pointerEvents: 'auto'

  },
});

export default MessageStatus;
