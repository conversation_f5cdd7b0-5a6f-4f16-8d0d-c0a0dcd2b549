import { useQuery } from '@apollo/client';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, TouchableOpacity } from 'react-native';
import { Avatar } from '../../components/ui/Avatar';
import { Button } from '../../components/ui/Button';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { GET_INCOMING_FRIEND_REQUESTS, GET_OUTGOING_FRIEND_REQUESTS } from '../../lib/graphql-operations';
import { formatRelativeTime } from '../../utils/date';

export default function FriendRequestsScreen() {
  const { currentUser } = useAuth();
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState<'incoming' | 'outgoing'>('incoming');
  const [refreshing, setRefreshing] = useState(false);

  // Query incoming friend requests
  const { 
    data: incomingData, 
    loading: incomingLoading, 
    error: incomingError, 
    refetch: refetchIncoming 
  } = useQuery(GET_INCOMING_FRIEND_REQUESTS);

  // Query outgoing friend requests
  const { 
    data: outgoingData, 
    loading: outgoingLoading, 
    error: outgoingError, 
    refetch: refetchOutgoing 
  } = useQuery(GET_OUTGOING_FRIEND_REQUESTS);

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      if (activeTab === 'incoming') {
        await refetchIncoming();
      } else {
        await refetchOutgoing();
      }
    } catch (error) {
      console.error('Error refreshing friend requests:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle tab change
  const handleTabChange = (tab: 'incoming' | 'outgoing') => {
    setActiveTab(tab);
  };

  // Handle user press
  const handleUserPress = (userId: string) => {
    router.push(`/profile/${userId}`);
  };

  // Get the appropriate data based on active tab
  const loading = activeTab === 'incoming' ? incomingLoading : outgoingLoading;
  const error = activeTab === 'incoming' ? incomingError : outgoingError;
  const data = activeTab === 'incoming' ? incomingData : outgoingData;
  const requests = data?.requests || [];

  // Render request item
  const renderRequestItem = ({ item }: { item: any }) => {
    const user = activeTab === 'incoming' ? item.requester : item.target;
    
    return (
      <TouchableOpacity
        style={styles.requestItem}
        onPress={() => handleUserPress(user.id)}
      >
        <Avatar
          source={user.userProfile?.avatar}
          name={user.userProfile ? 
            `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim() :
            user.emails?.[0]?.address?.split('@')[0]
          }
          size="medium"
          online={user.isOnline}
        />
        <ThemedView style={styles.requestInfo}>
          <ThemedText variant="subtitle">{user.userProfile ? 
            `${user.userProfile.firstName || ''} ${user.userProfile.lastName || ''}`.trim() :
            user.emails?.[0]?.address?.split('@')[0]
          }</ThemedText>
          <ThemedText variant="caption">{formatRelativeTime(item.createdAt)}</ThemedText>
        </ThemedView>
      </TouchableOpacity>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <ThemedView style={styles.emptyState}>
      <ThemedText>No {activeTab} friend requests</ThemedText>
    </ThemedView>
  );

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <ThemedText variant="title">Friend Requests</ThemedText>
      </ThemedView>

      <ThemedView style={styles.tabs}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'incoming' && styles.activeTab,
          ]}
          onPress={() => handleTabChange('incoming')}
        >
          <ThemedText
            style={[
              styles.tabText,
              activeTab === 'incoming' && styles.activeTabText,
            ]}
          >
            Incoming
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'outgoing' && styles.activeTab,
          ]}
          onPress={() => handleTabChange('outgoing')}
        >
          <ThemedText
            style={[
              styles.tabText,
              activeTab === 'outgoing' && styles.activeTabText,
            ]}
          >
            Outgoing
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>

      {loading && !data ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <ThemedText style={styles.loadingText}>Loading friend requests...</ThemedText>
        </ThemedView>
      ) : error ? (
        <ThemedView style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>
            Error loading friend requests: {error.message}
          </ThemedText>
          <Button title="Retry" onPress={onRefresh} style={styles.retryButton} />
        </ThemedView>
      ) : (
        <FlatList
          data={requests}
          keyExtractor={(item) => item.id}
          renderItem={renderRequestItem}
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={styles.listContent}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
        pointerEvents: 'auto'

  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    marginRight: 16,
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#0a7ea4',
  },
  tabText: {
    fontSize: 16,
  },
  activeTabText: {
    fontWeight: 'bold',
    color: '#0a7ea4',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  listContent: {
    flexGrow: 1,
  },
  requestItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  requestInfo: {
    marginLeft: 12,
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
});
