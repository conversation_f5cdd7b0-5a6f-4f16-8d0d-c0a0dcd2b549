import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Category } from '../../types';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface CategoryFilterProps {
  categories: Category[];
  selectedCategories: string[];
  onSelectCategory: (categoryId: string) => void;
}

export function CategoryFilter({
  categories,
  selectedCategories,
  onSelectCategory,
}: CategoryFilterProps) {
  const theme = useTheme();

  // Get icon name for category
  const getCategoryIcon = (category: Category) => {
    if (category.iconName) {
      return category.iconName;
    }

    // Default icons based on category type
    switch (category.ctype.toLowerCase()) {
      case 'food':
      case 'restaurant':
        return 'restaurant';
      case 'shopping':
      case 'retail':
        return 'cart';
      case 'entertainment':
        return 'film';
      case 'service':
        return 'briefcase';
      case 'landmark':
      case 'attraction':
        return 'location';
      default:
        return 'list';
    }
  };

  // Get color for category
  const getCategoryColor = (category: Category) => {
    // Check if category is selected
    const isSelected = selectedCategories.includes(category.id);

    // Return selected or unselected color
    return isSelected ? theme.colors.primary : theme.colors.text;
  };

  // Get background color for category
  const getCategoryBackgroundColor = (category: Category) => {
    // Check if category is selected
    const isSelected = selectedCategories.includes(category.id);

    // Return selected or unselected background color
    return isSelected ? `${theme.colors.primary}20` : 'transparent';
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              { backgroundColor: getCategoryBackgroundColor(category) },
            ]}
            onPress={() => onSelectCategory(category.id)}
          >
            <Ionicons
              name={getCategoryIcon(category) as any}
              size={20}
              color={getCategoryColor(category)}
            />
            <ThemedText
              style={[
                styles.categoryText,
                { color: getCategoryColor(category) },
              ]}
            >
              {category.title}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 16,
    left: 16,
    right: 16,
    borderRadius: 8,
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
        pointerEvents: 'auto'

  },
  scrollContent: {
    paddingVertical: 4,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  categoryText: {
    marginLeft: 4,
    fontSize: 14,
  },
});
