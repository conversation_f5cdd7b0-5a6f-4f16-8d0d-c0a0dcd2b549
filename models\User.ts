import { Email, Place, User as UserInterface } from '../types';
import { UserProfile, UserProfileData } from './UserProfile';


export function createUser(data: UserInterface): User {
  return new User(data);
}


// Friend request interface
export interface FriendRequest {
  id: string;
  linkedObjectId: string; // Target user ID
  objectType: string;     // Always "users"
  type: string;           // Always "friend"
  requesterId: string;    // Requesting user ID
  createdAt?: string;
  updatedAt?: string;
  status?: string;        // 'pending', 'accepted', 'rejected'
  requester?: UserData;   // The user who sent the request
  target?: UserData;      // The user who received the request
}

// Friend relationship interface
export interface Friend {
  id: string;
  userId: string;
  friendId: string;
  createdAt: string;
  updatedAt?: string;
  friend?: UserData;      // The friend user object
}

// Base interface for the raw data
export interface UserData {
  id: string;
  userProfile?: UserProfileData;
  status?: string;
  lastOnline?: string;
  zone?: string;
  categories?: string[];
  visible?: boolean;
  createdAt?: string;
  roles?: string[];
  friendRequests?: FriendRequest[];
  friends?: Friend[];
  blockedByMe?: boolean;
  emails?: Email[]; // Added emails property to User type
}

// Class that wraps the data and provides methods
export class User {
  id: string;
  userProfile?: UserProfile;
  status?: string;
  lastOnline?: string;
  zone?: string;
  categories?: string[];
  visible?: boolean;
  createdAt: string;
  roles?: string[];
  friendRequests?: FriendRequest[];
  friends?: Friend[];
  blockedByMe?: boolean;
  emails?: Email[]; // Added emails property to User type

  constructor(data: UserData) {
    this.id = data.id;
    this.userProfile = data.userProfile ? new UserProfile(data.userProfile) : undefined;
    this.status = data.status;
    this.lastOnline = data.lastOnline;
    this.zone = data.zone;
    this.categories = data.categories;
    this.visible = data.visible;
    this.createdAt = data.createdAt || new Date().toISOString();
    this.roles = data.roles;
    this.friendRequests = data.friendRequests;
    this.friends = data.friends;
    this.blockedByMe = data.blockedByMe;
    this.emails = data.emails;
  }

  // Method to get human-readable name (previously treated as a field)
  humanName(): string {
    // Then try profile's fullName
    if (this.userProfile) {
      const fullName = this.userProfile.fullName();
      if (fullName) {
        return fullName;
      }
    }
    // No email property on User, fallback only
    return 'Unnamed user';
  }

  // Method to get display name (similar to the Meteor version)
  displayName(): string {
    // This mimics the Meteor User.displayName() method
    return this.humanName();
  }

  // Method to check if user has a specific role
  hasRole(role: string): boolean {
    return this.roles?.includes(role) || false;
  }

  // --- Role-based permission methods ---
  isAdmin(): boolean {
    return this.roles?.includes('admin') || false;
  }

  isCityManager(zoneId?: string): boolean {
    if (!this.roles?.includes('cityManager') || !zoneId) return false;
    return (this.zone === zoneId) || this.isAdmin();
  }

  isPlaceOwner(zoneId?: string): boolean {
    if (!this.roles?.includes('placeOwner') || !zoneId) return false;
    return (this.zone === zoneId) || this.isAdmin();
  }

  isPlaceMember(place?: Place): boolean {
    if (!place) return false;
    return !!place.members?.some((m: { id: string }) => m.id === this.id);
  }

  isUser(): boolean {
    return !!this.id;
  }

  isAnonymous(): boolean {
    return !this.id;
  }

  // Method to check if user is friends with another user
  isFriendsWith(userId: string): boolean {
    return this.friends?.some(friend => friend.friendId === userId) || false;
  }

  // Method to check if user has a pending friend request from another user
  hasPendingRequestFrom(userId: string): boolean {
    return this.friendRequests?.some(
      request => request.requesterId === userId && request.objectType === 'users' && request.type === 'friend'
    ) || false;
  }

  // Method to check if user has sent a pending friend request to another user
  hasSentRequestTo(userId: string): boolean {
    return this.friendRequests?.some(
      request => request.linkedObjectId === userId && request.requesterId === this.id &&
                request.objectType === 'users' && request.type === 'friend'
    ) || false;
  }

  // Method to get all friend requests sent by this user
  getSentFriendRequests(): FriendRequest[] {
    return this.friendRequests?.filter(
      request => request.requesterId === this.id && request.objectType === 'users' && request.type === 'friend'
    ) || [];
  }

  // Method to get all friend requests received by this user
  getReceivedFriendRequests(): FriendRequest[] {
    return this.friendRequests?.filter(
      request => request.linkedObjectId === this.id && request.objectType === 'users' && request.type === 'friend'
    ) || [];
  }

  // Method to find online status:
  isOnline(): boolean {
    return this.status === 'online';
  }

  // Method to get avatar URL (if available)
  avatarURL(size?: 'square' | 'wide'): string {
    if (this.userProfile?.avatar) {
      return this.userProfile.avatarURL(size);
    }

    // Default avatar
    const defaultAvatar = process.env.NEXT_PUBLIC_DEFAULT_AVATAR || '/default-avatar.png';

    // Apply transformations based on size
    let url = defaultAvatar;
    if (size === 'wide') {
      url = url.replace('/upload/', '/upload/c_fill,g_auto,h_320,w_1000/');
    } else if (size === 'square') {
      url = url.replace('/upload/', '/upload/c_fill,g_auto,h_400,w_400/');
    }

    return url;
  }
}
