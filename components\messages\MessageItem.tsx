import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Message } from '../../types';
import { Avatar } from '../ui/Avatar';
import { ThemedText } from '../ui/ThemedText';
import MessageStatus from './MessageStatus';

interface MessageItemProps {
  message: Message;
  isOwnMessage: boolean;
}

const MessageItem: React.FC<MessageItemProps> = ({ message, isOwnMessage }) => {
  const { theme } = useTheme();

  // Format timestamp
  const formatTime = (date: string | Date) => {
    const now = new Date();
    const messageDate = new Date(date);

    // If the message is from today, show only the time
    if (
      messageDate.getDate() === now.getDate() &&
      messageDate.getMonth() === now.getMonth() &&
      messageDate.getFullYear() === now.getFullYear()
    ) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // If the message is from this year, show the date without the year
    if (messageDate.getFullYear() === now.getFullYear()) {
      return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' }) +
        ' ' +
        messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // Otherwise, show the full date
    return messageDate.toLocaleDateString() +
      ' ' +
      messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <View style={[
      styles.container,
      isOwnMessage ? styles.ownMessageContainer : styles.otherMessageContainer
    ]}>
      {!isOwnMessage && (
        <Avatar
          source={message.sender.userProfile?.avatar}
          name={message.sender.userProfile ?
            `${message.sender.userProfile.firstName || ''} ${message.sender.userProfile.lastName || ''}`.trim() :
            message.sender.emails?.[0]?.address?.split('@')[0]
          }
          size="small"
          style={styles.avatar}
        />
      )}
      <View style={[
        styles.messageContent,
        isOwnMessage
          ? [styles.ownMessage, { backgroundColor: theme.colors.primary }]
          : [styles.otherMessage, { backgroundColor: theme.colors.card }]
      ]}>
        <ThemedText style={[
          styles.messageText,
          isOwnMessage && { color: '#ffffff' }
        ]}>
          {message.content}
        </ThemedText>
        <View style={styles.messageFooter}>
          <ThemedText style={[
            styles.timestamp,
            isOwnMessage && { color: 'rgba(255, 255, 255, 0.7)' }
          ]}>
            {formatTime(message.createdAt)}
          </ThemedText>

          {isOwnMessage && (
            <MessageStatus
              status={message.read ? 'read' : message.id.startsWith('temp-') ? 'sending' : 'delivered'}
              size={12}
            />
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginVertical: 5,
    maxWidth: '80%',
        pointerEvents: 'auto'

  },
  ownMessageContainer: {
    alignSelf: 'flex-end',
  },
  otherMessageContainer: {
    alignSelf: 'flex-start',
  },
  avatar: {
    marginRight: 8,
    alignSelf: 'flex-end',
  },
  messageContent: {
    borderRadius: 16,
    padding: 10,
    paddingHorizontal: 12,
  },
  ownMessage: {
    borderBottomRightRadius: 4,
  },
  otherMessage: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 2,
  },
  timestamp: {
    fontSize: 11,
    opacity: 0.7,
  },
});

export default MessageItem;
