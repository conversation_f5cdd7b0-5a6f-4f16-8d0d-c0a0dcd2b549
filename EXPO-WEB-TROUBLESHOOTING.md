# Expo Web Troubleshooting Guide

This document provides solutions for common issues when running the Expo app on web, particularly related to GraphQL and other package compatibility problems.

## Common Issues

1. **GraphQL Module Resolution Error**:
   ```
   While trying to resolve module 'graphql' from file 'node_modules/graphql-tag/lib/index.js',
   the package 'node_modules/graphql/package.json' was successfully found.
   However, this package itself specifies a 'main' module field that could not be resolved.
   ```

2. **AJV Package Error**: Issues related to the ajv package during build.

3. **React Native Maps on Web**: Compatibility issues with react-native-maps when running on web.

4. **Asset Resolution Error**:
   ```
   Unable to resolve "missing-asset-registry-path" from "node_modules\@expo\metro-runtime\assets\chevron-left.png"
   ```

5. **Require.Context Error**:
   ```
   The experimental Metro feature `require.context` is not enabled in your project.
   ```

## Quick Fix

If you encounter build issues, try these commands:

```bash
# Clear Metro cache and restart the web app
npm run web-reset
```

If you encounter specific errors, you can run these targeted fixes:

```bash
# Fix the missing asset registry issue
npm run fix-assets

# Fix the require.context issue
npm run fix-require-context

# Fix all known issues
npm run fix-all

# Clear Metro cache and restart the web app
npm run web-reset
```

If that doesn't work, try:

```bash
# Install required dependencies (if not already installed)
npm install @babel/plugin-transform-runtime react-native-svg-transformer --save-dev

# Clear Metro cache
npm run clear-cache

# Fix asset registry issues
npm run fix-assets

# Start the web app with a clean cache
npm run web-clean
```


3. **Asset Handling**: We've added proper asset handling:
   - Created a placeholder for the missing asset registry path
   - Added SVG transformer for better SVG support
   - Configured asset plugins for proper asset hashing

4. **Platform-Specific Components**: We've created web-specific versions of native components:
   - `MapViewWrapper.web.tsx` - A web-friendly version of react-native-maps

5. **Cache Clearing**: We've added scripts to clear the Metro cache and restart the app with a clean slate.

## Manual Troubleshooting Steps

If you're still experiencing issues:

### 1. Check Package Versions

```bash
# Check GraphQL version
npm ls graphql

# Check other key dependencies
npm ls @apollo/client react-native-maps
```

### 2. Reinstall Dependencies

```bash
rm -rf node_modules
npm install
```

### 3. Clear All Caches

```bash
# Clear Metro cache
npm run clear-cache

# Clear npm cache
npm cache clean --force

# Clear watchman cache (if installed)
watchman watch-del-all
```

### 4. Check Metro Configuration

Make sure your Metro config includes support for all necessary file extensions:

```js
sourceExts: ['js', 'jsx', 'ts', 'tsx', 'mjs', 'cjs'],
```

### 5. Check Babel Configuration

Ensure your Babel config includes the necessary plugins and aliases.

### 6. Inspect Network Requests

When running on web, use browser developer tools to inspect network requests and console errors for more specific troubleshooting.

## Additional Resources

- [Expo Documentation on Metro Configuration](https://docs.expo.dev/guides/customizing-metro/)
- [GraphQL GitHub Issues](https://github.com/graphql/graphql-js/issues)
- [Metro Bundler Documentation](https://facebook.github.io/metro/)
- [React Native Maps Documentation](https://github.com/react-native-maps/react-native-maps)
