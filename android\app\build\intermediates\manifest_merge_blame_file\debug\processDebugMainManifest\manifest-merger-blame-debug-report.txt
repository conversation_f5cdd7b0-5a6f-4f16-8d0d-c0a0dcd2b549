1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.townapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:8:3-75
11-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:8:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:2:3-78
12-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:3:3-76
13-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.CAMERA" />
14-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:4:3-62
14-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:4:20-60
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:5:3-64
15-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:5:20-62
16    <uses-permission
16-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:6:3-77
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:6:20-75
18        android:maxSdkVersion="32" />
18-->[BareExpo:expo.modules.image:2.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3da9f0510c69585b356824038b3a6d4f\transformed\expo.modules.image-2.1.7\AndroidManifest.xml:17:9-35
19    <uses-permission android:name="android.permission.RECORD_AUDIO" />
19-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:7:3-68
19-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:7:20-66
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:9:3-63
20-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:9:20-61
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:10:3-78
21-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:10:20-76
22
23    <queries>
23-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:11:3-17:13
24        <intent>
24-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:12:5-16:14
25            <action android:name="android.intent.action.VIEW" />
25-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:13:7-58
25-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:13:15-56
26
27            <category android:name="android.intent.category.BROWSABLE" />
27-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:14:7-67
27-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:14:17-65
28
29            <data android:scheme="https" />
29-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:15:7-37
29-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:15:13-35
30        </intent>
31        <intent>
31-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
32
33            <!-- Required for opening tabs if targeting API 30 -->
34            <action android:name="android.support.customtabs.action.CustomTabsService" />
34-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
34-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
35        </intent>
36        <intent>
36-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
37
38            <!-- Required for picking images from the camera roll if targeting API 30 -->
39            <action android:name="android.media.action.IMAGE_CAPTURE" />
39-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
39-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
40        </intent>
41        <intent>
41-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
42
43            <!-- Required for picking images from the camera if targeting API 30 -->
44            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
44-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
44-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
45        </intent>
46        <intent>
46-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
47            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
47-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
47-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
48        </intent>
49        <intent>
49-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
50            <action android:name="android.intent.action.GET_CONTENT" />
50-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
50-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
51
52            <category android:name="android.intent.category.OPENABLE" />
52-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
52-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
53
54            <data android:mimeType="*/*" />
54-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:15:7-37
55        </intent> <!-- Query open documents -->
56        <intent>
56-->[:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
57            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
57-->[:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
57-->[:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
61-->[:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
61-->[:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
62    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
62-->[:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
62-->[:react-native-community_netinfo] C:\ckt_web\townexpo\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
63    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
63-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
63-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
64    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
64-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
64-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27b4d8b3dc714a89d9adc0d83f296ead\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
65    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
65-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
65-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
66    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
66-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
66-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
67    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
67-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
67-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
68    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
68-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
68-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
69
70    <permission
70-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
71        android:name="com.townapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
72        android:protectionLevel="signature" />
72-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
73
74    <uses-permission android:name="com.townapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
74-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
74-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
75    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
76    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
77    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
78    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
79    <!-- for Samsung -->
80    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
80-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
80-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
81    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
81-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
81-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
82    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
83    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
83-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
83-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
84    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
84-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
84-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
85    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
85-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
85-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
86    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
87    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
88    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
89    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
90    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
91    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
92    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
93    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
94    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
95    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\db1ae7ff6b98087cd5431acfde8f8f53\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
96    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
96-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
96-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1a13bba2b08943526c0f7ddf937e361\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
97
98    <application
98-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:3-36:17
99        android:name="com.townapp.MainApplication"
99-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:16-47
100        android:allowBackup="true"
100-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:162-188
101        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
101-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
102        android:debuggable="true"
103        android:extractNativeLibs="false"
104        android:icon="@mipmap/ic_launcher"
104-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:81-115
105        android:label="@string/app_name"
105-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:48-80
106        android:roundIcon="@mipmap/ic_launcher_round"
106-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:116-161
107        android:supportsRtl="true"
107-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:221-247
108        android:theme="@style/AppTheme"
108-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:18:189-220
109        android:usesCleartextTraffic="true" >
109-->C:\ckt_web\townexpo\android\app\src\debug\AndroidManifest.xml:6:18-53
110        <meta-data
110-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:19:5-82
111            android:name="expo.modules.updates.ENABLED"
111-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:19:16-59
112            android:value="true" />
112-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:19:60-80
113        <meta-data
113-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:20:5-119
114            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
114-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:20:16-72
115            android:value="@string/expo_runtime_version" />
115-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:20:73-117
116        <meta-data
116-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:21:5-105
117            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
117-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:21:16-80
118            android:value="ALWAYS" />
118-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:21:81-103
119        <meta-data
119-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:22:5-99
120            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
120-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:22:16-79
121            android:value="0" />
121-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:22:80-97
122        <meta-data
122-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:23:5-120
123            android:name="expo.modules.updates.EXPO_UPDATE_URL"
123-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:23:16-67
124            android:value="https://u.expo.dev/your-project-id" />
124-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:23:68-118
125
126        <activity
126-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:5-35:16
127            android:name="com.townapp.MainActivity"
127-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:15-43
128            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
128-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:44-134
129            android:exported="true"
129-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:256-279
130            android:launchMode="singleTask"
130-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:135-166
131            android:screenOrientation="portrait"
131-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:280-316
132            android:theme="@style/Theme.App.SplashScreen"
132-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:210-255
133            android:windowSoftInputMode="adjustResize" >
133-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:24:167-209
134            <intent-filter>
134-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:25:7-28:23
135                <action android:name="android.intent.action.MAIN" />
135-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:26:9-60
135-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:26:17-58
136
137                <category android:name="android.intent.category.LAUNCHER" />
137-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:27:9-68
137-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:27:19-66
138            </intent-filter>
139            <intent-filter>
139-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:29:7-34:23
140                <action android:name="android.intent.action.VIEW" />
140-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:13:7-58
140-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:13:15-56
141
142                <category android:name="android.intent.category.DEFAULT" />
142-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:31:9-67
142-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:31:19-65
143                <category android:name="android.intent.category.BROWSABLE" />
143-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:14:7-67
143-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:14:17-65
144
145                <data android:scheme="townapp" />
145-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:15:7-37
145-->C:\ckt_web\townexpo\android\app\src\main\AndroidManifest.xml:15:13-35
146            </intent-filter>
147        </activity>
148
149        <provider
149-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
150            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
150-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
151            android:authorities="com.townapp.fileprovider"
151-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
152            android:exported="false"
152-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
153            android:grantUriPermissions="true" >
153-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
154            <meta-data
154-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
155                android:name="android.support.FILE_PROVIDER_PATHS"
155-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
156                android:resource="@xml/file_provider_paths" />
156-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
157        </provider>
158
159        <meta-data
159-->[:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
160            android:name="org.unimodules.core.AppLoader#react-native-headless"
160-->[:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
161            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
161-->[:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
162        <meta-data
162-->[:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
163            android:name="com.facebook.soloader.enabled"
163-->[:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
164            android:value="true" />
164-->[:expo-modules-core] C:\ckt_web\townexpo\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
165
166        <activity
166-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
167            android:name="com.facebook.react.devsupport.DevSettingsActivity"
167-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
168            android:exported="false" />
168-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
169
170        <meta-data
170-->[host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:11:9-13:42
171            android:name="com.google.mlkit.vision.DEPENDENCIES"
171-->[host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:12:13-64
172            android:value="barcode_ui" />
172-->[host.exp.exponent:expo.modules.camera:16.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e35c4c22e201433acbd3c2de2e03f8\transformed\expo.modules.camera-16.1.6\AndroidManifest.xml:13:13-39
173
174        <service
174-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
175            android:name="com.google.android.gms.metadata.ModuleDependencies"
175-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
176            android:enabled="false"
176-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
177            android:exported="false" >
177-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
178            <intent-filter>
178-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
179                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
179-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
179-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
180            </intent-filter>
181
182            <meta-data
182-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
183                android:name="photopicker_activity:0:required"
183-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
184                android:value="" />
184-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
185        </service>
186
187        <activity
187-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
188            android:name="com.canhub.cropper.CropImageActivity"
188-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
189            android:exported="true"
189-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
190            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
190-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
191        <provider
191-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
192            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
192-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
193            android:authorities="com.townapp.ImagePickerFileProvider"
193-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
194            android:exported="false"
194-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
195            android:grantUriPermissions="true" >
195-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7716641a13144969537ab0584e7eb08\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
196            <meta-data
196-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
197                android:name="android.support.FILE_PROVIDER_PATHS"
197-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
198                android:resource="@xml/image_picker_provider_paths" />
198-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
199        </provider>
200
201        <uses-library
201-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
202            android:name="androidx.camera.extensions.impl"
202-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
203            android:required="false" />
203-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76eab2f225ac584c687df4f442603578\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
204
205        <service
205-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
206            android:name="androidx.camera.core.impl.MetadataHolderService"
206-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
207            android:enabled="false"
207-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
208            android:exported="false" >
208-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
209            <meta-data
209-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
210                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
210-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
211                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
211-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f98773c0256f32efd92f65dad7d3cf\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
212        </service>
213
214        <provider
214-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
215            android:name="com.canhub.cropper.CropFileProvider"
215-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
216            android:authorities="com.townapp.cropper.fileprovider"
216-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
217            android:exported="false"
217-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
218            android:grantUriPermissions="true" >
218-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e78917444daee60c1b50720bcd9758c\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
219            <meta-data
219-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
220                android:name="android.support.FILE_PROVIDER_PATHS"
220-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
221                android:resource="@xml/library_file_paths" />
221-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
222        </provider>
223        <provider
223-->[:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
224            android:name="expo.modules.filesystem.FileSystemFileProvider"
224-->[:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
225            android:authorities="com.townapp.FileSystemFileProvider"
225-->[:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
226            android:exported="false"
226-->[:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
227            android:grantUriPermissions="true" >
227-->[:expo-file-system] C:\ckt_web\townexpo\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
228            <meta-data
228-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
229                android:name="android.support.FILE_PROVIDER_PATHS"
229-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
230                android:resource="@xml/file_system_provider_paths" />
230-->[:react-native-webview] C:\ckt_web\townexpo\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
231        </provider>
232
233        <meta-data
233-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
234            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
234-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
235            android:value="GlideModule" />
235-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b67bd1ca35a7055243ea40a2ad28ea9d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
236
237        <service
237-->[:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-14:56
238            android:name="expo.modules.location.services.LocationTaskService"
238-->[:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-78
239            android:exported="false"
239-->[:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
240            android:foregroundServiceType="location" />
240-->[:expo-location] C:\ckt_web\townexpo\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-53
241        <service
241-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
242            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
242-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
243            android:exported="false" >
243-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
244            <intent-filter android:priority="-1" >
244-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
244-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
245                <action android:name="com.google.firebase.MESSAGING_EVENT" />
245-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
245-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
246            </intent-filter>
247        </service>
248
249        <receiver
249-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
250            android:name="expo.modules.notifications.service.NotificationsService"
250-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
251            android:enabled="true"
251-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
252            android:exported="false" >
252-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
253            <intent-filter android:priority="-1" >
253-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
253-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
254                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
254-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
254-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
255                <action android:name="android.intent.action.BOOT_COMPLETED" />
255-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
255-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
256                <action android:name="android.intent.action.REBOOT" />
256-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
256-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
257                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
257-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
257-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
258                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
258-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
258-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
259                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
259-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
259-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
260            </intent-filter>
261        </receiver>
262
263        <activity
263-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
264            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
264-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
265            android:excludeFromRecents="true"
265-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
266            android:exported="false"
266-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
267            android:launchMode="standard"
267-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
268            android:noHistory="true"
268-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
269            android:taskAffinity=""
269-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
270            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
270-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
271
272        <receiver
272-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
273            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
273-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
274            android:exported="true"
274-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
275            android:permission="com.google.android.c2dm.permission.SEND" >
275-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
276            <intent-filter>
276-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
277                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
277-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
277-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
278            </intent-filter>
279
280            <meta-data
280-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
281                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
281-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
282                android:value="true" />
282-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
283        </receiver>
284        <!--
285             FirebaseMessagingService performs security checks at runtime,
286             but set to not exported to explicitly avoid allowing another app to call it.
287        -->
288        <service
288-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
289            android:name="com.google.firebase.messaging.FirebaseMessagingService"
289-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
290            android:directBootAware="true"
290-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
291            android:exported="false" >
291-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
292            <intent-filter android:priority="-500" >
292-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
292-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
293                <action android:name="com.google.firebase.MESSAGING_EVENT" />
293-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
293-->[:expo-notifications] C:\ckt_web\townexpo\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
294            </intent-filter>
295        </service>
296        <service
296-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
297            android:name="com.google.firebase.components.ComponentDiscoveryService"
297-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
298            android:directBootAware="true"
298-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
299            android:exported="false" >
299-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
300            <meta-data
300-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
301                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
301-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
302                android:value="com.google.firebase.components.ComponentRegistrar" />
302-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
303            <meta-data
303-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
304                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
304-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
305                android:value="com.google.firebase.components.ComponentRegistrar" />
305-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d83d30fadfe1225cf463285dd1620102\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
306            <meta-data
306-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
307                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
307-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
308                android:value="com.google.firebase.components.ComponentRegistrar" />
308-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
309            <meta-data
309-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
310                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
310-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
311                android:value="com.google.firebase.components.ComponentRegistrar" />
311-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c90f44cf5c6ef61f7262b3be46bec35\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
312            <meta-data
312-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
313                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
313-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
314                android:value="com.google.firebase.components.ComponentRegistrar" />
314-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb4765c74392a565f58dc0824988cb1\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
315            <meta-data
315-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
316                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
316-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
317                android:value="com.google.firebase.components.ComponentRegistrar" />
317-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
318            <meta-data
318-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
319                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
319-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
320                android:value="com.google.firebase.components.ComponentRegistrar" />
320-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df3b74462828e04016b7b9dd9dd44665\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
321        </service>
322        <!--
323        This activity is an invisible delegate activity to start scanner activity
324        and receive result, so it's unnecessary to support screen orientation and
325        we can avoid any side effect from activity recreation in any case.
326        -->
327        <activity
327-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
328            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
328-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
329            android:exported="false"
329-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
330            android:screenOrientation="portrait" >
330-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\547e13bd6a5692f0afca9f2673fa0dfd\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
331        </activity>
332
333        <service
333-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
334            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
334-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
335            android:directBootAware="true"
335-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
336            android:exported="false" >
336-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
337            <meta-data
337-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
338                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
338-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
339                android:value="com.google.firebase.components.ComponentRegistrar" />
339-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa21acc46dfabe1ba706f229fcc82f4f\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
340            <meta-data
340-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
341                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
341-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
342                android:value="com.google.firebase.components.ComponentRegistrar" />
342-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a90f26ed631d7b2d308f4f364a0b0776\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
343            <meta-data
343-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
344                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
344-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
345                android:value="com.google.firebase.components.ComponentRegistrar" />
345-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
346        </service>
347
348        <provider
348-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
349            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
349-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
350            android:authorities="com.townapp.mlkitinitprovider"
350-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
351            android:exported="false"
351-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
352            android:initOrder="99" />
352-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28fe9d453564e79a80c4dd085074de74\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
353
354        <activity
354-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
355            android:name="com.google.android.gms.common.api.GoogleApiActivity"
355-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
356            android:exported="false"
356-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
357            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
357-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac05835035c583740d1a4f8d61f55f0\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
358
359        <provider
359-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
360            android:name="com.google.firebase.provider.FirebaseInitProvider"
360-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
361            android:authorities="com.townapp.firebaseinitprovider"
361-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
362            android:directBootAware="true"
362-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
363            android:exported="false"
363-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
364            android:initOrder="100" />
364-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\808d6a7cb8cdb946291ca15f33f40e2e\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
365        <provider
365-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
366            android:name="androidx.startup.InitializationProvider"
366-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
367            android:authorities="com.townapp.androidx-startup"
367-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
368            android:exported="false" >
368-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
369            <meta-data
369-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
370                android:name="androidx.emoji2.text.EmojiCompatInitializer"
370-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
371                android:value="androidx.startup" />
371-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
372            <meta-data
372-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
373                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
373-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
374                android:value="androidx.startup" />
374-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
375            <meta-data
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
376                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
377                android:value="androidx.startup" />
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
378        </provider>
379
380        <meta-data
380-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
381            android:name="com.google.android.gms.version"
381-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
382            android:value="@integer/google_play_services_version" />
382-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b7a95e3d9be8d22942c383e641eb5e\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
383
384        <receiver
384-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
385            android:name="androidx.profileinstaller.ProfileInstallReceiver"
385-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
386            android:directBootAware="false"
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
387            android:enabled="true"
387-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
388            android:exported="true"
388-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
389            android:permission="android.permission.DUMP" >
389-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
390            <intent-filter>
390-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
391                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
391-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
391-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
392            </intent-filter>
393            <intent-filter>
393-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
394                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
394-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
394-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
395            </intent-filter>
396            <intent-filter>
396-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
397                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
397-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
397-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
398            </intent-filter>
399            <intent-filter>
399-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
400                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
400-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
400-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9de15b90a7f0a6d40646090412a46c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
401            </intent-filter>
402        </receiver>
403
404        <service
404-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
405            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
405-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
406            android:exported="false" >
406-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
407            <meta-data
407-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
408                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
408-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
409                android:value="cct" />
409-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd11b06b3961a67cba7f301497b979e3\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
410        </service>
411        <service
411-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
412            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
412-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
413            android:exported="false"
413-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
414            android:permission="android.permission.BIND_JOB_SERVICE" >
414-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
415        </service>
416
417        <receiver
417-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
418            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
418-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
419            android:exported="false" />
419-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\42cbea8a1c2c64de92f3f2d18a7c4905\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
420    </application>
421
422</manifest>
