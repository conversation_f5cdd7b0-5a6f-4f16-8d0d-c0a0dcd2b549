import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import { KeyboardAvoidingView, Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { actions, RichEditor, RichToolbar } from 'react-native-pell-rich-editor';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';
import { EntitySearch } from './EntitySearch';

interface RichTextEditorProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (text: string) => void;
  onBlur?: () => void;
  height?: number;
  minHeight?: number;
  maxHeight?: number;
  containerStyle?: any;
  editorStyle?: any;
  toolbarStyle?: any;
  disabled?: boolean;
  showToolbar?: boolean;
  showEntityLinking?: boolean;
}

export function RichTextEditor({
  initialContent = '',
  placeholder = 'Start typing...',
  onChange,
  onBlur,
  height = 200,
  minHeight = 100,
  maxHeight = 400,
  containerStyle,
  editorStyle,
  toolbarStyle,
  disabled = false,
  showToolbar = true,
  showEntityLinking = true,
}: RichTextEditorProps) {
  const richText = useRef<RichEditor>(null);
  const [content, setContent] = useState(initialContent);
  const [showEntitySearch, setShowEntitySearch] = useState(false);
  const theme = useTheme();

  useEffect(() => {
    if (initialContent !== content) {
      setContent(initialContent);
    }
  }, [initialContent]);

  const handleChange = (text: string) => {
    setContent(text);
    if (onChange) {
      onChange(text);
    }
  };

  const handleBlur = () => {
    if (onBlur) {
      onBlur();
    }
  };

  const handleInsertLink = () => {
    // Show custom link dialog
    setShowEntitySearch(true);
  };

  const handleInsertEntity = (entity: any) => {
    if (!richText.current) return;

    const entityType = entity.title ? 'happening' : 
                      entity.name ? 'place' : 
                      entity.userProfile ? 'user' : 'post';
    
    const entityName = entity.title || entity.name || 
                      (entity.userProfile ? 
                        `${entity.userProfile.firstName || ''} ${entity.userProfile.lastName || ''}`.trim() : 
                        entity.content || 'Link');
    
    const entityLink = `/${entityType}/${entity.id}`;
    
    // Insert link at current cursor position
    richText.current.insertLink(entityLink, entityName);
    setShowEntitySearch(false);
  };

  const handleCancelEntitySearch = () => {
    setShowEntitySearch(false);
  };

  // Custom link button for the toolbar
  const renderLinkButton = () => {
    return (
      <TouchableOpacity
        style={styles.toolbarButton}
        onPress={handleInsertLink}
        disabled={disabled}
      >
        <Ionicons name="link-outline" size={20} color={theme.colors.text} />
      </TouchableOpacity>
    );
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, containerStyle]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      {showToolbar && (
        <RichToolbar
          editor={richText}
          selectedIconTint={theme.colors.primary}
          iconTint={theme.colors.text}
          actions={[
            actions.setBold,
            actions.setItalic,
            actions.setUnderline,
            actions.heading1,
            actions.heading2,
            actions.insertBulletsList,
            actions.insertOrderedList,
            actions.insertLink,
            actions.keyboard,
            actions.setStrikethrough,
          ]}
          iconMap={{
            [actions.heading1]: ({ tintColor }) => (
              <ThemedText style={[styles.toolbarIcon, { color: tintColor }]}>H1</ThemedText>
            ),
            [actions.heading2]: ({ tintColor }) => (
              <ThemedText style={[styles.toolbarIcon, { color: tintColor }]}>H2</ThemedText>
            ),
          }}
          style={[styles.toolbar, toolbarStyle]}
          disabled={disabled}
        />
      )}

      {showEntityLinking && (
        <View style={styles.entityLinkingContainer}>
          <TouchableOpacity
            style={styles.entityLinkingButton}
            onPress={handleInsertLink}
            disabled={disabled}
          >
            <Ionicons name="link-outline" size={16} color={theme.colors.primary} />
            <ThemedText style={styles.entityLinkingText}>Link to Entity</ThemedText>
          </TouchableOpacity>
        </View>
      )}

      <RichEditor
        ref={richText}
        initialContentHTML={content}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        initialHeight={height}
        editorStyle={{
          backgroundColor: theme.colors.card,
          color: theme.colors.text,
          placeholderColor: theme.colors.textLight,
          contentCSSText: `
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
            font-size: 16px;
            padding: 10px;
          `,
          ...editorStyle,
        }}
        disabled={disabled}
        style={styles.editor}
      />

      {showEntitySearch && (
        <ThemedView style={styles.entitySearchContainer}>
          <EntitySearch
            onSelect={handleInsertEntity}
            onCancel={handleCancelEntitySearch}
          />
        </ThemedView>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    minHeight: 200,
        pointerEvents: 'auto'

  },
  toolbar: {
    backgroundColor: 'transparent',
  },
  editor: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginVertical: 5,
  },
  toolbarIcon: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  toolbarButton: {
    padding: 8,
  },
  entityLinkingContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingVertical: 4,
  },
  entityLinkingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  entityLinkingText: {
    marginLeft: 4,
    fontSize: 12,
  },
  entitySearchContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
});
