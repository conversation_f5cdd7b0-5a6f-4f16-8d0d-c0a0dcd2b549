import React from 'react';
import { Image, StyleSheet, TouchableOpacity, View, useWindowDimensions } from 'react-native';
import RenderHtml from 'react-native-render-html';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface ImageListItemProps {
  id: string;
  title: string;
  description?: string;
  avatarURL?: string;
  linkPath?: string;
  iconName?: string;
  onPress?: () => void;
  style?: any;
  editButton?: React.ReactNode;
}

export function ImageListItem({
  id,
  title,
  description,
  avatarURL,
  linkPath,
  iconName,
  onPress,
  style,
  editButton,
}: ImageListItemProps) {
  const theme = useTheme();
  const { width } = useWindowDimensions();
  console.log('ImageListItem for:', title, ' avatar:',avatarURL);
  // Map web icon names to Expo/RN compatible icons
  const getIconEmoji = () => {
    switch (iconName) {
      case 'faLocationDot':
      case 'faMapMarker':
        return '🏬';
      case 'faTag':
        return '🏷️';
      case 'faCalendarDays':
        return '📅';
      case 'faNewspaper':
        return '📰';
      case 'faFile':
        return '📄';
      case 'faPeopleGroup':
        return '👪';
      default:
        return '📦';
    }
  };
  
  // Render HTML content if description contains HTML tags
  const renderDescription = () => {
    if (!description) return null;
    
    const containsHtml = /<[a-z][\s\S]*>/i.test(description);
    
    if (containsHtml) {
      return (
        <RenderHtml
          contentWidth={width - 32} // Account for padding
          source={{ html: description }}
          tagsStyles={{
            body: { color: theme.colors.text },
            p: { margin: 0, padding: 0 },
          }}
        />
      );
    }
    
    return (
      <ThemedText numberOfLines={3} style={styles.description}>
        {description}
      </ThemedText>
    );
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Image/Icon Container - Full Width */}
      <View style={styles.imageContainer}>
        {avatarURL ? (
          <Image
            source={{ uri: avatarURL }}
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <ThemedView style={styles.placeholderImage}>
            <ThemedText style={styles.emojiIcon}>{getIconEmoji()}</ThemedText>
          </ThemedView>
        )}
      </View>
      
      {/* Content Container */}
      <ThemedView style={styles.content}>
        <ThemedText style={styles.title} numberOfLines={1}>
          {title}
        </ThemedText>
        {renderDescription()}
      </ThemedView>
      
      {/* Edit Button - if provided */}
      {editButton && (
        <ThemedView style={styles.editButtonContainer}>
          {editButton}
        </ThemedView>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 16,
    flexDirection: 'column', // Changed to column for vertical layout
    height: 300, // Fixed height to match web version
        pointerEvents: 'auto'

  },
  imageContainer: {
    width: '100%',
    height: 180, // Taller image area to match web version
  },
  image: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emojiIcon: {
    fontSize: 48, // Large emoji
  },
  content: {
    padding: 16,
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 8,
    fontSize: 16,
    color: '#5a67d8', // Indigo color to match web version
  },
  description: {
    fontSize: 14,
    opacity: 0.8,
  },
  editButtonContainer: {
    padding: 16,
    paddingTop: 0,
  }
});


