import { StyleSheet } from 'react-native';
import { lightTheme } from './theme';

// Shared styles for detail and list screens
export const sharedStyles = StyleSheet.create({
  container: {
    flex: 1,
    pointerEvents: 'auto',
    backgroundColor: lightTheme.colors.background,
  },
  contentContainer: {
    padding: 16,
    pointerEvents: 'auto',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: lightTheme.colors.error,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  imagesContainer: {
    marginBottom: 16,
    maxHeight: 200,
  },
  image: {
    width: 280,
    height: 200,
    borderRadius: 8,
    marginRight: 8,
  },
  noImageContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginBottom: 16,
  },
  header: {
    marginBottom: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
  },
  description: {
    marginTop: 8,
    lineHeight: 24,
  },
  address: {
    marginTop: 8,
  },
  mapContainer: {
    marginTop: 8,
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    marginBottom: 16,
  },
  tag: {
    backgroundColor: 'rgba(10, 126, 164, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  avatar: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
  },
  category: {
    marginTop: 4,
  },
  eventItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  offerItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  date: {
    marginTop: 8,
  },
  placeName: {
    fontWeight: 'bold',
    fontSize: 16,
    marginTop: 8,
  },
  validityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 8,
  },
  validBadge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
  },
  validText: {
    fontWeight: 'bold',
    fontSize: 12,
    textAlign: 'center',
  },
  daysRemaining: {
    marginTop: 4,
    fontSize: 12,
    color: '#888',
  },
  createButton: {
    marginTop: 8,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
    color: '#888',
  },
});
