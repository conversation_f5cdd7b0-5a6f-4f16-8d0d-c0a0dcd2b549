import React, { useState } from 'react';
import { Modal, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Button } from '../ui/Button';
import { ThemedText } from '../ui/ThemedText';
import { ThemedView } from '../ui/ThemedView';

interface RedeemModalProps {
  visible: boolean;
  onClose: () => void;
  onRedeem: (comment?: string) => void;
}

export function RedeemModal({ 
  visible, 
  onClose, 
  onRedeem
}: RedeemModalProps) {
  const [comment, setComment] = useState('');
  const theme = useTheme();

  const handleRedeem = () => {
    onRedeem(comment.trim() || undefined);
    setComment('');
  };

  const handleClose = () => {
    setComment('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={handleClose}
      >
        <ThemedView
          style={styles.container}
          onStartShouldSetResponder={() => true}
          onTouchEnd={(e) => e.stopPropagation()}
        >
          <ThemedText variant="subtitle" style={styles.title}>
            Redeem Offer
          </ThemedText>
          
          <ThemedText style={styles.description}>
            You're about to redeem this offer. Would you like to add a comment?
          </ThemedText>
          
          <TextInput
            style={[
              styles.commentInput,
              { 
                backgroundColor: theme.colors.card,
                color: theme.colors.text,
                borderColor: theme.colors.border
              }
            ]}
            placeholder="Add a comment (optional)"
            placeholderTextColor={theme.colors.text + '80'}
            value={comment}
            onChangeText={setComment}
            multiline
          />
          
          <View style={styles.buttonContainer}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={handleClose}
              style={styles.button}
            />
            <Button
              title="Redeem"
              variant="primary"
              onPress={handleRedeem}
              style={styles.button}
            />
          </View>
        </ThemedView>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: '80%',
    padding: 20,
    borderRadius: 8,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
        pointerEvents: 'auto'

  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  description: {
    marginBottom: 16,
  },
  commentInput: {
    borderWidth: 1,
    borderRadius: 4,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    marginLeft: 8,
  },
});
