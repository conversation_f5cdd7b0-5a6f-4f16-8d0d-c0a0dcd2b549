import { Ionicons } from '@expo/vector-icons';
import { usePathname, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { ThemedText } from '../../components/ui/ThemedText';
import { ThemedView } from '../../components/ui/ThemedView';
import { useAuth } from '../../contexts/AuthContext';
import { useMap } from '../../contexts/MapContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useZone } from '../../contexts/ZoneContext';

export default function ResponsiveNav() {
  const { user: currentUser, isAuthenticated, isPlaceOwner, isAdmin, logout } = useAuth();
  const { currentZone } = useZone();
  const { showMap, setShowMap } = useMap();
  const theme = useTheme();
  const router = useRouter();
  const pathname = usePathname();

  // State for navigation
  const [pagesMenuName, setPagesMenuName] = useState('Explore');
  const [rootPageId, setRootPageId] = useState('');
  const [sideNavOpen, setSideNavOpen] = useState(false);
  const [meMenuOpen, setMeMenuOpen] = useState(false);

  // Determine if we're on mobile
  const windowWidth = Dimensions.get('window').width;
  const isMobile = windowWidth < 768;

  // Toggle map visibility
  const toggleMap = () => {
    setShowMap(!showMap);
  };

  // Handle back button click
  const handleBackClick = () => {
    router.back();
  };

  // Toggle side navigation
  const toggleSideNav = () => {
    setSideNavOpen(!sideNavOpen);
  };

  // Update pagesMenuName based on zone settings
  useEffect(() => {
    if (currentZone?.title) {
      setPagesMenuName(currentZone.title);
    }
    if (currentZone?.rootPage) {
      setRootPageId(currentZone.rootPage);
    }
  }, [currentZone]);

  // Define navigation items - all publicly accessible
  const navItems = [
    { href: '/', icon: 'home', label: 'Home', requiresAuth: false },
    { href: '/map', icon: 'map', label: 'Map', requiresAuth: false },
    { href: '/places', icon: 'location', label: 'Places', requiresAuth: false },
    { href: '/happenings', icon: 'calendar', label: 'Events', requiresAuth: false },
    { href: '/posts', icon: 'newspaper', label: 'News', requiresAuth: false },
    {
      href: '/page/'+rootPageId,
      icon: 'compass',
      label: 'About',
      requiresAuth: false,
    },
  ];

  // Items only shown to authenticated users
  const authItems = [
    { href: '/campaigns', icon: 'trophy', label: 'Campaigns' },
    { href: '/users', icon: 'people', label: 'People' },
    { href: '/offers', icon: 'pricetag', label: 'Offers' },
    { href: '/messages', icon: 'chatbubble', label: 'Messages' },
  ];

  // Admin items
  const adminItems = [
    { href: '/pages', icon: 'document-text', label: 'Pages' },
  ];

  // City Manager items
  const cityManagerItems = [
    { href: '/pages', icon: 'document-text', label: 'Pages' },
  ];

  // Check if user has a specific role
  const hasRole = (role: string) => {
    return currentUser?.roles?.includes(role) || false;
  };

  // Define the navigation item type
  interface NavItem {
    href: string;
    icon: string;
    label: string;
    requiresAuth?: boolean;
    requiredRoles?: string[];
  }

  // Handle navigation - no permission checks, just navigate
  const handleNavigation = (item: NavItem) => {
    // Simply navigate to the requested page
    // We only show navigation items that the user has permission to access
    router.push(item.href as any);
  };

  return (
    <>
      {/* Desktop Navigation - Horizontal */}
      {!isMobile && (
        <ThemedView style={styles.desktopNav}>
          <View style={styles.desktopNavContent}>
            <View style={styles.leftSection}>
              {/* Back button */}
              <TouchableOpacity
                onPress={handleBackClick}
                style={styles.iconButton}
              >
                <Ionicons name="chevron-back" size={24} color={theme.colors.primary} />
              </TouchableOpacity>

              {/* Navigation Items */}
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.navScroll}>
                {navItems.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.navItem, pathname === item.href && styles.activeNavItem]}
                    onPress={() => handleNavigation(item)}
                  >
                    <Ionicons name={item.icon as any} size={20} color={theme.colors.primary} style={styles.navIcon} />
                    <ThemedText style={styles.navText}>{item.label}</ThemedText>
                  </TouchableOpacity>
                ))}

                {isAuthenticated && (
                  <>
                    {authItems.map((item, index) => (
                      <TouchableOpacity
                        key={`auth-${index}`}
                        style={[styles.navItem, pathname === item.href && styles.activeNavItem]}
                        onPress={() => handleNavigation(item)}
                      >
                        <Ionicons name={item.icon as any} size={20} color={theme.colors.primary} style={styles.navIcon} />
                        <ThemedText style={styles.navText}>{item.label}</ThemedText>
                      </TouchableOpacity>
                    ))}

                    {(hasRole('admin') || hasRole('cityManager')) && (hasRole('admin') ? adminItems : cityManagerItems).map((item, index) => (
                      <TouchableOpacity
                        key={`admin-${index}`}
                        style={[styles.navItem, pathname === item.href && styles.activeNavItem]}
                        onPress={() => handleNavigation(item)}
                      >
                        <Ionicons name={item.icon as any} size={20} color={theme.colors.primary} style={styles.navIcon} />
                        <ThemedText style={styles.navText}>{item.label}</ThemedText>
                      </TouchableOpacity>
                    ))}
                  </>
                )}
              </ScrollView>
            </View>

            <View style={styles.rightSection}>
              {/* Map Toggle Button */}
              <TouchableOpacity
                onPress={toggleMap}
                style={styles.iconButton}
              >
                <Ionicons name={showMap ? "eye-off" : "eye"} size={24} color={theme.colors.primary} />
              </TouchableOpacity>

              {/* Auth Button */}
              {isAuthenticated ? (
                <View style={{ position: 'relative' }}>
                  <TouchableOpacity
                    style={styles.profileButton}
                    onPress={() => setMeMenuOpen(!meMenuOpen)}
                  >
                    <Ionicons name="person-circle" size={24} color={theme.colors.primary} />
                    <ThemedText style={styles.profileText}>
                      Me
                    </ThemedText>
                  </TouchableOpacity>
                  {meMenuOpen && (
                    <View style={[styles.dropdownMenu, { backgroundColor: theme.colors.background }]}>
                      <TouchableOpacity
                        style={styles.dropdownItem}
                        onPress={() => {
                          setMeMenuOpen(false);
                          router.push('/profile');
                        }}
                      >
                        <ThemedText style={styles.dropdownText}>My profile</ThemedText>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.dropdownItem}
                        onPress={async () => {
                          setMeMenuOpen(false);
                          await logout();
                        }}
                      >
                        <ThemedText style={[styles.dropdownText, { color: '#e53e3e' }]}>Signout</ThemedText>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.navItem}
                  onPress={() => router.push('/login')}
                >
                  <Ionicons name="log-in" size={20} color={theme.colors.primary} style={styles.navIcon} />
                  <ThemedText style={styles.navText}>Sign In</ThemedText>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </ThemedView>
      )}

      {/* Mobile Navigation - Top Bar */}
      {isMobile && (
        <ThemedView style={styles.mobileNav}>
          <View style={styles.mobileNavContent}>
            {/* Menu and Back Buttons */}
            <View style={styles.mobileLeftSection}>
              <TouchableOpacity
                onPress={toggleSideNav}
                style={styles.iconButton}
              >
                <Ionicons name="menu" size={24} color={theme.colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleBackClick}
                style={styles.iconButton}
              >
                <Ionicons name="chevron-back" size={24} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>

            {/* Zone Title */}
            <ThemedText style={styles.zoneTitle}>
              {currentZone?.title || 'TownExpo'}
            </ThemedText>

            {/* Map Toggle Button */}
            <TouchableOpacity
              onPress={toggleMap}
              style={styles.iconButton}
            >
              <Ionicons name={showMap ? "eye-off" : "eye"} size={24} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
        </ThemedView>
      )}

      {/* Side Navigation for Mobile */}
      {isMobile && sideNavOpen && (
        <View style={styles.sideNavOverlay}>
          <ThemedView style={styles.sideNav}>
            <View style={styles.sideNavHeader}>
              <ThemedText style={styles.sideNavTitle}>Menu</ThemedText>
              <TouchableOpacity
                onPress={() => setSideNavOpen(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.sideNavContent}>
              {navItems.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.sideNavItem, pathname === item.href && styles.activeSideNavItem]}
                  onPress={() => {
                    handleNavigation(item);
                    setSideNavOpen(false);
                  }}
                >
                  <Ionicons name={item.icon as any} size={20} color={theme.colors.primary} style={styles.sideNavIcon} />
                  <ThemedText style={styles.sideNavText}>{item.label}</ThemedText>
                </TouchableOpacity>
              ))}

              {isAuthenticated && (
                <>
                  {authItems.map((item, index) => (
                    <TouchableOpacity
                      key={`auth-${index}`}
                      style={[styles.sideNavItem, pathname === item.href && styles.activeSideNavItem]}
                      onPress={() => {
                        handleNavigation(item);
                        setSideNavOpen(false);
                      }}
                    >
                      <Ionicons name={item.icon as any} size={20} color={theme.colors.primary} style={styles.sideNavIcon} />
                      <ThemedText style={styles.sideNavText}>{item.label}</ThemedText>
                    </TouchableOpacity>
                  ))}

                  {(hasRole('admin') || hasRole('cityManager')) && (hasRole('admin') ? adminItems : cityManagerItems).map((item, index) => (
                    <TouchableOpacity
                      key={`admin-${index}`}
                      style={[styles.sideNavItem, pathname === item.href && styles.activeSideNavItem]}
                      onPress={() => {
                        handleNavigation(item);
                        setSideNavOpen(false);
                      }}
                    >
                      <Ionicons name={item.icon as any} size={20} color={theme.colors.primary} style={styles.sideNavIcon} />
                      <ThemedText style={styles.sideNavText}>{item.label}</ThemedText>
                    </TouchableOpacity>
                  ))}
                </>
              )}

              <View style={styles.sideNavDivider} />

              {isAuthenticated ? (
                <>
                  <TouchableOpacity
                    style={styles.sideNavItem}
                    onPress={() => {
                      router.push('/profile');
                      setSideNavOpen(false);
                    }}
                  >
                    <Ionicons name="person-circle" size={20} color={theme.colors.primary} style={styles.sideNavIcon} />
                    <ThemedText style={styles.sideNavText}>My Profile</ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.sideNavItem, styles.signOutButton]}
                    onPress={() => {
                      // Handle sign out
                      setSideNavOpen(false);
                    }}
                  >
                    <Ionicons name="log-out" size={20} color="#e53e3e" style={styles.sideNavIcon} />
                    <Text style={[styles.sideNavText, { color: '#e53e3e' }]}>Sign Out</Text>
                  </TouchableOpacity>
                </>
              ) : (
                <TouchableOpacity
                  style={styles.sideNavItem}
                  onPress={() => {
                    router.push('/login');
                    setSideNavOpen(false);
                  }}
                >
                  <Ionicons name="log-in" size={20} color={theme.colors.primary} style={styles.sideNavIcon} />
                  <ThemedText style={styles.sideNavText}>Sign In</ThemedText>
                </TouchableOpacity>
              )}
            </ScrollView>
          </ThemedView>

          {/* Tap outside to close */}
          <TouchableOpacity
            style={styles.sideNavBackdrop}
            onPress={() => setSideNavOpen(false)}
            activeOpacity={1}
          />
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  // Desktop Navigation
  desktopNav: {
    borderBottomWidth: 1,
    backgroundColor: '#ffffff',
    borderBottomColor: 'rgba(0,0,0,0.1)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    pointerEvents: 'auto'
  },
  desktopNavContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  navScroll: {
    flexDirection: 'row',
    marginLeft: 8,
  },
  navItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 4,
    borderRadius: 8,
  },
  activeNavItem: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  navIcon: {
    marginRight: 8,
  },
  navText: {
    fontSize: 14,
    fontWeight: '500',
  },
  iconButton: {
    padding: 8,
    borderRadius: 8,
  },
  profileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginLeft: 8,
  },
  profileText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },

  // Mobile Navigation
  mobileNav: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  mobileNavContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  mobileLeftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  zoneTitle: {
    fontSize: 16,
    fontWeight: '600',
  },

  // Side Navigation
  sideNavOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    flexDirection: 'row',
  },
  sideNav: {
    width: 280,
    height: '100%',
    zIndex: 1001,
  },
  sideNavBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  sideNavHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  sideNavTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  sideNavContent: {
    flex: 1,
  },
  sideNavItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  activeSideNavItem: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  sideNavIcon: {
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  sideNavText: {
    fontSize: 16,
  },
  sideNavDivider: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginVertical: 8,
  },
  signOutButton: {
    marginTop: 8,
  },
});
