import { Asset } from 'expo-asset';
import { Frequency, RRule } from 'rrule';
import { Category, Comment, Happening as HappeningInterface, Like, Place } from '../types';

/**
 * Happening class that implements the HappeningInterface and adds methods
 * that were incorrectly converted to fields in the original conversion
 */
export class Happening implements HappeningInterface {
  id!: string;
  title!: string;
  desc?: string;
  when?: {
    start: string;
    end: string;
  };
  address?: string;
  avatar?: string;
  categories?: string[];
  categoryDocs?: Category[];
  avatarURL?: string;
  placeDetails?: Place;
  zone: string = '';
  userId: string = '';
  createdAt: string = '';
  updatedAt?: string;
  likes?: Like[];
  comments?: Comment[];
  recurrence?: {
    frequency: string;
    interval: number;
    byWeekday?: number[];
    byMonthDay?: number[];
    byMonth?: number[];
    count?: number;
    until?: string;
    rrule?: string;
  };

  placeId?: string;

  constructor(happeningData: HappeningInterface) {
    Object.assign(this, happeningData);
  }

  /**
   * Get the URL for this happening
   * @returns The URL for the happening
   */
  linkToMe(): string {
    return `/happening/${this.id}`;
  }

  /**
   * Get the avatar URL for this happening
   * @returns The URL for the avatar image
   */
  avatarUrl(): string {
    if (this.avatar) {
      return this.ensureProperUrl(this.avatar);
    }
    // Dynamically resolve the URL for the default avatar image
    const defaultAvatar = Asset.fromModule(require('../assets/images/event-placeholder.png')).uri;
    return defaultAvatar;
  }

  /**
   * Format the date/time of the happening
   * @returns A formatted string with the date and time
   */
  formattedDateTime(): string {
    const dateStr = this.when?.start;
    if (!dateStr) {
      return '';
    }

    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Get the icon name for this happening based on its categories
   * @returns The name of the icon to use for this happening
   */
  iconName(): string {
    // If no categories or categoryDocs, return a default icon
    if ((!this.categories || this.categories.length === 0) &&
        (!this.categoryDocs || this.categoryDocs.length === 0)) {
      return 'faCalendarAlt';
    }

    // Try to get the icon from the first categoryDoc if available
    if (this.categoryDocs && this.categoryDocs.length > 0) {
      const firstCategoryDoc = this.categoryDocs[0];
      return firstCategoryDoc.iconName || 'faCalendarAlt';
    }

    // Default fallback
    return 'faCalendarAlt';
  }

  /**
   * Check if the happening has already occurred
   * @returns True if the happening date is in the past
   */
  isPast(): boolean {
    const endDateStr = this.when?.end;
    // If no end date, use start date
    const dateStr = endDateStr || this.when?.start;

    if (!dateStr) {
      return false;
    }

    // If this is a recurring event with no end date or future end date, it's not past
    if (this.recurrence && (!this.recurrence.until || new Date(this.recurrence.until) > new Date())) {
      return false;
    }

    const happeningDate = new Date(dateStr);
    const now = new Date();
    return happeningDate < now;
  }

  /**
   * Get the next occurrence of this happening
   * @returns The date of the next occurrence, or null if there are no more occurrences
   */
  getNextOccurrence(): Date | null {
    // If not recurring, just return the start date if it's in the future
    if (!this.recurrence) {
      const startDate = new Date(this.when?.start || '');
      return startDate > new Date() ? startDate : null;
    }

    // If we have an RRule string, use it directly
    if (this.recurrence.rrule) {
      try {
        const rule = RRule.fromString(this.recurrence.rrule);
        const nextDates = rule.after(new Date(), true);
        return nextDates ? new Date(nextDates) : null;
      } catch (error) {
        console.error('Error parsing RRule:', error);
        return null;
      }
    }

    // Otherwise, build the rule from the recurrence properties
    try {
      const startDate = new Date(this.when?.start || '');

      // Map frequency string to RRule frequency
      const frequencyMap: Record<string, Frequency> = {
        'DAILY': RRule.DAILY,
        'WEEKLY': RRule.WEEKLY,
        'MONTHLY': RRule.MONTHLY,
        'YEARLY': RRule.YEARLY,
      };

      const frequency = frequencyMap[this.recurrence.frequency] || RRule.DAILY;

      // Build options
      const options: any = {
        freq: frequency,
        interval: this.recurrence.interval || 1,
        dtstart: startDate,
      };

      // Add weekdays if specified
      if (this.recurrence.byWeekday && this.recurrence.byWeekday.length > 0) {
        options.byweekday = this.recurrence.byWeekday.map(day => {
          switch (day) {
            case 0: return RRule.SU;
            case 1: return RRule.MO;
            case 2: return RRule.TU;
            case 3: return RRule.WE;
            case 4: return RRule.TH;
            case 5: return RRule.FR;
            case 6: return RRule.SA;
            default: return RRule.MO;
          }
        });
      }

      // Add month days if specified
      if (this.recurrence.byMonthDay && this.recurrence.byMonthDay.length > 0) {
        options.bymonthday = this.recurrence.byMonthDay;
      }

      // Add months if specified
      if (this.recurrence.byMonth && this.recurrence.byMonth.length > 0) {
        options.bymonth = this.recurrence.byMonth;
      }

      // Add count if specified
      if (this.recurrence.count) {
        options.count = this.recurrence.count;
      }

      // Add until if specified
      if (this.recurrence.until) {
        options.until = new Date(this.recurrence.until);
      }

      // Create rule and get next occurrence
      const rule = new RRule(options);
      const nextDate = rule.after(new Date(), true);
      return nextDate ? new Date(nextDate) : null;
    } catch (error) {
      console.error('Error calculating next occurrence:', error);
      return null;
    }
  }

  /**
   * Get all future occurrences of this happening
   * @param limit Maximum number of occurrences to return
   * @returns Array of dates for future occurrences
   */
  getFutureOccurrences(limit: number = 10): Date[] {
    // If not recurring, just return the start date if it's in the future
    if (!this.recurrence) {
      const startDate = new Date(this.when?.start || '');
      return startDate > new Date() ? [startDate] : [];
    }

    try {
      // Get the next occurrence
      const nextOccurrence = this.getNextOccurrence();
      if (!nextOccurrence) {
        return [];
      }

      // If we have an RRule string, use it directly
      if (this.recurrence.rrule) {
        try {
          const rule = RRule.fromString(this.recurrence.rrule);
          return rule.between(new Date(), new Date(9999, 11, 31), true, limit);
        } catch (error) {
          console.error('Error parsing RRule:', error);
          return [nextOccurrence];
        }
      }

      // Otherwise, build the rule from the recurrence properties
      const startDate = new Date(this.when?.start || '');

      // Map frequency string to RRule frequency
      const frequencyMap: Record<string, Frequency> = {
        'DAILY': RRule.DAILY,
        'WEEKLY': RRule.WEEKLY,
        'MONTHLY': RRule.MONTHLY,
        'YEARLY': RRule.YEARLY,
      };

      const frequency = frequencyMap[this.recurrence.frequency] || RRule.DAILY;

      // Build options
      const options: any = {
        freq: frequency,
        interval: this.recurrence.interval || 1,
        dtstart: startDate,
      };

      // Add weekdays if specified
      if (this.recurrence.byWeekday && this.recurrence.byWeekday.length > 0) {
        options.byweekday = this.recurrence.byWeekday.map(day => {
          switch (day) {
            case 0: return RRule.SU;
            case 1: return RRule.MO;
            case 2: return RRule.TU;
            case 3: return RRule.WE;
            case 4: return RRule.TH;
            case 5: return RRule.FR;
            case 6: return RRule.SA;
            default: return RRule.MO;
          }
        });
      }

      // Add month days if specified
      if (this.recurrence.byMonthDay && this.recurrence.byMonthDay.length > 0) {
        options.bymonthday = this.recurrence.byMonthDay;
      }

      // Add months if specified
      if (this.recurrence.byMonth && this.recurrence.byMonth.length > 0) {
        options.bymonth = this.recurrence.byMonth;
      }

      // Add count if specified
      if (this.recurrence.count) {
        options.count = this.recurrence.count;
      }

      // Add until if specified
      if (this.recurrence.until) {
        options.until = new Date(this.recurrence.until);
      }

      // Create rule and get occurrences
      const rule = new RRule(options);
      return rule.between(new Date(), new Date(9999, 11, 31), true, limit);
    } catch (error) {
      console.error('Error calculating future occurrences:', error);
      return [];
    }
  }

  /**
   * Get a human-readable description of the recurrence pattern
   * @returns A string describing the recurrence pattern
   */
  getRecurrenceDescription(): string {
    if (!this.recurrence) {
      return 'One-time event';
    }

    let description = '';

    switch (this.recurrence.frequency) {
      case 'DAILY':
        description = this.recurrence.interval === 1 ? 'Daily' : `Every ${this.recurrence.interval} days`;
        break;
      case 'WEEKLY':
        description = this.recurrence.interval === 1 ? 'Weekly' : `Every ${this.recurrence.interval} weeks`;
        if (this.recurrence.byWeekday && this.recurrence.byWeekday.length > 0) {
          const days = this.recurrence.byWeekday
            .map(day => ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][day])
            .join(', ');
          description += ` on ${days}`;
        }
        break;
      case 'MONTHLY':
        description = this.recurrence.interval === 1 ? 'Monthly' : `Every ${this.recurrence.interval} months`;
        if (this.recurrence.byMonthDay && this.recurrence.byMonthDay.length > 0) {
          const days = this.recurrence.byMonthDay.map(day => day.toString()).join(', ');
          description += ` on day${this.recurrence.byMonthDay.length > 1 ? 's' : ''} ${days}`;
        }
        break;
      case 'YEARLY':
        description = this.recurrence.interval === 1 ? 'Yearly' : `Every ${this.recurrence.interval} years`;
        if (this.recurrence.byMonth && this.recurrence.byMonth.length > 0) {
          const monthNames = this.recurrence.byMonth
            .map(month => [
              'January', 'February', 'March', 'April', 'May', 'June',
              'July', 'August', 'September', 'October', 'November', 'December'
            ][month - 1])
            .join(', ');
          description += ` in ${monthNames}`;
        }
        break;
      default:
        return 'Recurring event';
    }

    if (this.recurrence.count) {
      description += `, ${this.recurrence.count} time${this.recurrence.count > 1 ? 's' : ''}`;
    } else if (this.recurrence.until) {
      description += `, until ${new Date(this.recurrence.until).toLocaleDateString()}`;
    }

    return description;
  }

  /**
   * Get a short excerpt of the description
   * @param length Maximum length of the excerpt
   * @returns A truncated version of the description
   */
  excerpt(length: number = 150): string {
    if (!this.desc) {
      return '';
    }

    // Remove HTML tags
    const textOnly = this.desc.replace(/<[^>]*>/g, '');

    if (textOnly.length <= length) {
      return textOnly;
    }

    // Truncate and add ellipsis
    return textOnly.substring(0, length) + '...';
  }

  /**
   * Get the place name and address for this happening
   * @returns An object with the place name and address
   */
  getPlaceInfo(): { name: string; address: string } | null {
    if (!this.placeDetails) {
      return null;
    }

    const address = this.placeDetails.fullLocation?.fullAddress || '';
    return { name: this.placeDetails.name, address };
  }

  /**
   * Ensure a URL has the proper protocol
   * @param url The URL to check
   * @returns The URL with proper protocol
   */
  private ensureProperUrl(url: string): string {
    if (!url) return url;

    // If it already has a protocol, return as is
    if (url.substring(0, 4) === 'http') {
      return url;
    }

    // Add // if needed
    if (url.substring(0, 2) !== '//') {
      url = '//' + url;
    }

    // Add https: protocol
    return 'https:' + url;
  }
}

/**
 * Factory function to create a Happening instance from a plain object
 * @param data The happening data
 * @returns A new Happening instance
 */
export function createHappening(data: HappeningInterface): Happening {
  return new Happening(data);
}

/**
 * Factory function to create multiple Happening instances from an array
 * @param dataArray Array of happening data
 * @returns Array of Happening instances
 */
export function createHappenings(dataArray: HappeningInterface[]): Happening[] {
  return dataArray.map(data => createHappening(data));
}
